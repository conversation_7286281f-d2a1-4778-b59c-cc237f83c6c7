-- Insert test users for HealthConnect application
-- Note: Passwords are BCrypt hashed for 'password123'

-- Test Doctor
INSERT INTO users (full_name, email, password, role, specialization, license_number, affiliation, years_of_experience, phone_number, address, is_active, created_at, updated_at) 
VALUES (
    'Dr. <PERSON>', 
    '<EMAIL>', 
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 
    'DOCTOR', 
    'Cardiology', 
    'MD123456', 
    'HealthConnect Medical Center', 
    10, 
    '******-0123', 
    '123 Medical Plaza, Healthcare City, HC 12345', 
    true, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
);

-- Test Patient
INSERT INTO users (full_name, email, password, role, phone_number, address, is_active, created_at, updated_at) 
VALUES (
    '<PERSON>', 
    '<EMAIL>', 
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 
    'PATIENT', 
    '******-0124', 
    '456 Patient Street, Wellness Town, WT 67890', 
    true, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
);

-- Additional Test Doctor for variety
INSERT INTO users (full_name, email, password, role, specialization, license_number, affiliation, years_of_experience, phone_number, address, is_active, created_at, updated_at) 
VALUES (
    'Dr. Sarah Johnson', 
    '<EMAIL>', 
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 
    'DOCTOR', 
    'Dermatology', 
    'MD789012', 
    'HealthConnect Skin Clinic', 
    8, 
    '******-0125', 
    '789 Dermatology Ave, Skin Care City, SC 11111', 
    true, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
);

-- Additional Test Patient
INSERT INTO users (full_name, email, password, role, phone_number, address, is_active, created_at, updated_at) 
VALUES (
    'Bob Wilson', 
    '<EMAIL>', 
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 
    'PATIENT', 
    '******-0126', 
    '321 Health Street, Wellness City, WC 22222', 
    true, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
);
