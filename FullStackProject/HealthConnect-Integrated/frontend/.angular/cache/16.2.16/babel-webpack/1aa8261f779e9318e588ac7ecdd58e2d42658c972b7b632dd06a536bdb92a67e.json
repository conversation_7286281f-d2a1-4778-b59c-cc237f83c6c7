{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/auth.service\";\nimport * as i2 from \"../../core/services/user.service\";\nimport * as i3 from \"../../core/services/appointment.service\";\nimport * as i4 from \"../../core/services/chat.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../../shared/components/quick-chat-widget/quick-chat-widget.component\";\nimport * as i8 from \"../../chat/chat-list/chat-list.component\";\nimport * as i9 from \"../../chat/chat-window/chat-window.component\";\nconst _c0 = [\"chatWindow\"];\nfunction PatientDashboardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"span\", 19);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 20);\n    i0.ɵɵtext(5, \"Loading your dashboard...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PatientDashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"i\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction PatientDashboardComponent_div_3_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 47)(2, \"div\", 58);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementStart(4, \"h6\", 59);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h4\", 60);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementStart(8, \"small\", 61);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"titlecase\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const metric_r12 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", metric_r12.icon, \" display-6 \", metric_r12.color, \" mb-2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(metric_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", metric_r12.value, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(metric_r12.unit);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r4.getStatusBadgeClass(metric_r12.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 10, metric_r12.status));\n  }\n}\nfunction PatientDashboardComponent_div_3_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_div_23_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const action_r13 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.navigateTo(action_r13.route));\n    });\n    i0.ɵɵelementStart(2, \"div\", 58)(3, \"div\", 63);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h6\", 59);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 64);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const action_r13 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(action_r13.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMapInterpolate1(\"bi bi-\", action_r13.icon, \" text-white fs-4\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r13.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r13.description);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"i\", 66);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No upcoming appointments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_div_34_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.navigateTo(\"/appointments/book\"));\n    });\n    i0.ɵɵelement(5, \"i\", 68);\n    i0.ɵɵtext(6, \"Book Appointment \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PatientDashboardComponent_div_3_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70)(2, \"div\", 71)(3, \"div\", 72);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h6\", 73);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 74);\n    i0.ɵɵelement(9, \"i\", 75);\n    i0.ɵɵtext(10);\n    i0.ɵɵelement(11, \"i\", 76);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 77)(17, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_div_35_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const appointment_r18 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.navigateTo(\"/appointments/\" + appointment_r18.id));\n    });\n    i0.ɵɵelement(18, \"i\", 78);\n    i0.ɵɵtext(19, \"View Details \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const appointment_r18 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate1(\"bi bi-\", appointment_r18.type === \"VIDEO_CALL\" ? \"camera-video\" : \"geo-alt\", \" text-primary\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Dr. \", appointment_r18.doctor.fullName, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", appointment_r18.date, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", appointment_r18.startTime, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r7.getStatusBadgeClass(appointment_r18.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 9, appointment_r18.status), \" \");\n  }\n}\nfunction PatientDashboardComponent_div_3_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"i\", 79);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No messages yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_div_47_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.openChatModal());\n    });\n    i0.ɵɵelement(5, \"i\", 80);\n    i0.ɵɵtext(6, \"Start Conversation \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PatientDashboardComponent_div_3_div_48_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind3(2, 2, chat_r23.lastMessage.content, 0, 50), \"\", chat_r23.lastMessage.content.length > 50 ? \"...\" : \"\", \" \");\n  }\n}\nfunction PatientDashboardComponent_div_3_div_48_small_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.formatChatTime(chat_r23.lastMessage.createdAt), \" \");\n  }\n}\nfunction PatientDashboardComponent_div_3_div_48_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 87);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", chat_r23.unreadCount, \" \");\n  }\n}\nfunction PatientDashboardComponent_div_3_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 70)(2, \"div\", 71);\n    i0.ɵɵelement(3, \"img\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\")(5, \"h6\", 73);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, PatientDashboardComponent_div_3_div_48_p_7_Template, 3, 6, \"p\", 83);\n    i0.ɵɵtemplate(8, PatientDashboardComponent_div_3_div_48_small_8_Template, 2, 1, \"small\", 84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 77);\n    i0.ɵɵtemplate(10, PatientDashboardComponent_div_3_div_48_span_10_Template, 2, 1, \"span\", 85);\n    i0.ɵɵelementStart(11, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_div_48_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r31);\n      const chat_r23 = restoredCtx.$implicit;\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.openChat(chat_r23));\n    });\n    i0.ɵɵelement(12, \"i\", 86);\n    i0.ɵɵtext(13, \"Open \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const chat_r23 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", chat_r23.doctor.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", chat_r23.doctor.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Dr. \", chat_r23.doctor.fullName, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chat_r23.lastMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chat_r23.lastMessage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", chat_r23.unreadCount > 0);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_59_hr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\", 92);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 71)(2, \"div\", 89);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 90)(5, \"h6\", 73);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 74);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"small\", 61);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, PatientDashboardComponent_div_3_div_59_hr_11_Template, 1, 0, \"hr\", 91);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const activity_r32 = ctx.$implicit;\n    const isLast_r33 = ctx.last;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", activity_r32.icon, \" \", activity_r32.color, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r32.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r32.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r32.time);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !isLast_r33);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_67_hr_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\", 92);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"div\", 71)(2, \"div\", 94);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 90)(5, \"h6\", 73);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 95);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, PatientDashboardComponent_div_3_div_67_hr_9_Template, 1, 0, \"hr\", 91);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tip_r35 = ctx.$implicit;\n    const isLast_r36 = ctx.last;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate1(\"bi bi-\", tip_r35.icon, \" text-primary\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tip_r35.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tip_r35.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !isLast_r36);\n  }\n}\nfunction PatientDashboardComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 23)(2, \"div\", 24)(3, \"div\", 25)(4, \"div\")(5, \"h1\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 27);\n    i0.ɵɵtext(8, \"Here's your health overview for today\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.refreshData());\n    });\n    i0.ɵɵelement(10, \"i\", 29);\n    i0.ɵɵtext(11, \"Refresh \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"div\", 23)(13, \"div\", 24)(14, \"h5\", 30);\n    i0.ɵɵelement(15, \"i\", 31);\n    i0.ɵɵtext(16, \"Health Metrics \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, PatientDashboardComponent_div_3_div_17_Template, 13, 12, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 23)(19, \"div\", 24)(20, \"h5\", 30);\n    i0.ɵɵelement(21, \"i\", 33);\n    i0.ɵɵtext(22, \"Quick Actions \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, PatientDashboardComponent_div_3_div_23_Template, 9, 7, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 23)(25, \"div\", 24)(26, \"div\", 34)(27, \"div\", 35)(28, \"h6\", 36);\n    i0.ɵɵelement(29, \"i\", 37);\n    i0.ɵɵtext(30, \"Upcoming Appointments \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.navigateTo(\"/appointments\"));\n    });\n    i0.ɵɵtext(32, \" View All \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 39);\n    i0.ɵɵtemplate(34, PatientDashboardComponent_div_3_div_34_Template, 7, 0, \"div\", 40);\n    i0.ɵɵtemplate(35, PatientDashboardComponent_div_3_div_35_Template, 20, 11, \"div\", 41);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"div\", 23)(37, \"div\", 24)(38, \"div\", 34)(39, \"div\", 35)(40, \"h6\", 36);\n    i0.ɵɵelement(41, \"i\", 9);\n    i0.ɵɵtext(42, \"Recent Messages \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.openChatModal());\n    });\n    i0.ɵɵelement(44, \"i\", 42);\n    i0.ɵɵtext(45, \"New Message \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 39);\n    i0.ɵɵtemplate(47, PatientDashboardComponent_div_3_div_47_Template, 7, 0, \"div\", 40);\n    i0.ɵɵtemplate(48, PatientDashboardComponent_div_3_div_48_Template, 14, 6, \"div\", 43);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 44)(50, \"div\", 45);\n    i0.ɵɵelement(51, \"app-quick-chat-widget\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 46)(53, \"div\", 47)(54, \"div\", 48)(55, \"h6\", 36);\n    i0.ɵɵelement(56, \"i\", 49);\n    i0.ɵɵtext(57, \"Recent Activities \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 39);\n    i0.ɵɵtemplate(59, PatientDashboardComponent_div_3_div_59_Template, 12, 8, \"div\", 50);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"div\", 51)(61, \"div\", 47)(62, \"div\", 48)(63, \"h6\", 36);\n    i0.ɵɵelement(64, \"i\", 52);\n    i0.ɵɵtext(65, \"Health Tips \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"div\", 39);\n    i0.ɵɵtemplate(67, PatientDashboardComponent_div_3_div_67_Template, 10, 6, \"div\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(68, \"div\", 44)(69, \"div\", 24)(70, \"div\", 54);\n    i0.ɵɵelement(71, \"i\", 55);\n    i0.ɵɵelementStart(72, \"div\")(73, \"h6\", 56);\n    i0.ɵɵtext(74, \"Emergency Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"p\", 36);\n    i0.ɵɵtext(76, \"For medical emergencies, call \");\n    i0.ɵɵelementStart(77, \"strong\");\n    i0.ɵɵtext(78, \"911\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(79, \" or visit your nearest emergency room.\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.getGreeting(), \", \", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.fullName, \"!\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.healthMetrics);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.quickActions);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.upcomingAppointments.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.upcomingAppointments);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.recentChats.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentChats);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentActivities);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.healthTips);\n  }\n}\nexport class PatientDashboardComponent {\n  constructor(authService, userService, appointmentService, chatService, router) {\n    this.authService = authService;\n    this.userService = userService;\n    this.appointmentService = appointmentService;\n    this.chatService = chatService;\n    this.router = router;\n    this.currentUser = null;\n    this.isLoading = true;\n    this.error = '';\n    this.appointments = [];\n    this.upcomingAppointments = [];\n    this.recentChats = [];\n    this.healthMetrics = [{\n      name: 'Heart Rate',\n      value: '72',\n      unit: 'bpm',\n      status: 'normal',\n      icon: 'heart-pulse',\n      color: 'text-success'\n    }, {\n      name: 'Blood Pressure',\n      value: '120/80',\n      unit: 'mmHg',\n      status: 'normal',\n      icon: 'activity',\n      color: 'text-success'\n    }, {\n      name: 'Weight',\n      value: '70',\n      unit: 'kg',\n      status: 'normal',\n      icon: 'speedometer2',\n      color: 'text-info'\n    }, {\n      name: 'Temperature',\n      value: '98.6',\n      unit: '°F',\n      status: 'normal',\n      icon: 'thermometer-half',\n      color: 'text-success'\n    }];\n    this.quickActions = [{\n      title: 'Book Appointment',\n      description: 'Schedule a consultation with a doctor',\n      icon: 'calendar-plus',\n      color: 'bg-primary',\n      route: '/appointments/book'\n    }, {\n      title: 'Find Doctors',\n      description: 'Browse available healthcare providers',\n      icon: 'search',\n      color: 'bg-info',\n      route: '/appointments/doctors'\n    }, {\n      title: 'Health Assistant',\n      description: 'Get AI-powered health guidance',\n      icon: 'robot',\n      color: 'bg-success',\n      route: '/health-bot'\n    }, {\n      title: 'Messages',\n      description: 'Chat with your healthcare providers',\n      icon: 'chat-dots',\n      color: 'bg-warning',\n      route: '/chat'\n    }];\n    this.recentActivities = [{\n      title: 'Appointment Scheduled',\n      description: 'Consultation with Dr. Smith on Dec 15, 2024',\n      time: '2 hours ago',\n      icon: 'calendar-check',\n      color: 'text-primary'\n    }, {\n      title: 'Health Metrics Updated',\n      description: 'Blood pressure and weight recorded',\n      time: '1 day ago',\n      icon: 'graph-up',\n      color: 'text-success'\n    }, {\n      title: 'Message Received',\n      description: 'New message from Dr. Johnson',\n      time: '2 days ago',\n      icon: 'envelope',\n      color: 'text-info'\n    }];\n    this.healthTips = [{\n      title: 'Stay Hydrated',\n      description: 'Drink at least 8 glasses of water daily for optimal health.',\n      icon: 'droplet'\n    }, {\n      title: 'Regular Exercise',\n      description: 'Aim for 30 minutes of moderate exercise 5 days a week.',\n      icon: 'bicycle'\n    }, {\n      title: 'Healthy Sleep',\n      description: 'Get 7-9 hours of quality sleep each night.',\n      icon: 'moon'\n    }];\n  }\n  ngOnInit() {\n    this.loadUserData();\n    this.loadAppointments();\n    this.loadRecentChats();\n  }\n  loadUserData() {\n    this.authService.currentUser$.subscribe({\n      next: user => {\n        this.currentUser = user;\n        this.isLoading = false;\n      },\n      error: error => {\n        this.error = 'Failed to load user data';\n        this.isLoading = false;\n      }\n    });\n  }\n  loadAppointments() {\n    this.appointmentService.getPatientAppointments().subscribe({\n      next: appointments => {\n        this.appointments = appointments;\n        this.upcomingAppointments = appointments.filter(apt => new Date(apt.date) >= new Date()).slice(0, 3); // Show only next 3 appointments\n        this.updateRecentActivities();\n      },\n      error: error => {\n        console.error('Failed to load appointments:', error);\n      }\n    });\n  }\n  updateRecentActivities() {\n    // Update recent activities with real appointment data\n    const recentAppointments = this.appointments.filter(apt => apt.status === 'SCHEDULED' || apt.status === 'CONFIRMED').slice(0, 2);\n    this.recentActivities = [...recentAppointments.map(apt => ({\n      title: 'Appointment Scheduled',\n      description: `Consultation with Dr. ${apt.doctor?.fullName} on ${apt.date}`,\n      time: this.getTimeAgo(apt.createdAt),\n      icon: 'calendar-check',\n      color: 'text-primary'\n    })), ...this.recentActivities.slice(recentAppointments.length)];\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const appointmentDate = new Date(date);\n    const diffInHours = Math.floor((now.getTime() - appointmentDate.getTime()) / (1000 * 60 * 60));\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays} days ago`;\n  }\n  navigateTo(route) {\n    this.router.navigate([route]);\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good morning';\n    if (hour < 18) return 'Good afternoon';\n    return 'Good evening';\n  }\n  getStatusBadgeClass(status) {\n    switch (status) {\n      case 'normal':\n        return 'badge bg-success';\n      case 'warning':\n        return 'badge bg-warning';\n      case 'danger':\n        return 'badge bg-danger';\n      default:\n        return 'badge bg-secondary';\n    }\n  }\n  refreshData() {\n    this.isLoading = true;\n    this.loadAppointments();\n    this.loadRecentChats();\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 1000);\n  }\n  loadRecentChats() {\n    this.chatService.getUserChats().subscribe({\n      next: chats => {\n        this.recentChats = chats.slice(0, 3); // Show only recent 3 chats\n      },\n\n      error: error => {\n        console.error('Failed to load chats:', error);\n      }\n    });\n  }\n  openChatModal() {\n    const modalElement = document.getElementById('chatModal');\n    if (modalElement) {\n      const modal = new window.bootstrap.Modal(modalElement);\n      modal.show();\n    }\n  }\n  openChat(chat) {\n    this.openChatModal();\n    // Wait for modal to open, then load chat\n    setTimeout(() => {\n      this.onChatSelected(chat);\n    }, 300);\n  }\n  onChatSelected(chat) {\n    if (this.chatWindow) {\n      this.chatWindow.loadChat(chat);\n    }\n  }\n  formatChatTime(dateString) {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n    if (diffInHours < 1) {\n      return 'Just now';\n    } else if (diffInHours < 24) {\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else {\n      return date.toLocaleDateString();\n    }\n  }\n  static {\n    this.ɵfac = function PatientDashboardComponent_Factory(t) {\n      return new (t || PatientDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AppointmentService), i0.ɵɵdirectiveInject(i4.ChatService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PatientDashboardComponent,\n      selectors: [[\"app-patient-dashboard\"]],\n      viewQuery: function PatientDashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatWindow = _t.first);\n        }\n      },\n      decls: 19,\n      vars: 3,\n      consts: [[1, \"container-fluid\", \"py-4\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [\"id\", \"chatModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"chatModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-xl\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"chatModalLabel\", 1, \"modal-title\"], [1, \"bi\", \"bi-chat-dots\", \"me-2\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"modal-body\", \"p-0\", 2, \"height\", \"600px\"], [1, \"row\", \"h-100\", \"g-0\"], [1, \"col-md-4\", \"border-end\"], [3, \"chatSelected\"], [1, \"col-md-8\"], [\"chatWindow\", \"\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-3\", \"text-muted\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-2\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"h3\", \"mb-1\"], [1, \"text-muted\", \"mb-0\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-2\"], [1, \"mb-3\"], [1, \"bi\", \"bi-heart-pulse\", \"me-2\", \"text-primary\"], [\"class\", \"col-md-3 col-sm-6 mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-lightning\", \"me-2\", \"text-primary\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"bi\", \"bi-calendar-check\", \"me-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"card-body\"], [\"class\", \"text-center py-4 text-muted\", 4, \"ngIf\"], [\"class\", \"appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded border\", 4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-chat-plus\", \"me-1\"], [\"class\", \"chat-preview d-flex align-items-center justify-content-between p-3 mb-2 rounded border\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\"], [1, \"col-lg-4\", \"mb-4\"], [1, \"col-md-4\", \"mb-4\"], [1, \"card\", \"h-100\"], [1, \"card-header\"], [1, \"bi\", \"bi-clock-history\", \"me-2\"], [\"class\", \"activity-item d-flex align-items-start mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-6\", \"mb-4\"], [1, \"bi\", \"bi-lightbulb\", \"me-2\"], [\"class\", \"tip-item d-flex align-items-start mb-3\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-info\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-info-circle\", \"me-3\", \"fs-4\"], [1, \"alert-heading\", \"mb-1\"], [1, \"col-md-3\", \"col-sm-6\", \"mb-3\"], [1, \"card-body\", \"text-center\"], [1, \"card-title\"], [1, \"mb-2\"], [1, \"text-muted\"], [1, \"card\", \"h-100\", \"action-card\", 3, \"click\"], [1, \"rounded-circle\", \"d-inline-flex\", \"align-items-center\", \"justify-content-center\", \"mb-3\", 2, \"width\", \"60px\", \"height\", \"60px\"], [1, \"card-text\", \"text-muted\", \"small\"], [1, \"text-center\", \"py-4\", \"text-muted\"], [1, \"bi\", \"bi-calendar-x\", \"display-6\", \"mb-3\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-calendar-plus\", \"me-2\"], [1, \"appointment-item\", \"d-flex\", \"align-items-center\", \"justify-content-between\", \"p-3\", \"mb-2\", \"rounded\", \"border\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-shrink-0\", \"me-3\"], [1, \"rounded-circle\", \"bg-light\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"45px\", \"height\", \"45px\"], [1, \"mb-1\"], [1, \"mb-1\", \"text-muted\", \"small\"], [1, \"bi\", \"bi-calendar\", \"me-1\"], [1, \"bi\", \"bi-clock\", \"ms-2\", \"me-1\"], [1, \"flex-shrink-0\"], [1, \"bi\", \"bi-eye\", \"me-1\"], [1, \"bi\", \"bi-chat-square-text\", \"display-6\", \"mb-3\"], [1, \"bi\", \"bi-chat-plus\", \"me-2\"], [1, \"chat-preview\", \"d-flex\", \"align-items-center\", \"justify-content-between\", \"p-3\", \"mb-2\", \"rounded\", \"border\"], [1, \"rounded-circle\", 2, \"width\", \"45px\", \"height\", \"45px\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [\"class\", \"mb-1 text-muted small\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"class\", \"badge bg-primary rounded-pill me-2\", 4, \"ngIf\"], [1, \"bi\", \"bi-chat\", \"me-1\"], [1, \"badge\", \"bg-primary\", \"rounded-pill\", \"me-2\"], [1, \"activity-item\", \"d-flex\", \"align-items-start\", \"mb-3\"], [1, \"rounded-circle\", \"bg-light\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"40px\", \"height\", \"40px\"], [1, \"flex-grow-1\"], [\"class\", \"my-3\", 4, \"ngIf\"], [1, \"my-3\"], [1, \"tip-item\", \"d-flex\", \"align-items-start\", \"mb-3\"], [1, \"rounded-circle\", \"bg-primary\", \"bg-opacity-10\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"40px\", \"height\", \"40px\"], [1, \"mb-0\", \"text-muted\", \"small\"]],\n      template: function PatientDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, PatientDashboardComponent_div_1_Template, 6, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, PatientDashboardComponent_div_2_Template, 3, 1, \"div\", 2);\n          i0.ɵɵtemplate(3, PatientDashboardComponent_div_3_Template, 80, 10, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h5\", 8);\n          i0.ɵɵelement(9, \"i\", 9);\n          i0.ɵɵtext(10, \"Messages \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"button\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12)(14, \"div\", 13)(15, \"app-chat-list\", 14);\n          i0.ɵɵlistener(\"chatSelected\", function PatientDashboardComponent_Template_app_chat_list_chatSelected_15_listener($event) {\n            return ctx.onChatSelected($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 15);\n          i0.ɵɵelement(17, \"app-chat-window\", null, 16);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i7.QuickChatWidgetComponent, i8.ChatListComponent, i9.ChatWindowComponent, i6.SlicePipe, i6.TitleCasePipe],\n      styles: [\".action-card[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.action-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border-bottom: 1px solid #e9ecef;\\n  font-weight: 600;\\n}\\n\\n.activity-item[_ngcontent-%COMP%], .tip-item[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #f8f9fa;\\n  padding-bottom: 1rem;\\n}\\n\\n.activity-item[_ngcontent-%COMP%]:last-child, .tip-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  padding-bottom: 0;\\n}\\n\\n.bg-primary[_ngcontent-%COMP%] {\\n  background-color: #0d6efd !important;\\n}\\n\\n.bg-info[_ngcontent-%COMP%] {\\n  background-color: #0dcaf0 !important;\\n}\\n\\n.bg-success[_ngcontent-%COMP%] {\\n  background-color: #198754 !important;\\n}\\n\\n.bg-warning[_ngcontent-%COMP%] {\\n  background-color: #ffc107 !important;\\n}\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #0d6efd !important;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .container-fluid[_ngcontent-%COMP%] {\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .display-6[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵclassMapInterpolate2", "metric_r12", "icon", "color", "ɵɵtextInterpolate", "name", "value", "unit", "ɵɵclassMap", "ctx_r4", "getStatusBadgeClass", "status", "ɵɵpipeBind1", "ɵɵlistener", "PatientDashboardComponent_div_3_div_23_Template_div_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r15", "action_r13", "$implicit", "ctx_r14", "ɵɵnextContext", "ɵɵresetView", "navigateTo", "route", "ɵɵclassMapInterpolate1", "title", "description", "PatientDashboardComponent_div_3_div_34_Template_button_click_4_listener", "_r17", "ctx_r16", "PatientDashboardComponent_div_3_div_35_Template_button_click_17_listener", "_r20", "appointment_r18", "ctx_r19", "id", "type", "doctor", "fullName", "date", "startTime", "ctx_r7", "PatientDashboardComponent_div_3_div_47_Template_button_click_4_listener", "_r22", "ctx_r21", "openChatModal", "ɵɵtextInterpolate2", "ɵɵpipeBind3", "chat_r23", "lastMessage", "content", "length", "ctx_r25", "formatChatTime", "createdAt", "unreadCount", "ɵɵtemplate", "PatientDashboardComponent_div_3_div_48_p_7_Template", "PatientDashboardComponent_div_3_div_48_small_8_Template", "PatientDashboardComponent_div_3_div_48_span_10_Template", "PatientDashboardComponent_div_3_div_48_Template_button_click_11_listener", "_r31", "ctx_r30", "openChat", "ɵɵproperty", "avatar", "ɵɵsanitizeUrl", "PatientDashboardComponent_div_3_div_59_hr_11_Template", "activity_r32", "time", "isLast_r33", "PatientDashboardComponent_div_3_div_67_hr_9_Template", "tip_r35", "isLast_r36", "PatientDashboardComponent_div_3_Template_button_click_9_listener", "_r39", "ctx_r38", "refreshData", "PatientDashboardComponent_div_3_div_17_Template", "PatientDashboardComponent_div_3_div_23_Template", "PatientDashboardComponent_div_3_Template_button_click_31_listener", "ctx_r40", "PatientDashboardComponent_div_3_div_34_Template", "PatientDashboardComponent_div_3_div_35_Template", "PatientDashboardComponent_div_3_Template_button_click_43_listener", "ctx_r41", "PatientDashboardComponent_div_3_div_47_Template", "PatientDashboardComponent_div_3_div_48_Template", "PatientDashboardComponent_div_3_div_59_Template", "PatientDashboardComponent_div_3_div_67_Template", "ctx_r2", "getGreeting", "currentUser", "healthMetrics", "quickActions", "upcomingAppointments", "recentChats", "recentActivities", "healthTips", "PatientDashboardComponent", "constructor", "authService", "userService", "appointmentService", "chatService", "router", "isLoading", "appointments", "ngOnInit", "loadUserData", "loadAppointments", "loadRecentChats", "currentUser$", "subscribe", "next", "user", "getPatientAppointments", "filter", "apt", "Date", "slice", "updateRecentActivities", "console", "recentAppointments", "map", "getTimeAgo", "now", "appointmentDate", "diffInHours", "Math", "floor", "getTime", "diffInDays", "navigate", "hour", "getHours", "setTimeout", "getUserChats", "chats", "modalElement", "document", "getElementById", "modal", "window", "bootstrap", "Modal", "show", "chat", "onChatSelected", "chatWindow", "loadChat", "dateString", "toLocaleTimeString", "minute", "toLocaleDateString", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "UserService", "i3", "AppointmentService", "i4", "ChatService", "i5", "Router", "selectors", "viewQuery", "PatientDashboardComponent_Query", "rf", "ctx", "PatientDashboardComponent_div_1_Template", "PatientDashboardComponent_div_2_Template", "PatientDashboardComponent_div_3_Template", "PatientDashboardComponent_Template_app_chat_list_chatSelected_15_listener", "$event"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/patient/dashboard/dashboard.component.ts", "/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/patient/dashboard/dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../core/services/auth.service';\nimport { UserService } from '../../core/services/user.service';\nimport { AppointmentService } from '../../core/services/appointment.service';\nimport { ChatService } from '../../core/services/chat.service';\nimport { User } from '../../core/models/user.model';\nimport { Appointment } from '../../core/models/appointment.model';\nimport { Chat } from '../../core/models/chat.model';\nimport { ChatWindowComponent } from '../../chat/chat-window/chat-window.component';\n\ninterface HealthMetric {\n  name: string;\n  value: string;\n  unit: string;\n  status: 'normal' | 'warning' | 'danger';\n  icon: string;\n  color: string;\n}\n\ninterface QuickAction {\n  title: string;\n  description: string;\n  icon: string;\n  color: string;\n  route: string;\n}\n\n@Component({\n  selector: 'app-patient-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class PatientDashboardComponent implements OnInit {\n  @ViewChild('chatWindow') chatWindow!: ChatWindowComponent;\n\n  currentUser: User | null = null;\n  isLoading = true;\n  error = '';\n  appointments: Appointment[] = [];\n  upcomingAppointments: Appointment[] = [];\n  recentChats: Chat[] = [];\n\n  healthMetrics: HealthMetric[] = [\n    {\n      name: 'Heart Rate',\n      value: '72',\n      unit: 'bpm',\n      status: 'normal',\n      icon: 'heart-pulse',\n      color: 'text-success'\n    },\n    {\n      name: 'Blood Pressure',\n      value: '120/80',\n      unit: 'mmHg',\n      status: 'normal',\n      icon: 'activity',\n      color: 'text-success'\n    },\n    {\n      name: 'Weight',\n      value: '70',\n      unit: 'kg',\n      status: 'normal',\n      icon: 'speedometer2',\n      color: 'text-info'\n    },\n    {\n      name: 'Temperature',\n      value: '98.6',\n      unit: '°F',\n      status: 'normal',\n      icon: 'thermometer-half',\n      color: 'text-success'\n    }\n  ];\n\n  quickActions: QuickAction[] = [\n    {\n      title: 'Book Appointment',\n      description: 'Schedule a consultation with a doctor',\n      icon: 'calendar-plus',\n      color: 'bg-primary',\n      route: '/appointments/book'\n    },\n    {\n      title: 'Find Doctors',\n      description: 'Browse available healthcare providers',\n      icon: 'search',\n      color: 'bg-info',\n      route: '/appointments/doctors'\n    },\n    {\n      title: 'Health Assistant',\n      description: 'Get AI-powered health guidance',\n      icon: 'robot',\n      color: 'bg-success',\n      route: '/health-bot'\n    },\n    {\n      title: 'Messages',\n      description: 'Chat with your healthcare providers',\n      icon: 'chat-dots',\n      color: 'bg-warning',\n      route: '/chat'\n    }\n  ];\n\n  recentActivities = [\n    {\n      title: 'Appointment Scheduled',\n      description: 'Consultation with Dr. Smith on Dec 15, 2024',\n      time: '2 hours ago',\n      icon: 'calendar-check',\n      color: 'text-primary'\n    },\n    {\n      title: 'Health Metrics Updated',\n      description: 'Blood pressure and weight recorded',\n      time: '1 day ago',\n      icon: 'graph-up',\n      color: 'text-success'\n    },\n    {\n      title: 'Message Received',\n      description: 'New message from Dr. Johnson',\n      time: '2 days ago',\n      icon: 'envelope',\n      color: 'text-info'\n    }\n  ];\n\n  healthTips = [\n    {\n      title: 'Stay Hydrated',\n      description: 'Drink at least 8 glasses of water daily for optimal health.',\n      icon: 'droplet'\n    },\n    {\n      title: 'Regular Exercise',\n      description: 'Aim for 30 minutes of moderate exercise 5 days a week.',\n      icon: 'bicycle'\n    },\n    {\n      title: 'Healthy Sleep',\n      description: 'Get 7-9 hours of quality sleep each night.',\n      icon: 'moon'\n    }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private userService: UserService,\n    private appointmentService: AppointmentService,\n    private chatService: ChatService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadUserData();\n    this.loadAppointments();\n    this.loadRecentChats();\n  }\n\n  private loadUserData(): void {\n    this.authService.currentUser$.subscribe({\n      next: (user) => {\n        this.currentUser = user;\n        this.isLoading = false;\n      },\n      error: (error) => {\n        this.error = 'Failed to load user data';\n        this.isLoading = false;\n      }\n    });\n  }\n\n  private loadAppointments(): void {\n    this.appointmentService.getPatientAppointments().subscribe({\n      next: (appointments) => {\n        this.appointments = appointments;\n        this.upcomingAppointments = appointments\n          .filter(apt => new Date(apt.date) >= new Date())\n          .slice(0, 3); // Show only next 3 appointments\n        this.updateRecentActivities();\n      },\n      error: (error) => {\n        console.error('Failed to load appointments:', error);\n      }\n    });\n  }\n\n  private updateRecentActivities(): void {\n    // Update recent activities with real appointment data\n    const recentAppointments = this.appointments\n      .filter(apt => apt.status === 'SCHEDULED' || apt.status === 'CONFIRMED')\n      .slice(0, 2);\n\n    this.recentActivities = [\n      ...recentAppointments.map(apt => ({\n        title: 'Appointment Scheduled',\n        description: `Consultation with Dr. ${apt.doctor?.fullName} on ${apt.date}`,\n        time: this.getTimeAgo(apt.createdAt),\n        icon: 'calendar-check',\n        color: 'text-primary'\n      })),\n      ...this.recentActivities.slice(recentAppointments.length)\n    ];\n  }\n\n  private getTimeAgo(date: string): string {\n    const now = new Date();\n    const appointmentDate = new Date(date);\n    const diffInHours = Math.floor((now.getTime() - appointmentDate.getTime()) / (1000 * 60 * 60));\n\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays} days ago`;\n  }\n\n  navigateTo(route: string): void {\n    this.router.navigate([route]);\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good morning';\n    if (hour < 18) return 'Good afternoon';\n    return 'Good evening';\n  }\n\n  getStatusBadgeClass(status: string): string {\n    switch (status) {\n      case 'normal': return 'badge bg-success';\n      case 'warning': return 'badge bg-warning';\n      case 'danger': return 'badge bg-danger';\n      default: return 'badge bg-secondary';\n    }\n  }\n\n  refreshData(): void {\n    this.isLoading = true;\n    this.loadAppointments();\n    this.loadRecentChats();\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 1000);\n  }\n\n  private loadRecentChats(): void {\n    this.chatService.getUserChats().subscribe({\n      next: (chats) => {\n        this.recentChats = chats.slice(0, 3); // Show only recent 3 chats\n      },\n      error: (error) => {\n        console.error('Failed to load chats:', error);\n      }\n    });\n  }\n\n  openChatModal(): void {\n    const modalElement = document.getElementById('chatModal');\n    if (modalElement) {\n      const modal = new (window as any).bootstrap.Modal(modalElement);\n      modal.show();\n    }\n  }\n\n  openChat(chat: Chat): void {\n    this.openChatModal();\n    // Wait for modal to open, then load chat\n    setTimeout(() => {\n      this.onChatSelected(chat);\n    }, 300);\n  }\n\n  onChatSelected(chat: Chat): void {\n    if (this.chatWindow) {\n      this.chatWindow.loadChat(chat);\n    }\n  }\n\n  formatChatTime(dateString: string): string {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n\n    if (diffInHours < 1) {\n      return 'Just now';\n    } else if (diffInHours < 24) {\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n    } else {\n      return date.toLocaleDateString();\n    }\n  }\n}\n", "<div class=\"container-fluid py-4\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"text-center py-5\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <p class=\"mt-3 text-muted\">Loading your dashboard...</p>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"alert alert-danger\" role=\"alert\">\n    <i class=\"bi bi-exclamation-triangle me-2\"></i>\n    {{ error }}\n  </div>\n\n  <!-- Dashboard Content -->\n  <div *ngIf=\"!isLoading && !error\">\n    <!-- Welcome Header -->\n    <div class=\"row mb-4\">\n      <div class=\"col-12\">\n        <div class=\"d-flex justify-content-between align-items-center\">\n          <div>\n            <h1 class=\"h3 mb-1\">{{ getGreeting() }}, {{ currentUser?.fullName }}!</h1>\n            <p class=\"text-muted mb-0\">Here's your health overview for today</p>\n          </div>\n          <button class=\"btn btn-outline-primary\" (click)=\"refreshData()\">\n            <i class=\"bi bi-arrow-clockwise me-2\"></i>Refresh\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Health Metrics -->\n    <div class=\"row mb-4\">\n      <div class=\"col-12\">\n        <h5 class=\"mb-3\">\n          <i class=\"bi bi-heart-pulse me-2 text-primary\"></i>Health Metrics\n        </h5>\n      </div>\n      <div class=\"col-md-3 col-sm-6 mb-3\" *ngFor=\"let metric of healthMetrics\">\n        <div class=\"card h-100\">\n          <div class=\"card-body text-center\">\n            <i class=\"bi bi-{{ metric.icon }} display-6 {{ metric.color }} mb-2\"></i>\n            <h6 class=\"card-title\">{{ metric.name }}</h6>\n            <h4 class=\"mb-2\">{{ metric.value }} <small class=\"text-muted\">{{ metric.unit }}</small></h4>\n            <span [class]=\"getStatusBadgeClass(metric.status)\">{{ metric.status | titlecase }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"row mb-4\">\n      <div class=\"col-12\">\n        <h5 class=\"mb-3\">\n          <i class=\"bi bi-lightning me-2 text-primary\"></i>Quick Actions\n        </h5>\n      </div>\n      <div class=\"col-md-3 col-sm-6 mb-3\" *ngFor=\"let action of quickActions\">\n        <div class=\"card h-100 action-card\" (click)=\"navigateTo(action.route)\">\n          <div class=\"card-body text-center\">\n            <div class=\"rounded-circle d-inline-flex align-items-center justify-content-center mb-3\" \n                 [class]=\"action.color\" style=\"width: 60px; height: 60px;\">\n              <i class=\"bi bi-{{ action.icon }} text-white fs-4\"></i>\n            </div>\n            <h6 class=\"card-title\">{{ action.title }}</h6>\n            <p class=\"card-text text-muted small\">{{ action.description }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Upcoming Appointments -->\n    <div class=\"row mb-4\">\n      <div class=\"col-12\">\n        <div class=\"card\">\n          <div class=\"card-header d-flex justify-content-between align-items-center\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-calendar-check me-2\"></i>Upcoming Appointments\n            </h6>\n            <button class=\"btn btn-sm btn-outline-primary\" (click)=\"navigateTo('/appointments')\">\n              View All\n            </button>\n          </div>\n          <div class=\"card-body\">\n            <div *ngIf=\"upcomingAppointments.length === 0\" class=\"text-center py-4 text-muted\">\n              <i class=\"bi bi-calendar-x display-6 mb-3\"></i>\n              <p>No upcoming appointments</p>\n              <button class=\"btn btn-primary\" (click)=\"navigateTo('/appointments/book')\">\n                <i class=\"bi bi-calendar-plus me-2\"></i>Book Appointment\n              </button>\n            </div>\n\n            <div *ngFor=\"let appointment of upcomingAppointments\" class=\"appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded border\">\n              <div class=\"d-flex align-items-center\">\n                <div class=\"flex-shrink-0 me-3\">\n                  <div class=\"rounded-circle bg-light d-flex align-items-center justify-content-center\"\n                       style=\"width: 45px; height: 45px;\">\n                    <i class=\"bi bi-{{ appointment.type === 'VIDEO_CALL' ? 'camera-video' : 'geo-alt' }} text-primary\"></i>\n                  </div>\n                </div>\n                <div>\n                  <h6 class=\"mb-1\">Dr. {{ appointment.doctor.fullName }}</h6>\n                  <p class=\"mb-1 text-muted small\">\n                    <i class=\"bi bi-calendar me-1\"></i>{{ appointment.date }}\n                    <i class=\"bi bi-clock ms-2 me-1\"></i>{{ appointment.startTime }}\n                  </p>\n                  <span [class]=\"getStatusBadgeClass(appointment.status)\">\n                    {{ appointment.status | titlecase }}\n                  </span>\n                </div>\n              </div>\n              <div class=\"flex-shrink-0\">\n                <button\n                  class=\"btn btn-sm btn-outline-primary\"\n                  (click)=\"navigateTo('/appointments/' + appointment.id)\"\n                >\n                  <i class=\"bi bi-eye me-1\"></i>View Details\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Messages Section -->\n    <div class=\"row mb-4\">\n      <div class=\"col-12\">\n        <div class=\"card\">\n          <div class=\"card-header d-flex justify-content-between align-items-center\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-chat-dots me-2\"></i>Recent Messages\n            </h6>\n            <button class=\"btn btn-sm btn-outline-primary\" (click)=\"openChatModal()\">\n              <i class=\"bi bi-chat-plus me-1\"></i>New Message\n            </button>\n          </div>\n          <div class=\"card-body\">\n            <div *ngIf=\"recentChats.length === 0\" class=\"text-center py-4 text-muted\">\n              <i class=\"bi bi-chat-square-text display-6 mb-3\"></i>\n              <p>No messages yet</p>\n              <button class=\"btn btn-primary\" (click)=\"openChatModal()\">\n                <i class=\"bi bi-chat-plus me-2\"></i>Start Conversation\n              </button>\n            </div>\n\n            <div *ngFor=\"let chat of recentChats\" class=\"chat-preview d-flex align-items-center justify-content-between p-3 mb-2 rounded border\">\n              <div class=\"d-flex align-items-center\">\n                <div class=\"flex-shrink-0 me-3\">\n                  <img\n                    [src]=\"chat.doctor.avatar || '/assets/images/default-avatar.png'\"\n                    [alt]=\"chat.doctor.fullName\"\n                    class=\"rounded-circle\"\n                    style=\"width: 45px; height: 45px; object-fit: cover;\">\n                </div>\n                <div>\n                  <h6 class=\"mb-1\">Dr. {{ chat.doctor.fullName }}</h6>\n                  <p class=\"mb-1 text-muted small\" *ngIf=\"chat.lastMessage\">\n                    {{ chat.lastMessage.content | slice:0:50 }}{{ chat.lastMessage.content.length > 50 ? '...' : '' }}\n                  </p>\n                  <small class=\"text-muted\" *ngIf=\"chat.lastMessage\">\n                    {{ formatChatTime(chat.lastMessage.createdAt) }}\n                  </small>\n                </div>\n              </div>\n              <div class=\"flex-shrink-0\">\n                <span *ngIf=\"chat.unreadCount > 0\" class=\"badge bg-primary rounded-pill me-2\">\n                  {{ chat.unreadCount }}\n                </span>\n                <button\n                  class=\"btn btn-sm btn-outline-primary\"\n                  (click)=\"openChat(chat)\"\n                >\n                  <i class=\"bi bi-chat me-1\"></i>Open\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Recent Activities & Health Tips -->\n    <div class=\"row\">\n      <!-- Quick Chat Widget -->\n      <div class=\"col-lg-4 mb-4\">\n        <app-quick-chat-widget></app-quick-chat-widget>\n      </div>\n\n      <!-- Recent Activities -->\n      <div class=\"col-md-4 mb-4\">\n        <div class=\"card h-100\">\n          <div class=\"card-header\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-clock-history me-2\"></i>Recent Activities\n            </h6>\n          </div>\n          <div class=\"card-body\">\n            <div class=\"activity-item d-flex align-items-start mb-3\" *ngFor=\"let activity of recentActivities; last as isLast\">\n              <div class=\"flex-shrink-0 me-3\">\n                <div class=\"rounded-circle bg-light d-flex align-items-center justify-content-center\" \n                     style=\"width: 40px; height: 40px;\">\n                  <i class=\"bi bi-{{ activity.icon }} {{ activity.color }}\"></i>\n                </div>\n              </div>\n              <div class=\"flex-grow-1\">\n                <h6 class=\"mb-1\">{{ activity.title }}</h6>\n                <p class=\"mb-1 text-muted small\">{{ activity.description }}</p>\n                <small class=\"text-muted\">{{ activity.time }}</small>\n              </div>\n              <hr *ngIf=\"!isLast\" class=\"my-3\">\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Health Tips -->\n      <div class=\"col-md-6 mb-4\">\n        <div class=\"card h-100\">\n          <div class=\"card-header\">\n            <h6 class=\"mb-0\">\n              <i class=\"bi bi-lightbulb me-2\"></i>Health Tips\n            </h6>\n          </div>\n          <div class=\"card-body\">\n            <div class=\"tip-item d-flex align-items-start mb-3\" *ngFor=\"let tip of healthTips; last as isLast\">\n              <div class=\"flex-shrink-0 me-3\">\n                <div class=\"rounded-circle bg-primary bg-opacity-10 d-flex align-items-center justify-content-center\" \n                     style=\"width: 40px; height: 40px;\">\n                  <i class=\"bi bi-{{ tip.icon }} text-primary\"></i>\n                </div>\n              </div>\n              <div class=\"flex-grow-1\">\n                <h6 class=\"mb-1\">{{ tip.title }}</h6>\n                <p class=\"mb-0 text-muted small\">{{ tip.description }}</p>\n              </div>\n              <hr *ngIf=\"!isLast\" class=\"my-3\">\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Emergency Contact -->\n    <div class=\"row\">\n      <div class=\"col-12\">\n        <div class=\"alert alert-info d-flex align-items-center\" role=\"alert\">\n          <i class=\"bi bi-info-circle me-3 fs-4\"></i>\n          <div>\n            <h6 class=\"alert-heading mb-1\">Emergency Contact</h6>\n            <p class=\"mb-0\">For medical emergencies, call <strong>911</strong> or visit your nearest emergency room.</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Chat Modal -->\n<div class=\"modal fade\" id=\"chatModal\" tabindex=\"-1\" aria-labelledby=\"chatModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-xl\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"chatModalLabel\">\n          <i class=\"bi bi-chat-dots me-2\"></i>Messages\n        </h5>\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n      </div>\n      <div class=\"modal-body p-0\" style=\"height: 600px;\">\n        <div class=\"row h-100 g-0\">\n          <div class=\"col-md-4 border-end\">\n            <app-chat-list (chatSelected)=\"onChatSelected($event)\"></app-chat-list>\n          </div>\n          <div class=\"col-md-8\">\n            <app-chat-window #chatWindow></app-chat-window>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;;ICEEA,EAAA,CAAAC,cAAA,cAAgD;IAEdD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAI1DH,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAI,SAAA,YAA+C;IAC/CJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IA0BIR,EAAA,CAAAC,cAAA,cAAyE;IAGnED,EAAA,CAAAI,SAAA,QAAyE;IACzEJ,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7CH,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvFH,EAAA,CAAAC,cAAA,YAAmD;IAAAD,EAAA,CAAAE,MAAA,IAA+B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHtFH,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAS,sBAAA,WAAAC,UAAA,CAAAC,IAAA,iBAAAD,UAAA,CAAAE,KAAA,UAAiE;IAC7CZ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAa,iBAAA,CAAAH,UAAA,CAAAI,IAAA,CAAiB;IACvBd,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,kBAAA,KAAAI,UAAA,CAAAK,KAAA,MAAmB;IAA0Bf,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAa,iBAAA,CAAAH,UAAA,CAAAM,IAAA,CAAiB;IACzEhB,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAiB,UAAA,CAAAC,MAAA,CAAAC,mBAAA,CAAAT,UAAA,CAAAU,MAAA,EAA4C;IAACpB,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAa,iBAAA,CAAAb,EAAA,CAAAqB,WAAA,SAAAX,UAAA,CAAAU,MAAA,EAA+B;;;;;;IAaxFpB,EAAA,CAAAC,cAAA,cAAwE;IAClCD,EAAA,CAAAsB,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAA7B,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAF,OAAA,CAAAG,UAAA,CAAAL,UAAA,CAAAM,KAAA,CAAwB;IAAA,EAAC;IACpEjC,EAAA,CAAAC,cAAA,cAAmC;IAG/BD,EAAA,CAAAI,SAAA,QAAuD;IACzDJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,YAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAJ7DH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAiB,UAAA,CAAAU,UAAA,CAAAf,KAAA,CAAsB;IACtBZ,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAkC,sBAAA,WAAAP,UAAA,CAAAhB,IAAA,qBAA+C;IAE7BX,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAa,iBAAA,CAAAc,UAAA,CAAAQ,KAAA,CAAkB;IACHnC,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAa,iBAAA,CAAAc,UAAA,CAAAS,WAAA,CAAwB;;;;;;IAmB9DpC,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAI,SAAA,YAA+C;IAC/CJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/BH,EAAA,CAAAC,cAAA,iBAA2E;IAA3CD,EAAA,CAAAsB,UAAA,mBAAAe,wEAAA;MAAArC,EAAA,CAAAyB,aAAA,CAAAa,IAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAQ,OAAA,CAAAP,UAAA,CAAW,oBAAoB,CAAC;IAAA,EAAC;IACxEhC,EAAA,CAAAI,SAAA,YAAwC;IAAAJ,EAAA,CAAAE,MAAA,wBAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGXH,EAAA,CAAAC,cAAA,cAAyJ;IAKjJD,EAAA,CAAAI,SAAA,QAAuG;IACzGJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,UAAK;IACcD,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAI,SAAA,YAAmC;IAAAJ,EAAA,CAAAE,MAAA,IACnC;IAAAF,EAAA,CAAAI,SAAA,aAAqC;IAAAJ,EAAA,CAAAE,MAAA,IACvC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,YAAwD;IACtDD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGXH,EAAA,CAAAC,cAAA,eAA2B;IAGvBD,EAAA,CAAAsB,UAAA,mBAAAkB,yEAAA;MAAA,MAAAhB,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAgB,IAAA;MAAA,MAAAC,eAAA,GAAAlB,WAAA,CAAAI,SAAA;MAAA,MAAAe,OAAA,GAAA3C,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAY,OAAA,CAAAX,UAAA,CAAW,gBAAgB,GAAAU,eAAA,CAAAE,EAAA,CAAkB;IAAA,EAAC;IAEvD5C,EAAA,CAAAI,SAAA,aAA8B;IAAAJ,EAAA,CAAAE,MAAA,qBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IApBFH,EAAA,CAAAK,SAAA,GAA+F;IAA/FL,EAAA,CAAAkC,sBAAA,WAAAQ,eAAA,CAAAG,IAAA,gEAA+F;IAInF7C,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,kBAAA,SAAAoC,eAAA,CAAAI,MAAA,CAAAC,QAAA,KAAqC;IAEjB/C,EAAA,CAAAK,SAAA,GACnC;IADmCL,EAAA,CAAAM,kBAAA,KAAAoC,eAAA,CAAAM,IAAA,MACnC;IAAqChD,EAAA,CAAAK,SAAA,GACvC;IADuCL,EAAA,CAAAM,kBAAA,KAAAoC,eAAA,CAAAO,SAAA,MACvC;IACMjD,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAiB,UAAA,CAAAiC,MAAA,CAAA/B,mBAAA,CAAAuB,eAAA,CAAAtB,MAAA,EAAiD;IACrDpB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAqB,WAAA,QAAAqB,eAAA,CAAAtB,MAAA,OACF;;;;;;IA8BNpB,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAI,SAAA,YAAqD;IACrDJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtBH,EAAA,CAAAC,cAAA,iBAA0D;IAA1BD,EAAA,CAAAsB,UAAA,mBAAA6B,wEAAA;MAAAnD,EAAA,CAAAyB,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAsB,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IACvDtD,EAAA,CAAAI,SAAA,YAAoC;IAAAJ,EAAA,CAAAE,MAAA,0BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAcLH,EAAA,CAAAC,cAAA,YAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAuD,kBAAA,MAAAvD,EAAA,CAAAwD,WAAA,OAAAC,QAAA,CAAAC,WAAA,CAAAC,OAAA,cAAAF,QAAA,CAAAC,WAAA,CAAAC,OAAA,CAAAC,MAAA,wBACF;;;;;IACA5D,EAAA,CAAAC,cAAA,gBAAmD;IACjDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IADNH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAuD,OAAA,CAAAC,cAAA,CAAAL,QAAA,CAAAC,WAAA,CAAAK,SAAA,OACF;;;;;IAIF/D,EAAA,CAAAC,cAAA,eAA8E;IAC5ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmD,QAAA,CAAAO,WAAA,MACF;;;;;;IAtBJhE,EAAA,CAAAC,cAAA,cAAqI;IAG/HD,EAAA,CAAAI,SAAA,cAIwD;IAC1DJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IACcD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAiE,UAAA,IAAAC,mDAAA,gBAEI;IACJlE,EAAA,CAAAiE,UAAA,IAAAE,uDAAA,oBAEQ;IACVnE,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAiE,UAAA,KAAAG,uDAAA,mBAEO;IACPpE,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAsB,UAAA,mBAAA+C,yEAAA;MAAA,MAAA7C,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAA6C,IAAA;MAAA,MAAAb,QAAA,GAAAjC,WAAA,CAAAI,SAAA;MAAA,MAAA2C,OAAA,GAAAvE,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAwC,OAAA,CAAAC,QAAA,CAAAf,QAAA,CAAc;IAAA,EAAC;IAExBzD,EAAA,CAAAI,SAAA,aAA+B;IAAAJ,EAAA,CAAAE,MAAA,aACjC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAxBLH,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAyE,UAAA,QAAAhB,QAAA,CAAAX,MAAA,CAAA4B,MAAA,yCAAA1E,EAAA,CAAA2E,aAAA,CAAiE,QAAAlB,QAAA,CAAAX,MAAA,CAAAC,QAAA;IAMlD/C,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,kBAAA,SAAAmD,QAAA,CAAAX,MAAA,CAAAC,QAAA,KAA8B;IACb/C,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAyE,UAAA,SAAAhB,QAAA,CAAAC,WAAA,CAAsB;IAG7B1D,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAyE,UAAA,SAAAhB,QAAA,CAAAC,WAAA,CAAsB;IAM5C1D,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAyE,UAAA,SAAAhB,QAAA,CAAAO,WAAA,KAA0B;;;;;IA4CnChE,EAAA,CAAAI,SAAA,aAAiC;;;;;IAZnCJ,EAAA,CAAAC,cAAA,cAAmH;IAI7GD,EAAA,CAAAI,SAAA,QAA8D;IAChEJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAAyB;IACND,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/DH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEvDH,EAAA,CAAAiE,UAAA,KAAAW,qDAAA,iBAAiC;IACnC5E,EAAA,CAAAG,YAAA,EAAM;;;;;IATGH,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAS,sBAAA,WAAAoE,YAAA,CAAAlE,IAAA,OAAAkE,YAAA,CAAAjE,KAAA,KAAsD;IAI1CZ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAa,iBAAA,CAAAgE,YAAA,CAAA1C,KAAA,CAAoB;IACJnC,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAa,iBAAA,CAAAgE,YAAA,CAAAzC,WAAA,CAA0B;IACjCpC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAa,iBAAA,CAAAgE,YAAA,CAAAC,IAAA,CAAmB;IAE1C9E,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAyE,UAAA,UAAAM,UAAA,CAAa;;;;;IA0BlB/E,EAAA,CAAAI,SAAA,aAAiC;;;;;IAXnCJ,EAAA,CAAAC,cAAA,cAAmG;IAI7FD,EAAA,CAAAI,SAAA,QAAiD;IACnDJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAAyB;IACND,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE5DH,EAAA,CAAAiE,UAAA,IAAAe,oDAAA,iBAAiC;IACnChF,EAAA,CAAAG,YAAA,EAAM;;;;;IARGH,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAkC,sBAAA,WAAA+C,OAAA,CAAAtE,IAAA,kBAAyC;IAI7BX,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAa,iBAAA,CAAAoE,OAAA,CAAA9C,KAAA,CAAe;IACCnC,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAa,iBAAA,CAAAoE,OAAA,CAAA7C,WAAA,CAAqB;IAEnDpC,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAyE,UAAA,UAAAS,UAAA,CAAa;;;;;;IA7N9BlF,EAAA,CAAAC,cAAA,UAAkC;IAMJD,EAAA,CAAAE,MAAA,GAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1EH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,4CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEtEH,EAAA,CAAAC,cAAA,iBAAgE;IAAxBD,EAAA,CAAAsB,UAAA,mBAAA6D,iEAAA;MAAAnF,EAAA,CAAAyB,aAAA,CAAA2D,IAAA;MAAA,MAAAC,OAAA,GAAArF,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAsD,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAC7DtF,EAAA,CAAAI,SAAA,aAA0C;IAAAJ,EAAA,CAAAE,MAAA,gBAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAC,cAAA,eAAsB;IAGhBD,EAAA,CAAAI,SAAA,aAAmD;IAAAJ,EAAA,CAAAE,MAAA,uBACrD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAiE,UAAA,KAAAsB,+CAAA,oBASM;IACRvF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAsB;IAGhBD,EAAA,CAAAI,SAAA,aAAiD;IAAAJ,EAAA,CAAAE,MAAA,sBACnD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAiE,UAAA,KAAAuB,+CAAA,kBAWM;IACRxF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAsB;IAKZD,EAAA,CAAAI,SAAA,aAAyC;IAAAJ,EAAA,CAAAE,MAAA,8BAC3C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,kBAAqF;IAAtCD,EAAA,CAAAsB,UAAA,mBAAAmE,kEAAA;MAAAzF,EAAA,CAAAyB,aAAA,CAAA2D,IAAA;MAAA,MAAAM,OAAA,GAAA1F,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAA2D,OAAA,CAAA1D,UAAA,CAAW,eAAe,CAAC;IAAA,EAAC;IAClFhC,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAiE,UAAA,KAAA0B,+CAAA,kBAMM;IAEN3F,EAAA,CAAAiE,UAAA,KAAA2B,+CAAA,oBA2BM;IACR5F,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAAsB;IAKZD,EAAA,CAAAI,SAAA,YAAoC;IAAAJ,EAAA,CAAAE,MAAA,wBACtC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,kBAAyE;IAA1BD,EAAA,CAAAsB,UAAA,mBAAAuE,kEAAA;MAAA7F,EAAA,CAAAyB,aAAA,CAAA2D,IAAA;MAAA,MAAAU,OAAA,GAAA9F,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAA+D,OAAA,CAAAxC,aAAA,EAAe;IAAA,EAAC;IACtEtD,EAAA,CAAAI,SAAA,aAAoC;IAAAJ,EAAA,CAAAE,MAAA,oBACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEXH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAiE,UAAA,KAAA8B,+CAAA,kBAMM;IAEN/F,EAAA,CAAAiE,UAAA,KAAA+B,+CAAA,mBA8BM;IACRhG,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAAiB;IAGbD,EAAA,CAAAI,SAAA,6BAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA2B;IAInBD,EAAA,CAAAI,SAAA,aAAwC;IAAAJ,EAAA,CAAAE,MAAA,0BAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAiE,UAAA,KAAAgC,+CAAA,mBAaM;IACRjG,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAA2B;IAInBD,EAAA,CAAAI,SAAA,aAAoC;IAAAJ,EAAA,CAAAE,MAAA,oBACtC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAiE,UAAA,KAAAiC,+CAAA,mBAYM;IACRlG,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAAiB;IAGXD,EAAA,CAAAI,SAAA,aAA2C;IAC3CJ,EAAA,CAAAC,cAAA,WAAK;IAC4BD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAE,MAAA,sCAA8B;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,8CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IArOxFH,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAuD,kBAAA,KAAA4C,MAAA,CAAAC,WAAA,UAAAD,MAAA,CAAAE,WAAA,kBAAAF,MAAA,CAAAE,WAAA,CAAAtD,QAAA,MAAiD;IAiBpB/C,EAAA,CAAAK,SAAA,IAAgB;IAAhBL,EAAA,CAAAyE,UAAA,YAAA0B,MAAA,CAAAG,aAAA,CAAgB;IAmBhBtG,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAyE,UAAA,YAAA0B,MAAA,CAAAI,YAAA,CAAe;IA2B1DvG,EAAA,CAAAK,SAAA,IAAuC;IAAvCL,EAAA,CAAAyE,UAAA,SAAA0B,MAAA,CAAAK,oBAAA,CAAA5C,MAAA,OAAuC;IAQhB5D,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAyE,UAAA,YAAA0B,MAAA,CAAAK,oBAAA,CAAuB;IA8C9CxG,EAAA,CAAAK,SAAA,IAA8B;IAA9BL,EAAA,CAAAyE,UAAA,SAAA0B,MAAA,CAAAM,WAAA,CAAA7C,MAAA,OAA8B;IAQd5D,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAyE,UAAA,YAAA0B,MAAA,CAAAM,WAAA,CAAc;IAoD0CzG,EAAA,CAAAK,SAAA,IAAqB;IAArBL,EAAA,CAAAyE,UAAA,YAAA0B,MAAA,CAAAO,gBAAA,CAAqB;IA2B/B1G,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAyE,UAAA,YAAA0B,MAAA,CAAAQ,UAAA,CAAe;;;ADjM/F,OAAM,MAAOC,yBAAyB;EAsHpCC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,kBAAsC,EACtCC,WAAwB,EACxBC,MAAc;IAJd,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAxHhB,KAAAb,WAAW,GAAgB,IAAI;IAC/B,KAAAc,SAAS,GAAG,IAAI;IAChB,KAAA3G,KAAK,GAAG,EAAE;IACV,KAAA4G,YAAY,GAAkB,EAAE;IAChC,KAAAZ,oBAAoB,GAAkB,EAAE;IACxC,KAAAC,WAAW,GAAW,EAAE;IAExB,KAAAH,aAAa,GAAmB,CAC9B;MACExF,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,KAAK;MACXI,MAAM,EAAE,QAAQ;MAChBT,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,MAAM;MACZI,MAAM,EAAE,QAAQ;MAChBT,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVI,MAAM,EAAE,QAAQ;MAChBT,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,IAAI;MACVI,MAAM,EAAE,QAAQ;MAChBT,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE;KACR,CACF;IAED,KAAA2F,YAAY,GAAkB,CAC5B;MACEpE,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,uCAAuC;MACpDzB,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,YAAY;MACnBqB,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE,uCAAuC;MACpDzB,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,SAAS;MAChBqB,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,gCAAgC;MAC7CzB,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,YAAY;MACnBqB,KAAK,EAAE;KACR,EACD;MACEE,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,qCAAqC;MAClDzB,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE,YAAY;MACnBqB,KAAK,EAAE;KACR,CACF;IAED,KAAAyE,gBAAgB,GAAG,CACjB;MACEvE,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,6CAA6C;MAC1D0C,IAAI,EAAE,aAAa;MACnBnE,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;KACR,EACD;MACEuB,KAAK,EAAE,wBAAwB;MAC/BC,WAAW,EAAE,oCAAoC;MACjD0C,IAAI,EAAE,WAAW;MACjBnE,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,EACD;MACEuB,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,8BAA8B;MAC3C0C,IAAI,EAAE,YAAY;MAClBnE,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,CACF;IAED,KAAA+F,UAAU,GAAG,CACX;MACExE,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE,6DAA6D;MAC1EzB,IAAI,EAAE;KACP,EACD;MACEwB,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,wDAAwD;MACrEzB,IAAI,EAAE;KACP,EACD;MACEwB,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE,4CAA4C;MACzDzB,IAAI,EAAE;KACP,CACF;EAQE;EAEH0G,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQF,YAAYA,CAAA;IAClB,IAAI,CAACR,WAAW,CAACW,YAAY,CAACC,SAAS,CAAC;MACtCC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACvB,WAAW,GAAGuB,IAAI;QACvB,IAAI,CAACT,SAAS,GAAG,KAAK;MACxB,CAAC;MACD3G,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,0BAA0B;QACvC,IAAI,CAAC2G,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQI,gBAAgBA,CAAA;IACtB,IAAI,CAACP,kBAAkB,CAACa,sBAAsB,EAAE,CAACH,SAAS,CAAC;MACzDC,IAAI,EAAGP,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,GAAGA,YAAY;QAChC,IAAI,CAACZ,oBAAoB,GAAGY,YAAY,CACrCU,MAAM,CAACC,GAAG,IAAI,IAAIC,IAAI,CAACD,GAAG,CAAC/E,IAAI,CAAC,IAAI,IAAIgF,IAAI,EAAE,CAAC,CAC/CC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,IAAI,CAACC,sBAAsB,EAAE;MAC/B,CAAC;MACD1H,KAAK,EAAGA,KAAK,IAAI;QACf2H,OAAO,CAAC3H,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;KACD,CAAC;EACJ;EAEQ0H,sBAAsBA,CAAA;IAC5B;IACA,MAAME,kBAAkB,GAAG,IAAI,CAAChB,YAAY,CACzCU,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAC3G,MAAM,KAAK,WAAW,IAAI2G,GAAG,CAAC3G,MAAM,KAAK,WAAW,CAAC,CACvE6G,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAEd,IAAI,CAACvB,gBAAgB,GAAG,CACtB,GAAG0B,kBAAkB,CAACC,GAAG,CAACN,GAAG,KAAK;MAChC5F,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,yBAAyB2F,GAAG,CAACjF,MAAM,EAAEC,QAAQ,OAAOgF,GAAG,CAAC/E,IAAI,EAAE;MAC3E8B,IAAI,EAAE,IAAI,CAACwD,UAAU,CAACP,GAAG,CAAChE,SAAS,CAAC;MACpCpD,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;KACR,CAAC,CAAC,EACH,GAAG,IAAI,CAAC8F,gBAAgB,CAACuB,KAAK,CAACG,kBAAkB,CAACxE,MAAM,CAAC,CAC1D;EACH;EAEQ0E,UAAUA,CAACtF,IAAY;IAC7B,MAAMuF,GAAG,GAAG,IAAIP,IAAI,EAAE;IACtB,MAAMQ,eAAe,GAAG,IAAIR,IAAI,CAAChF,IAAI,CAAC;IACtC,MAAMyF,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGJ,eAAe,CAACI,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE9F,IAAIH,WAAW,GAAG,CAAC,EAAE,OAAO,UAAU;IACtC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,YAAY;IACvD,MAAMI,UAAU,GAAGH,IAAI,CAACC,KAAK,CAACF,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGI,UAAU,WAAW;EACjC;EAEA7G,UAAUA,CAACC,KAAa;IACtB,IAAI,CAACiF,MAAM,CAAC4B,QAAQ,CAAC,CAAC7G,KAAK,CAAC,CAAC;EAC/B;EAEAmE,WAAWA,CAAA;IACT,MAAM2C,IAAI,GAAG,IAAIf,IAAI,EAAE,CAACgB,QAAQ,EAAE;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,cAAc;IACpC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,cAAc;EACvB;EAEA5H,mBAAmBA,CAACC,MAAc;IAChC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,kBAAkB;MACxC,KAAK,SAAS;QAAE,OAAO,kBAAkB;MACzC,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC;QAAS,OAAO,oBAAoB;;EAExC;EAEAkE,WAAWA,CAAA;IACT,IAAI,CAAC6B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACI,gBAAgB,EAAE;IACvB,IAAI,CAACC,eAAe,EAAE;IACtByB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9B,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EAEQK,eAAeA,CAAA;IACrB,IAAI,CAACP,WAAW,CAACiC,YAAY,EAAE,CAACxB,SAAS,CAAC;MACxCC,IAAI,EAAGwB,KAAK,IAAI;QACd,IAAI,CAAC1C,WAAW,GAAG0C,KAAK,CAAClB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC;;MACDzH,KAAK,EAAGA,KAAK,IAAI;QACf2H,OAAO,CAAC3H,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEA8C,aAAaA,CAAA;IACX,MAAM8F,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC;IACzD,IAAIF,YAAY,EAAE;MAChB,MAAMG,KAAK,GAAG,IAAKC,MAAc,CAACC,SAAS,CAACC,KAAK,CAACN,YAAY,CAAC;MAC/DG,KAAK,CAACI,IAAI,EAAE;;EAEhB;EAEAnF,QAAQA,CAACoF,IAAU;IACjB,IAAI,CAACtG,aAAa,EAAE;IACpB;IACA2F,UAAU,CAAC,MAAK;MACd,IAAI,CAACY,cAAc,CAACD,IAAI,CAAC;IAC3B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,cAAcA,CAACD,IAAU;IACvB,IAAI,IAAI,CAACE,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACC,QAAQ,CAACH,IAAI,CAAC;;EAElC;EAEA9F,cAAcA,CAACkG,UAAkB;IAC/B,MAAMhH,IAAI,GAAG,IAAIgF,IAAI,CAACgC,UAAU,CAAC;IACjC,MAAMzB,GAAG,GAAG,IAAIP,IAAI,EAAE;IACtB,MAAMS,WAAW,GAAG,CAACF,GAAG,CAACK,OAAO,EAAE,GAAG5F,IAAI,CAAC4F,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAIH,WAAW,GAAG,CAAC,EAAE;MACnB,OAAO,UAAU;KAClB,MAAM,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAOzF,IAAI,CAACiH,kBAAkB,CAAC,EAAE,EAAE;QAAElB,IAAI,EAAE,SAAS;QAAEmB,MAAM,EAAE;MAAS,CAAE,CAAC;KAC3E,MAAM;MACL,OAAOlH,IAAI,CAACmH,kBAAkB,EAAE;;EAEpC;;;uBAvQWvD,yBAAyB,EAAA5G,EAAA,CAAAoK,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtK,EAAA,CAAAoK,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxK,EAAA,CAAAoK,iBAAA,CAAAK,EAAA,CAAAC,kBAAA,GAAA1K,EAAA,CAAAoK,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA5K,EAAA,CAAAoK,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzBlE,yBAAyB;MAAAmE,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCjCtClL,EAAA,CAAAC,cAAA,aAAkC;UAEhCD,EAAA,CAAAiE,UAAA,IAAAmH,wCAAA,iBAKM;UAGNpL,EAAA,CAAAiE,UAAA,IAAAoH,wCAAA,iBAGM;UAGNrL,EAAA,CAAAiE,UAAA,IAAAqH,wCAAA,mBAgPM;UACRtL,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,aAAyG;UAK/FD,EAAA,CAAAI,SAAA,WAAoC;UAAAJ,EAAA,CAAAE,MAAA,iBACtC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAI,SAAA,kBAA4F;UAC9FJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAmD;UAG9BD,EAAA,CAAAsB,UAAA,0BAAAiK,0EAAAC,MAAA;YAAA,OAAgBL,GAAA,CAAAtB,cAAA,CAAA2B,MAAA,CAAsB;UAAA,EAAC;UAACxL,EAAA,CAAAG,YAAA,EAAgB;UAEzEH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAI,SAAA,iCAA+C;UACjDJ,EAAA,CAAAG,YAAA,EAAM;;;UAlRRH,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAyE,UAAA,SAAA0G,GAAA,CAAAhE,SAAA,CAAe;UAQfnH,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAyE,UAAA,SAAA0G,GAAA,CAAA3K,KAAA,KAAA2K,GAAA,CAAAhE,SAAA,CAAyB;UAMzBnH,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAyE,UAAA,UAAA0G,GAAA,CAAAhE,SAAA,KAAAgE,GAAA,CAAA3K,KAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}