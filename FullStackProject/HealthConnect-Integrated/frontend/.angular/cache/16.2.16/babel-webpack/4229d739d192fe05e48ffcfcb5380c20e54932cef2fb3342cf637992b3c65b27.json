{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/chat.service\";\nimport * as i2 from \"../../../core/services/appointment.service\";\nimport * as i3 from \"../../../core/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../chat-access/chat-access.component\";\nfunction QuickChatWidgetComponent_div_10_div_5_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", chat_r4.lastMessage.content.length > 30 ? chat_r4.lastMessage.content.substring(0, 30) + \"...\" : chat_r4.lastMessage.content, \" \");\n  }\n}\nfunction QuickChatWidgetComponent_div_10_div_5_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1, \"No messages yet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QuickChatWidgetComponent_div_10_div_5_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", chat_r4.unreadCount, \" \");\n  }\n}\nfunction QuickChatWidgetComponent_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function QuickChatWidgetComponent_div_10_div_5_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const chat_r4 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.navigateToChat(chat_r4.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵelement(2, \"img\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"div\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 27);\n    i0.ɵɵtemplate(7, QuickChatWidgetComponent_div_10_div_5_span_7_Template, 2, 1, \"span\", 28);\n    i0.ɵɵtemplate(8, QuickChatWidgetComponent_div_10_div_5_span_8_Template, 2, 0, \"span\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 29)(10, \"small\", 30);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, QuickChatWidgetComponent_div_10_div_5_div_13_Template, 2, 1, \"div\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const chat_r4 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ((tmp_0_0 = ctx_r3.getOtherParticipant(chat_r4)) == null ? null : tmp_0_0.avatar) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", (tmp_1_0 = ctx_r3.getOtherParticipant(chat_r4)) == null ? null : tmp_1_0.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r3.getOtherParticipant(chat_r4)) == null ? null : tmp_2_0.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", chat_r4.lastMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !chat_r4.lastMessage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(12, 7, chat_r4.updatedAt, \"short\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", chat_r4.unreadCount > 0);\n  }\n}\nfunction QuickChatWidgetComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"h6\", 18);\n    i0.ɵɵelement(2, \"i\", 19);\n    i0.ɵɵtext(3, \"Recent Conversations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 20);\n    i0.ɵɵtemplate(5, QuickChatWidgetComponent_div_10_div_5_Template, 14, 10, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.recentChats);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    appointmentId: a0,\n    doctorId: a1,\n    chatType: \"PRE_APPOINTMENT\",\n    buttonText: \"Pre-Chat\",\n    buttonClass: \"btn-outline-info\",\n    size: \"sm\",\n    showIcon: false\n  };\n};\nconst _c1 = function (a0) {\n  return {\n    doctorId: a0,\n    chatType: \"GENERAL\",\n    buttonText: \"General\",\n    buttonClass: \"btn-outline-primary\",\n    size: \"sm\",\n    showIcon: false\n  };\n};\nfunction QuickChatWidgetComponent_div_11_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"div\", 38)(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 39)(6, \"small\", 30);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 40)(10, \"span\", 41);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 42)(13, \"div\", 43);\n    i0.ɵɵelement(14, \"app-chat-access\", 44)(15, \"app-chat-access\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const appointment_r13 = ctx.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(appointment_r13.doctor.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(8, 6, appointment_r13.date, \"mediumDate\"), \" at \", appointment_r13.startTime, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" In \", ctx_r12.getTimeUntilAppointment(appointment_r13), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction2(9, _c0, appointment_r13.id, appointment_r13.doctor.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction1(12, _c1, appointment_r13.doctor.id));\n  }\n}\nfunction QuickChatWidgetComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h6\", 18);\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵtext(3, \"Upcoming Appointments \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 34);\n    i0.ɵɵtemplate(5, QuickChatWidgetComponent_div_11_div_5_Template, 16, 14, \"div\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.upcomingAppointments);\n  }\n}\nfunction QuickChatWidgetComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46);\n    i0.ɵɵelement(2, \"i\", 47);\n    i0.ɵɵelementStart(3, \"h6\", 30);\n    i0.ɵɵtext(4, \"No recent conversations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 18);\n    i0.ɵɵtext(6, \"Start chatting with your healthcare providers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 48);\n    i0.ɵɵelement(8, \"i\", 49);\n    i0.ɵɵtext(9, \" Book Appointment \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c2 = function () {\n  return {\n    chatType: \"URGENT\",\n    buttonText: \"Urgent\",\n    buttonClass: \"btn-outline-danger\",\n    size: \"sm\"\n  };\n};\nexport let QuickChatWidgetComponent = /*#__PURE__*/(() => {\n  class QuickChatWidgetComponent {\n    constructor(chatService, appointmentService, authService, router) {\n      this.chatService = chatService;\n      this.appointmentService = appointmentService;\n      this.authService = authService;\n      this.router = router;\n      this.recentChats = [];\n      this.upcomingAppointments = [];\n      this.loading = false;\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.getCurrentUser();\n      this.loadRecentChats();\n      this.loadUpcomingAppointments();\n    }\n    loadRecentChats() {\n      this.chatService.getUserChats().subscribe({\n        next: chats => {\n          this.recentChats = chats.slice(0, 3); // Show only 3 most recent\n        },\n\n        error: error => {\n          console.error('Error loading recent chats:', error);\n        }\n      });\n    }\n    loadUpcomingAppointments() {\n      if (this.currentUser?.role === 'PATIENT') {\n        this.appointmentService.getPatientAppointments().subscribe({\n          next: appointments => {\n            const now = new Date();\n            this.upcomingAppointments = appointments.filter(apt => new Date(`${apt.date}T${apt.startTime}`) > now).slice(0, 3); // Show only 3 upcoming\n          },\n\n          error: error => {\n            console.error('Error loading appointments:', error);\n          }\n        });\n      }\n    }\n    navigateToChat(chatId) {\n      if (chatId) {\n        this.router.navigate(['/chat'], {\n          queryParams: {\n            chatId\n          }\n        });\n      } else {\n        this.router.navigate(['/chat']);\n      }\n    }\n    startAppointmentChat(appointment, chatType) {\n      const participantId = this.currentUser.role === 'PATIENT' ? appointment.doctor.id : appointment.patient.id;\n      this.appointmentService.createAppointmentChat(appointment.id, participantId, chatType, `${chatType.replace('_', ' ')} discussion for appointment on ${appointment.date}`).subscribe({\n        next: chat => {\n          this.router.navigate(['/chat'], {\n            queryParams: {\n              chatId: chat.id,\n              appointmentId: appointment.id\n            }\n          });\n        },\n        error: error => {\n          console.error('Error creating appointment chat:', error);\n        }\n      });\n    }\n    isBeforeAppointment(appointment) {\n      const appointmentDateTime = new Date(`${appointment.date}T${appointment.startTime}`);\n      return appointmentDateTime > new Date();\n    }\n    getTimeUntilAppointment(appointment) {\n      const appointmentDateTime = new Date(`${appointment.date}T${appointment.startTime}`);\n      const now = new Date();\n      const diffMs = appointmentDateTime.getTime() - now.getTime();\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffDays = Math.floor(diffHours / 24);\n      if (diffDays > 0) {\n        return `${diffDays} day${diffDays > 1 ? 's' : ''}`;\n      } else if (diffHours > 0) {\n        return `${diffHours} hour${diffHours > 1 ? 's' : ''}`;\n      } else {\n        return 'Soon';\n      }\n    }\n    getOtherParticipant(chat) {\n      return this.currentUser.role === 'PATIENT' ? chat.doctor : chat.patient;\n    }\n    static {\n      this.ɵfac = function QuickChatWidgetComponent_Factory(t) {\n        return new (t || QuickChatWidgetComponent)(i0.ɵɵdirectiveInject(i1.ChatService), i0.ɵɵdirectiveInject(i2.AppointmentService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: QuickChatWidgetComponent,\n        selectors: [[\"app-quick-chat-widget\"]],\n        decls: 21,\n        vars: 5,\n        consts: [[1, \"quick-chat-widget\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"fas\", \"fa-comments\", \"text-primary\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-external-link-alt\", \"me-1\"], [1, \"card-body\"], [\"class\", \"mb-4\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [1, \"quick-actions\", \"mt-3\", \"pt-3\", \"border-top\"], [1, \"row\", \"g-2\"], [1, \"col-6\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"w-100\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"w-100\", 3, \"config\"], [1, \"mb-4\"], [1, \"text-muted\", \"mb-3\"], [1, \"fas\", \"fa-clock\", \"me-2\"], [1, \"chat-list\"], [\"class\", \"chat-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"chat-item\", 3, \"click\"], [1, \"chat-avatar\"], [1, \"rounded-circle\", 3, \"src\", \"alt\"], [1, \"chat-info\"], [1, \"chat-name\"], [1, \"chat-preview\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"chat-meta\"], [1, \"text-muted\"], [\"class\", \"badge bg-primary\", 4, \"ngIf\"], [1, \"badge\", \"bg-primary\"], [1, \"fas\", \"fa-calendar-alt\", \"me-2\"], [1, \"appointment-list\"], [\"class\", \"appointment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"appointment-item\"], [1, \"appointment-info\"], [1, \"appointment-doctor\"], [1, \"appointment-details\"], [1, \"appointment-time-left\"], [1, \"badge\", \"bg-info\"], [1, \"appointment-actions\"], [\"role\", \"group\", 1, \"btn-group-vertical\"], [3, \"config\"], [1, \"no-data\"], [1, \"text-center\", \"py-4\"], [1, \"fas\", \"fa-comments\", \"fa-3x\", \"text-muted\", \"mb-3\"], [\"type\", \"button\", \"routerLink\", \"/appointments/book\", 1, \"btn\", \"btn-primary\", \"btn-sm\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\"]],\n        template: function QuickChatWidgetComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h6\", 3);\n            i0.ɵɵelement(4, \"i\", 4);\n            i0.ɵɵtext(5, \" Quick Chat Access \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function QuickChatWidgetComponent_Template_button_click_6_listener() {\n              return ctx.navigateToChat();\n            });\n            i0.ɵɵelement(7, \"i\", 6);\n            i0.ɵɵtext(8, \" View All \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 7);\n            i0.ɵɵtemplate(10, QuickChatWidgetComponent_div_10_Template, 6, 1, \"div\", 8);\n            i0.ɵɵtemplate(11, QuickChatWidgetComponent_div_11_Template, 6, 1, \"div\", 9);\n            i0.ɵɵtemplate(12, QuickChatWidgetComponent_div_12_Template, 10, 0, \"div\", 10);\n            i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"button\", 14);\n            i0.ɵɵlistener(\"click\", function QuickChatWidgetComponent_Template_button_click_16_listener() {\n              return ctx.navigateToChat();\n            });\n            i0.ɵɵelement(17, \"i\", 15);\n            i0.ɵɵtext(18, \" New Chat \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"div\", 13);\n            i0.ɵɵelement(20, \"app-chat-access\", 16);\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngIf\", ctx.recentChats.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.upcomingAppointments.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.recentChats.length === 0 && ctx.upcomingAppointments.length === 0);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"config\", i0.ɵɵpureFunction0(4, _c2));\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i4.RouterLink, i6.ChatAccessComponent, i5.DatePipe],\n        styles: [\".quick-chat-widget[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{border:none;box-shadow:0 2px 8px #0000001a;border-radius:12px;overflow:hidden}.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff 0%,#0056b3 100%);color:#fff;border-bottom:none;padding:1rem 1.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#fff}.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]{border-color:#ffffff80;color:#fff}.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;border-color:#fff}.quick-chat-widget[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:1.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem;border-radius:8px;cursor:pointer;transition:all .2s ease;margin-bottom:.5rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;transform:translate(2px)}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]{flex-shrink:0;margin-right:.75rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:40px;height:40px;object-fit:cover}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-info[_ngcontent-%COMP%]{flex:1;min-width:0}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-info[_ngcontent-%COMP%]   .chat-name[_ngcontent-%COMP%]{font-weight:500;color:#212529;margin-bottom:.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-info[_ngcontent-%COMP%]   .chat-preview[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]{flex-shrink:0;text-align:right}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;margin-top:.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:1rem;border:1px solid #e9ecef;border-radius:8px;margin-bottom:.75rem;background:#f8f9fa}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]{flex:1}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]   .appointment-doctor[_ngcontent-%COMP%]{margin-bottom:.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]   .appointment-details[_ngcontent-%COMP%]{margin-bottom:.5rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]   .appointment-time-left[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]{flex-shrink:0;margin-left:1rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin-bottom:.25rem;font-size:.75rem;padding:.25rem .5rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child{margin-bottom:0}.quick-chat-widget[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]{text-align:center;padding:2rem 1rem}.quick-chat-widget[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.quick-chat-widget[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.875rem;padding:.5rem .75rem}.quick-chat-widget[_ngcontent-%COMP%]   h6.text-muted[_ngcontent-%COMP%]{font-size:.875rem;font-weight:600;text-transform:uppercase;letter-spacing:.5px;margin-bottom:1rem}.quick-chat-widget[_ngcontent-%COMP%]   h6.text-muted[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.7}@media (max-width: 768px){.quick-chat-widget[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:1rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{padding:.5rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;height:32px}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]{margin-left:0;margin-top:.75rem;width:100%}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]{flex-direction:row;width:100%}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{flex:1;margin-bottom:0;margin-right:.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child{margin-right:0}}.quick-chat-widget[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInUp .3s ease-out}@keyframes _ngcontent-%COMP%_slideInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}\"]\n      });\n    }\n  }\n  return QuickChatWidgetComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}