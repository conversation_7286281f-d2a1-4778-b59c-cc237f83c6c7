{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/auth.service\";\nimport * as i2 from \"../../core/services/user.service\";\nimport * as i3 from \"../../core/services/appointment.service\";\nimport * as i4 from \"../../core/services/chat.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../../chat/chat-list/chat-list.component\";\nimport * as i8 from \"../../chat/chat-window/chat-window.component\";\nconst _c0 = [\"chatWindow\"];\nfunction DoctorDashboardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"span\", 19);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 20);\n    i0.ɵɵtext(5, \"Loading your dashboard...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DoctorDashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"i\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u2022 \", ctx_r4.currentUser == null ? null : ctx_r4.currentUser.affiliation, \"\");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 34)(2, \"div\", 39)(3, \"div\", 59)(4, \"div\", 60);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 61)(7, \"h6\", 62);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"h3\", 63);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"small\");\n    i0.ɵɵelement(12, \"i\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const stat_r11 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", stat_r11.icon, \" fs-2 \", stat_r11.color, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r11.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r11.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r5.getChangeClass(stat_r11.changeType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r5.getChangeIcon(stat_r11.changeType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r11.change, \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"i\", 65);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No appointments scheduled for today\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_27_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const appointment_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u2022 \", appointment_r12.reasonForVisit, \"\");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 59)(2, \"div\", 60)(3, \"div\", 67);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h6\", 63);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 68);\n    i0.ɵɵelement(9, \"i\", 69);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementStart(11, \"span\", 70)(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"small\", 71);\n    i0.ɵɵtext(16);\n    i0.ɵɵtemplate(17, DoctorDashboardComponent_div_3_div_27_span_17_Template, 2, 1, \"span\", 3);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 72)(19, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_div_27_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const appointment_r12 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.navigateTo(\"/appointments/\" + appointment_r12.id));\n    });\n    i0.ɵɵelement(20, \"i\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const appointment_r12 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", ctx_r7.getAppointmentTypeIcon(appointment_r12.type), \" \", ctx_r7.getAppointmentTypeClass(appointment_r12.type), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(appointment_r12.patient.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", appointment_r12.startTime, \" - \", appointment_r12.endTime, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r7.getStatusBadgeClass(appointment_r12.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 16, appointment_r12.status), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", appointment_r12.type === \"VIDEO_CALL\" ? \"Video Consultation\" : \"In-Person Visit\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", appointment_r12.reasonForVisit);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate1(\"bi bi-\", appointment_r12.type === \"VIDEO_CALL\" ? \"camera-video\" : \"person\", \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", appointment_r12.type === \"VIDEO_CALL\" ? \"Join Call\" : \"View Details\", \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_35_hr_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\", 79);\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75)(2, \"div\", 60)(3, \"div\", 76);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 61)(6, \"h6\", 77);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 68);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"small\", 71);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, DoctorDashboardComponent_div_3_div_35_hr_12_Template, 1, 0, \"hr\", 78);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const activity_r17 = ctx.$implicit;\n    const isLast_r18 = ctx.last;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", activity_r17.icon, \" \", activity_r17.color, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r17.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r17.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r17.time);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !isLast_r18);\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"i\", 80);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No messages yet\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_48_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind3(2, 2, chat_r20.lastMessage.content, 0, 50), \"\", chat_r20.lastMessage.content.length > 50 ? \"...\" : \"\", \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_48_small_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r20 = i0.ɵɵnextContext().$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.formatChatTime(chat_r20.lastMessage.createdAt), \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_48_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 87);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", chat_r20.unreadCount, \" \");\n  }\n}\nfunction DoctorDashboardComponent_div_3_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 59)(2, \"div\", 60);\n    i0.ɵɵelement(3, \"img\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\")(5, \"h6\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, DoctorDashboardComponent_div_3_div_48_p_7_Template, 3, 6, \"p\", 83);\n    i0.ɵɵtemplate(8, DoctorDashboardComponent_div_3_div_48_small_8_Template, 2, 1, \"small\", 84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 72);\n    i0.ɵɵtemplate(10, DoctorDashboardComponent_div_3_div_48_span_10_Template, 2, 1, \"span\", 85);\n    i0.ɵɵelementStart(11, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_div_48_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const chat_r20 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.openChat(chat_r20));\n    });\n    i0.ɵɵelement(12, \"i\", 86);\n    i0.ɵɵtext(13, \"Reply \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const chat_r20 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", chat_r20.patient.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", chat_r20.patient.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(chat_r20.patient.fullName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chat_r20.lastMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chat_r20.lastMessage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", chat_r20.unreadCount > 0);\n  }\n}\nfunction DoctorDashboardComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 23)(2, \"div\", 24)(3, \"div\", 25)(4, \"div\")(5, \"h1\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 27);\n    i0.ɵɵelement(8, \"i\", 28);\n    i0.ɵɵtext(9);\n    i0.ɵɵtemplate(10, DoctorDashboardComponent_div_3_span_10_Template, 2, 1, \"span\", 3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.refreshData());\n    });\n    i0.ɵɵelement(12, \"i\", 30);\n    i0.ɵɵtext(13, \"Refresh \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"div\", 23);\n    i0.ɵɵtemplate(15, DoctorDashboardComponent_div_3_div_15_Template, 14, 11, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 32)(17, \"div\", 33)(18, \"div\", 34)(19, \"div\", 35)(20, \"h6\", 36);\n    i0.ɵɵelement(21, \"i\", 37);\n    i0.ɵɵtext(22, \"Today's Schedule \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.navigateTo(\"/appointments\"));\n    });\n    i0.ɵɵtext(24, \" View All \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 39);\n    i0.ɵɵtemplate(26, DoctorDashboardComponent_div_3_div_26_Template, 4, 0, \"div\", 40);\n    i0.ɵɵtemplate(27, DoctorDashboardComponent_div_3_div_27_Template, 22, 18, \"div\", 41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 42)(29, \"div\", 34)(30, \"div\", 43)(31, \"h6\", 36);\n    i0.ɵɵelement(32, \"i\", 44);\n    i0.ɵɵtext(33, \"Recent Activities \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 39);\n    i0.ɵɵtemplate(35, DoctorDashboardComponent_div_3_div_35_Template, 13, 8, \"div\", 45);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"div\", 23)(37, \"div\", 24)(38, \"div\", 46)(39, \"div\", 35)(40, \"h6\", 36);\n    i0.ɵɵelement(41, \"i\", 9);\n    i0.ɵɵtext(42, \"Recent Messages \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.openChatModal());\n    });\n    i0.ɵɵelement(44, \"i\", 47);\n    i0.ɵɵtext(45, \"View All Messages \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 39);\n    i0.ɵɵtemplate(47, DoctorDashboardComponent_div_3_div_47_Template, 4, 0, \"div\", 40);\n    i0.ɵɵtemplate(48, DoctorDashboardComponent_div_3_div_48_Template, 14, 6, \"div\", 48);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 32)(50, \"div\", 24)(51, \"div\", 46)(52, \"div\", 43)(53, \"h6\", 36);\n    i0.ɵɵelement(54, \"i\", 49);\n    i0.ɵɵtext(55, \"Quick Actions \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 39)(57, \"div\", 32)(58, \"div\", 50)(59, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.navigateTo(\"/patients\"));\n    });\n    i0.ɵɵelement(60, \"i\", 52);\n    i0.ɵɵelementStart(61, \"span\");\n    i0.ɵɵtext(62, \"Manage Patients\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(63, \"div\", 50)(64, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_64_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.navigateTo(\"/appointments\"));\n    });\n    i0.ɵɵelement(65, \"i\", 54);\n    i0.ɵɵelementStart(66, \"span\");\n    i0.ɵɵtext(67, \"Schedule Appointment\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(68, \"div\", 50)(69, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_69_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.navigateTo(\"/chat\"));\n    });\n    i0.ɵɵelement(70, \"i\", 56);\n    i0.ɵɵelementStart(71, \"span\");\n    i0.ɵɵtext(72, \"Messages\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(73, \"div\", 50)(74, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function DoctorDashboardComponent_div_3_Template_button_click_74_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.navigateTo(\"/reports\"));\n    });\n    i0.ɵɵelement(75, \"i\", 58);\n    i0.ɵɵelementStart(76, \"span\");\n    i0.ɵɵtext(77, \"View Reports\");\n    i0.ɵɵelementEnd()()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.getGreeting(), \", Dr. \", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.fullName, \"!\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.specialization) || \"General Practice\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.affiliation);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.dashboardStats);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.realTodayAppointments.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.realTodayAppointments);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentActivities);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.recentChats.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentChats);\n  }\n}\nexport let DoctorDashboardComponent = /*#__PURE__*/(() => {\n  class DoctorDashboardComponent {\n    constructor(authService, userService, appointmentService, chatService, router) {\n      this.authService = authService;\n      this.userService = userService;\n      this.appointmentService = appointmentService;\n      this.chatService = chatService;\n      this.router = router;\n      this.currentUser = null;\n      this.isLoading = true;\n      this.error = '';\n      this.realTodayAppointments = [];\n      this.recentChats = [];\n      this.dashboardStats = [{\n        title: 'Total Patients',\n        value: '156',\n        change: '+12%',\n        changeType: 'increase',\n        icon: 'people',\n        color: 'text-primary'\n      }, {\n        title: 'Today\\'s Appointments',\n        value: '8',\n        change: '+2',\n        changeType: 'increase',\n        icon: 'calendar-check',\n        color: 'text-success'\n      }, {\n        title: 'Pending Reviews',\n        value: '5',\n        change: '-3',\n        changeType: 'decrease',\n        icon: 'clipboard-check',\n        color: 'text-warning'\n      }, {\n        title: 'Messages',\n        value: '12',\n        change: '+4',\n        changeType: 'increase',\n        icon: 'chat-dots',\n        color: 'text-info'\n      }];\n      this.todayAppointments = [{\n        id: 1,\n        patientName: 'John Smith',\n        time: '09:00 AM',\n        type: 'VIDEO_CALL',\n        status: 'SCHEDULED'\n      }, {\n        id: 2,\n        patientName: 'Sarah Johnson',\n        time: '10:30 AM',\n        type: 'IN_PERSON',\n        status: 'SCHEDULED'\n      }, {\n        id: 3,\n        patientName: 'Michael Brown',\n        time: '02:00 PM',\n        type: 'VIDEO_CALL',\n        status: 'SCHEDULED'\n      }, {\n        id: 4,\n        patientName: 'Emily Davis',\n        time: '03:30 PM',\n        type: 'IN_PERSON',\n        status: 'SCHEDULED'\n      }];\n      this.recentActivities = [{\n        title: 'New Patient Registration',\n        description: 'Alice Wilson registered as a new patient',\n        time: '30 minutes ago',\n        icon: 'person-plus',\n        color: 'text-success'\n      }, {\n        title: 'Appointment Rescheduled',\n        description: 'Tom Anderson moved appointment to tomorrow',\n        time: '1 hour ago',\n        icon: 'calendar-event',\n        color: 'text-warning'\n      }, {\n        title: 'Message Received',\n        description: 'New message from Lisa Parker about medication',\n        time: '2 hours ago',\n        icon: 'envelope',\n        color: 'text-info'\n      }, {\n        title: 'Lab Results Available',\n        description: 'Blood test results for David Miller are ready',\n        time: '3 hours ago',\n        icon: 'file-medical',\n        color: 'text-primary'\n      }];\n    }\n    ngOnInit() {\n      this.loadUserData();\n      this.loadTodayAppointments();\n      this.loadRecentChats();\n    }\n    loadUserData() {\n      this.authService.currentUser$.subscribe({\n        next: user => {\n          this.currentUser = user;\n          this.isLoading = false;\n        },\n        error: error => {\n          this.error = 'Failed to load user data';\n          this.isLoading = false;\n        }\n      });\n    }\n    loadTodayAppointments() {\n      this.appointmentService.getTodayAppointments().subscribe({\n        next: appointments => {\n          this.realTodayAppointments = appointments;\n          this.updateDashboardStats(appointments.length);\n        },\n        error: error => {\n          console.error('Failed to load today appointments:', error);\n        }\n      });\n    }\n    updateDashboardStats(todayCount) {\n      // Update the \"Today's Appointments\" stat with real data\n      const todayStatIndex = this.dashboardStats.findIndex(stat => stat.title === \"Today's Appointments\");\n      if (todayStatIndex !== -1) {\n        this.dashboardStats[todayStatIndex].value = todayCount.toString();\n      }\n    }\n    getGreeting() {\n      const hour = new Date().getHours();\n      if (hour < 12) return 'Good morning';\n      if (hour < 18) return 'Good afternoon';\n      return 'Good evening';\n    }\n    getChangeClass(changeType) {\n      switch (changeType) {\n        case 'increase':\n          return 'text-success';\n        case 'decrease':\n          return 'text-danger';\n        default:\n          return 'text-muted';\n      }\n    }\n    getChangeIcon(changeType) {\n      switch (changeType) {\n        case 'increase':\n          return 'bi-arrow-up';\n        case 'decrease':\n          return 'bi-arrow-down';\n        default:\n          return 'bi-dash';\n      }\n    }\n    getAppointmentTypeIcon(type) {\n      return type === 'VIDEO_CALL' ? 'camera-video' : 'geo-alt';\n    }\n    getAppointmentTypeClass(type) {\n      return type === 'VIDEO_CALL' ? 'text-primary' : 'text-success';\n    }\n    getStatusBadgeClass(status) {\n      switch (status) {\n        case 'SCHEDULED':\n          return 'badge bg-primary';\n        case 'COMPLETED':\n          return 'badge bg-success';\n        case 'CANCELLED':\n          return 'badge bg-danger';\n        default:\n          return 'badge bg-secondary';\n      }\n    }\n    startAppointment(appointment) {\n      if (appointment.type === 'VIDEO_CALL') {\n        this.router.navigate(['/video-call'], {\n          queryParams: {\n            appointmentId: appointment.id\n          }\n        });\n      } else {\n        // For in-person appointments, navigate to patient details or appointment view\n        this.router.navigate(['/appointments', appointment.id]);\n      }\n    }\n    navigateTo(route) {\n      this.router.navigate([route]);\n    }\n    refreshData() {\n      this.isLoading = true;\n      this.loadTodayAppointments();\n      this.loadRecentChats();\n      setTimeout(() => {\n        this.isLoading = false;\n      }, 1000);\n    }\n    loadRecentChats() {\n      this.chatService.getUserChats().subscribe({\n        next: chats => {\n          this.recentChats = chats.slice(0, 3); // Show only recent 3 chats\n        },\n\n        error: error => {\n          console.error('Failed to load chats:', error);\n        }\n      });\n    }\n    openChatModal() {\n      const modalElement = document.getElementById('chatModal');\n      if (modalElement) {\n        const modal = new window.bootstrap.Modal(modalElement);\n        modal.show();\n      }\n    }\n    openChat(chat) {\n      this.openChatModal();\n      // Wait for modal to open, then load chat\n      setTimeout(() => {\n        this.onChatSelected(chat);\n      }, 300);\n    }\n    onChatSelected(chat) {\n      if (this.chatWindow) {\n        this.chatWindow.loadChat(chat);\n      }\n    }\n    formatChatTime(dateString) {\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n      if (diffInHours < 1) {\n        return 'Just now';\n      } else if (diffInHours < 24) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      } else {\n        return date.toLocaleDateString();\n      }\n    }\n    static {\n      this.ɵfac = function DoctorDashboardComponent_Factory(t) {\n        return new (t || DoctorDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AppointmentService), i0.ɵɵdirectiveInject(i4.ChatService), i0.ɵɵdirectiveInject(i5.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DoctorDashboardComponent,\n        selectors: [[\"app-doctor-dashboard\"]],\n        viewQuery: function DoctorDashboardComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatWindow = _t.first);\n          }\n        },\n        decls: 19,\n        vars: 3,\n        consts: [[1, \"container-fluid\", \"py-4\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [\"id\", \"chatModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"chatModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-xl\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"chatModalLabel\", 1, \"modal-title\"], [1, \"bi\", \"bi-chat-dots\", \"me-2\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"modal-body\", \"p-0\", 2, \"height\", \"600px\"], [1, \"row\", \"h-100\", \"g-0\"], [1, \"col-md-4\", \"border-end\"], [3, \"chatSelected\"], [1, \"col-md-8\"], [\"chatWindow\", \"\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-3\", \"text-muted\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-2\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"h3\", \"mb-1\"], [1, \"text-muted\", \"mb-0\"], [1, \"bi\", \"bi-hospital\", \"me-2\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-2\"], [\"class\", \"col-md-3 col-sm-6 mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\"], [1, \"col-md-8\", \"mb-4\"], [1, \"card\", \"h-100\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"bi\", \"bi-calendar-day\", \"me-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"card-body\"], [\"class\", \"text-center py-4 text-muted\", 4, \"ngIf\"], [\"class\", \"appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded border\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"mb-4\"], [1, \"card-header\"], [1, \"bi\", \"bi-activity\", \"me-2\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"card\"], [1, \"bi\", \"bi-chat-plus\", \"me-1\"], [\"class\", \"chat-preview d-flex align-items-center justify-content-between p-3 mb-2 rounded border\", 4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-lightning\", \"me-2\"], [1, \"col-md-3\", \"col-sm-6\", \"mb-3\"], [1, \"btn\", \"btn-outline-primary\", \"w-100\", \"py-3\", 3, \"click\"], [1, \"bi\", \"bi-people\", \"d-block\", \"fs-4\", \"mb-2\"], [1, \"btn\", \"btn-outline-success\", \"w-100\", \"py-3\", 3, \"click\"], [1, \"bi\", \"bi-calendar-plus\", \"d-block\", \"fs-4\", \"mb-2\"], [1, \"btn\", \"btn-outline-info\", \"w-100\", \"py-3\", 3, \"click\"], [1, \"bi\", \"bi-chat-dots\", \"d-block\", \"fs-4\", \"mb-2\"], [1, \"btn\", \"btn-outline-warning\", \"w-100\", \"py-3\", 3, \"click\"], [1, \"bi\", \"bi-graph-up\", \"d-block\", \"fs-4\", \"mb-2\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-shrink-0\", \"me-3\"], [1, \"flex-grow-1\"], [1, \"card-title\", \"text-muted\", \"mb-1\"], [1, \"mb-1\"], [1, \"text-center\", \"py-4\", \"text-muted\"], [1, \"bi\", \"bi-calendar-x\", \"display-6\", \"mb-3\"], [1, \"appointment-item\", \"d-flex\", \"align-items-center\", \"justify-content-between\", \"p-3\", \"mb-2\", \"rounded\", \"border\"], [1, \"rounded-circle\", \"bg-light\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"45px\", \"height\", \"45px\"], [1, \"mb-1\", \"text-muted\", \"small\"], [1, \"bi\", \"bi-clock\", \"me-1\"], [1, \"ms-2\"], [1, \"text-muted\"], [1, \"flex-shrink-0\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [1, \"activity-item\"], [1, \"d-flex\", \"align-items-start\"], [1, \"rounded-circle\", \"bg-light\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"35px\", \"height\", \"35px\"], [1, \"mb-1\", \"small\"], [\"class\", \"my-3\", 4, \"ngIf\"], [1, \"my-3\"], [1, \"bi\", \"bi-chat-square-text\", \"display-6\", \"mb-3\"], [1, \"chat-preview\", \"d-flex\", \"align-items-center\", \"justify-content-between\", \"p-3\", \"mb-2\", \"rounded\", \"border\"], [1, \"rounded-circle\", 2, \"width\", \"45px\", \"height\", \"45px\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [\"class\", \"mb-1 text-muted small\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"class\", \"badge bg-primary rounded-pill me-2\", 4, \"ngIf\"], [1, \"bi\", \"bi-chat\", \"me-1\"], [1, \"badge\", \"bg-primary\", \"rounded-pill\", \"me-2\"]],\n        template: function DoctorDashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, DoctorDashboardComponent_div_1_Template, 6, 0, \"div\", 1);\n            i0.ɵɵtemplate(2, DoctorDashboardComponent_div_2_Template, 3, 1, \"div\", 2);\n            i0.ɵɵtemplate(3, DoctorDashboardComponent_div_3_Template, 78, 10, \"div\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h5\", 8);\n            i0.ɵɵelement(9, \"i\", 9);\n            i0.ɵɵtext(10, \"Messages \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(11, \"button\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12)(14, \"div\", 13)(15, \"app-chat-list\", 14);\n            i0.ɵɵlistener(\"chatSelected\", function DoctorDashboardComponent_Template_app_chat_list_chatSelected_15_listener($event) {\n              return ctx.onChatSelected($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 15);\n            i0.ɵɵelement(17, \"app-chat-window\", null, 16);\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i7.ChatListComponent, i8.ChatWindowComponent, i6.SlicePipe, i6.TitleCasePipe],\n        styles: [\".appointment-item[_ngcontent-%COMP%]{background-color:#f8f9fa;border:1px solid #e9ecef;transition:all .3s ease}.appointment-item[_ngcontent-%COMP%]:hover{background-color:#e9ecef;border-color:#dee2e6}.activity-item[_ngcontent-%COMP%]{padding-bottom:1rem}.activity-item[_ngcontent-%COMP%]:last-child{padding-bottom:0}.card[_ngcontent-%COMP%]{border:none;border-radius:12px;box-shadow:0 2px 4px #0000001a}.card-header[_ngcontent-%COMP%]{background-color:transparent;border-bottom:1px solid #e9ecef;font-weight:600}.btn-outline-primary[_ngcontent-%COMP%], .btn-outline-success[_ngcontent-%COMP%], .btn-outline-info[_ngcontent-%COMP%], .btn-outline-warning[_ngcontent-%COMP%]{border-width:2px;font-weight:500;transition:all .3s ease}.btn-outline-primary[_ngcontent-%COMP%]:hover, .btn-outline-success[_ngcontent-%COMP%]:hover, .btn-outline-info[_ngcontent-%COMP%]:hover, .btn-outline-warning[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #00000026}.badge[_ngcontent-%COMP%]{font-size:.7rem;padding:.25rem .5rem}.text-primary[_ngcontent-%COMP%]{color:#0d6efd!important}.text-success[_ngcontent-%COMP%]{color:#198754!important}.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.text-info[_ngcontent-%COMP%]{color:#0dcaf0!important}.text-danger[_ngcontent-%COMP%]{color:#dc3545!important}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}@media (max-width: 768px){.container-fluid[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.card-body[_ngcontent-%COMP%]{padding:1rem}.appointment-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start!important}.appointment-item[_ngcontent-%COMP%]   .flex-shrink-0[_ngcontent-%COMP%]:last-child{margin-top:1rem;align-self:stretch}.appointment-item[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:100%}}\"]\n      });\n    }\n  }\n  return DoctorDashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}