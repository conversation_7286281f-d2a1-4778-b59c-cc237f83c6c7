{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { AppointmentsRoutingModule } from './appointments-routing.module';\nimport { SharedModule } from '../shared/shared.module';\nimport { AppointmentListComponent } from './appointment-list/appointment-list.component';\nimport { AppointmentBookingComponent } from './appointment-booking/appointment-booking.component';\nimport { DoctorSearchComponent } from './doctor-search/doctor-search.component';\nimport { AppointmentDetailsComponent } from './appointment-details/appointment-details.component';\nimport { AppointmentCalendarComponent } from './appointment-calendar/appointment-calendar.component';\nimport * as i0 from \"@angular/core\";\nexport class AppointmentsModule {\n  static {\n    this.ɵfac = function AppointmentsModule_Factory(t) {\n      return new (t || AppointmentsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppointmentsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, AppointmentsRoutingModule, SharedModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppointmentsModule, {\n    declarations: [AppointmentListComponent, AppointmentBookingComponent, DoctorSearchComponent, AppointmentDetailsComponent, AppointmentCalendarComponent],\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, AppointmentsRoutingModule, SharedModule],\n    exports: [AppointmentListComponent, AppointmentBookingComponent, DoctorSearchComponent, AppointmentDetailsComponent, AppointmentCalendarComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "RouterModule", "AppointmentsRoutingModule", "SharedModule", "AppointmentListComponent", "AppointmentBookingComponent", "Doctor<PERSON><PERSON>chComponent", "AppointmentDetailsComponent", "AppointmentCalendarComponent", "AppointmentsModule", "declarations", "imports", "exports"], "sources": ["/home/<USER>/projects/FullStackProject/HealthConnect-Integrated/frontend/src/app/appointments/appointments.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n\nimport { AppointmentsRoutingModule } from './appointments-routing.module';\nimport { SharedModule } from '../shared/shared.module';\nimport { SharedModule } from '../shared/shared.module';\n\nimport { AppointmentListComponent } from './appointment-list/appointment-list.component';\nimport { AppointmentBookingComponent } from './appointment-booking/appointment-booking.component';\nimport { DoctorSearchComponent } from './doctor-search/doctor-search.component';\nimport { AppointmentDetailsComponent } from './appointment-details/appointment-details.component';\nimport { AppointmentCalendarComponent } from './appointment-calendar/appointment-calendar.component';\n\n@NgModule({\n  declarations: [\n    AppointmentListComponent,\n    AppointmentBookingComponent,\n    DoctorSearchComponent,\n    AppointmentDetailsComponent,\n    AppointmentCalendarComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    RouterModule,\n    AppointmentsRoutingModule,\n    SharedModule\n  ],\n  exports: [\n    AppointmentListComponent,\n    AppointmentBookingComponent,\n    DoctorSearchComponent,\n    AppointmentDetailsComponent,\n    AppointmentCalendarComponent\n  ]\n})\nexport class AppointmentsModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,YAAY,QAAQ,yBAAyB;AAGtD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,2BAA2B,QAAQ,qDAAqD;AACjG,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,2BAA2B,QAAQ,qDAAqD;AACjG,SAASC,4BAA4B,QAAQ,uDAAuD;;AA0BpG,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAf3BX,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZC,yBAAyB,EACzBC,YAAY;IAAA;EAAA;;;2EAUHM,kBAAkB;IAAAC,YAAA,GAtB3BN,wBAAwB,EACxBC,2BAA2B,EAC3BC,qBAAqB,EACrBC,2BAA2B,EAC3BC,4BAA4B;IAAAG,OAAA,GAG5Bb,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZC,yBAAyB,EACzBC,YAAY;IAAAS,OAAA,GAGZR,wBAAwB,EACxBC,2BAA2B,EAC3BC,qBAAqB,EACrBC,2BAA2B,EAC3BC,4BAA4B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}