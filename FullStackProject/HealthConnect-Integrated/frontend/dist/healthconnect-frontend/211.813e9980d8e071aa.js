(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[211],{8211:(m,S,e)=>{"use strict";e.d(S,{m:()=>H});var h=e(1626),l=e(4412),s=e(1413),o=e(467);const n_LF="\n",n_NULL="\0";class r{get body(){return!this._body&&this.isBinaryBody&&(this._body=(new TextDecoder).decode(this._binaryBody)),this._body||""}get binaryBody(){return!this._binaryBody&&!this.isBinaryBody&&(this._binaryBody=(new TextEncoder).encode(this._body)),this._binaryBody}constructor(t){const{command:i,headers:f,body:I,binaryBody:U,escapeHeaderValues:D,skipContentLengthHeader:B}=t;this.command=i,this.headers=Object.assign({},f||{}),U?(this._binaryBody=U,this.isBinaryBody=!0):(this._body=I||"",this.isBinaryBody=!1),this.escapeHeaderValues=D||!1,this.skipContentLengthHeader=B||!1}static fromRawFrame(t,i){const f={},I=U=>U.replace(/^\s+|\s+$/g,"");for(const U of t.headers.reverse()){U.indexOf(":");const B=I(U[0]);let W=I(U[1]);i&&"CONNECT"!==t.command&&"CONNECTED"!==t.command&&(W=r.hdrValueUnEscape(W)),f[B]=W}return new r({command:t.command,headers:f,binaryBody:t.binaryBody,escapeHeaderValues:i})}toString(){return this.serializeCmdAndHeaders()}serialize(){const t=this.serializeCmdAndHeaders();return this.isBinaryBody?r.toUnit8Array(t,this._binaryBody).buffer:t+this._body+n_NULL}serializeCmdAndHeaders(){const t=[this.command];this.skipContentLengthHeader&&delete this.headers["content-length"];for(const i of Object.keys(this.headers||{})){const f=this.headers[i];t.push(this.escapeHeaderValues&&"CONNECT"!==this.command&&"CONNECTED"!==this.command?`${i}:${r.hdrValueEscape(`${f}`)}`:`${i}:${f}`)}return(this.isBinaryBody||!this.isBodyEmpty()&&!this.skipContentLengthHeader)&&t.push(`content-length:${this.bodyLength()}`),t.join(n_LF)+n_LF+n_LF}isBodyEmpty(){return 0===this.bodyLength()}bodyLength(){const t=this.binaryBody;return t?t.length:0}static sizeOfUTF8(t){return t?(new TextEncoder).encode(t).length:0}static toUnit8Array(t,i){const f=(new TextEncoder).encode(t),I=new Uint8Array([0]),U=new Uint8Array(f.length+i.length+I.length);return U.set(f),U.set(i,f.length),U.set(I,f.length+i.length),U}static marshall(t){return new r(t).serialize()}static hdrValueEscape(t){return t.replace(/\\/g,"\\\\").replace(/\r/g,"\\r").replace(/\n/g,"\\n").replace(/:/g,"\\c")}static hdrValueUnEscape(t){return t.replace(/\\r/g,"\r").replace(/\\n/g,"\n").replace(/\\c/g,":").replace(/\\\\/g,"\\")}}class y{constructor(t,i){this.onFrame=t,this.onIncomingPing=i,this._encoder=new TextEncoder,this._decoder=new TextDecoder,this._token=[],this._initState()}parseChunk(t,i=!1){let f;if(f="string"==typeof t?this._encoder.encode(t):new Uint8Array(t),i&&0!==f[f.length-1]){const I=new Uint8Array(f.length+1);I.set(f,0),I[f.length]=0,f=I}for(let I=0;I<f.length;I++)this._onByte(f[I])}_collectFrame(t){if(0!==t&&13!==t){if(10===t)return void this.onIncomingPing();this._onByte=this._collectCommand,this._reinjectByte(t)}}_collectCommand(t){if(13!==t){if(10===t)return this._results.command=this._consumeTokenAsUTF8(),void(this._onByte=this._collectHeaders);this._consumeByte(t)}}_collectHeaders(t){if(13!==t){if(10===t)return void this._setupCollectBody();this._onByte=this._collectHeaderKey,this._reinjectByte(t)}}_reinjectByte(t){this._onByte(t)}_collectHeaderKey(t){if(58===t)return this._headerKey=this._consumeTokenAsUTF8(),void(this._onByte=this._collectHeaderValue);this._consumeByte(t)}_collectHeaderValue(t){if(13!==t){if(10===t)return this._results.headers.push([this._headerKey,this._consumeTokenAsUTF8()]),this._headerKey=void 0,void(this._onByte=this._collectHeaders);this._consumeByte(t)}}_setupCollectBody(){const t=this._results.headers.filter(i=>"content-length"===i[0])[0];t?(this._bodyBytesRemaining=parseInt(t[1],10),this._onByte=this._collectBodyFixedSize):this._onByte=this._collectBodyNullTerminated}_collectBodyNullTerminated(t){0!==t?this._consumeByte(t):this._retrievedBody()}_collectBodyFixedSize(t){0!=this._bodyBytesRemaining--?this._consumeByte(t):this._retrievedBody()}_retrievedBody(){this._results.binaryBody=this._consumeTokenAsRaw();try{this.onFrame(this._results)}catch(t){console.log("Ignoring an exception thrown by a frame handler. Original exception: ",t)}this._initState()}_consumeByte(t){this._token.push(t)}_consumeTokenAsUTF8(){return this._decoder.decode(this._consumeTokenAsRaw())}_consumeTokenAsRaw(){const t=new Uint8Array(this._token);return this._token=[],t}_initState(){this._results={command:void 0,headers:[],binaryBody:void 0},this._token=[],this._headerKey=void 0,this._onByte=this._collectFrame}}var x=function(N){return N[N.CONNECTING=0]="CONNECTING",N[N.OPEN=1]="OPEN",N[N.CLOSING=2]="CLOSING",N[N.CLOSED=3]="CLOSED",N}(x||{}),T=function(N){return N[N.ACTIVE=0]="ACTIVE",N[N.DEACTIVATING=1]="DEACTIVATING",N[N.INACTIVE=2]="INACTIVE",N}(T||{}),L=function(N){return N[N.LINEAR=0]="LINEAR",N[N.EXPONENTIAL=1]="EXPONENTIAL",N}(L||{}),k=function(N){return N.Interval="interval",N.Worker="worker",N}(k||{});class j{constructor(t,i=k.Interval,f){this._interval=t,this._strategy=i,this._debug=f,this._workerScript=`\n    var startTime = Date.now();\n    setInterval(function() {\n        self.postMessage(Date.now() - startTime);\n    }, ${this._interval});\n  `}start(t){this.stop(),this.shouldUseWorker()?this.runWorker(t):this.runInterval(t)}stop(){this.disposeWorker(),this.disposeInterval()}shouldUseWorker(){return typeof Worker<"u"&&this._strategy===k.Worker}runWorker(t){this._debug("Using runWorker for outgoing pings"),this._worker||(this._worker=new Worker(URL.createObjectURL(new Blob([this._workerScript],{type:"text/javascript"}))),this._worker.onmessage=i=>t(i.data))}runInterval(t){if(this._debug("Using runInterval for outgoing pings"),!this._timer){const i=Date.now();this._timer=setInterval(()=>{t(Date.now()-i)},this._interval)}}disposeWorker(){this._worker&&(this._worker.terminate(),delete this._worker,this._debug("Outgoing ping disposeWorker"))}disposeInterval(){this._timer&&(clearInterval(this._timer),delete this._timer,this._debug("Outgoing ping disposeInterval"))}}class A{constructor(t){this.versions=t}supportedVersions(){return this.versions.join(",")}protocolVersions(){return this.versions.map(t=>`v${t.replace(".","")}.stomp`)}}A.V1_0="1.0",A.V1_1="1.1",A.V1_2="1.2",A.default=new A([A.V1_2,A.V1_1,A.V1_0]);class v{get connectedVersion(){return this._connectedVersion}get connected(){return this._connected}constructor(t,i,f){this._client=t,this._webSocket=i,this._connected=!1,this._serverFrameHandlers={CONNECTED:I=>{this.debug(`connected to server ${I.headers.server}`),this._connected=!0,this._connectedVersion=I.headers.version,this._connectedVersion===A.V1_2&&(this._escapeHeaderValues=!0),this._setupHeartbeat(I.headers),this.onConnect(I)},MESSAGE:I=>{const U=I.headers.subscription,D=this._subscriptions[U]||this.onUnhandledMessage,B=I,W=this,P=this._connectedVersion===A.V1_2?B.headers.ack:B.headers["message-id"];B.ack=(F={})=>W.ack(P,U,F),B.nack=(F={})=>W.nack(P,U,F),D(B)},RECEIPT:I=>{const U=this._receiptWatchers[I.headers["receipt-id"]];U?(U(I),delete this._receiptWatchers[I.headers["receipt-id"]]):this.onUnhandledReceipt(I)},ERROR:I=>{this.onStompError(I)}},this._counter=0,this._subscriptions={},this._receiptWatchers={},this._partialData="",this._escapeHeaderValues=!1,this._lastServerActivityTS=Date.now(),this.debug=f.debug,this.stompVersions=f.stompVersions,this.connectHeaders=f.connectHeaders,this.disconnectHeaders=f.disconnectHeaders,this.heartbeatIncoming=f.heartbeatIncoming,this.heartbeatOutgoing=f.heartbeatOutgoing,this.splitLargeFrames=f.splitLargeFrames,this.maxWebSocketChunkSize=f.maxWebSocketChunkSize,this.forceBinaryWSFrames=f.forceBinaryWSFrames,this.logRawCommunication=f.logRawCommunication,this.appendMissingNULLonIncoming=f.appendMissingNULLonIncoming,this.discardWebsocketOnCommFailure=f.discardWebsocketOnCommFailure,this.onConnect=f.onConnect,this.onDisconnect=f.onDisconnect,this.onStompError=f.onStompError,this.onWebSocketClose=f.onWebSocketClose,this.onWebSocketError=f.onWebSocketError,this.onUnhandledMessage=f.onUnhandledMessage,this.onUnhandledReceipt=f.onUnhandledReceipt,this.onUnhandledFrame=f.onUnhandledFrame}start(){const t=new y(i=>{const f=r.fromRawFrame(i,this._escapeHeaderValues);this.logRawCommunication||this.debug(`<<< ${f}`),(this._serverFrameHandlers[f.command]||this.onUnhandledFrame)(f)},()=>{this.debug("<<< PONG")});this._webSocket.onmessage=i=>{if(this.debug("Received data"),this._lastServerActivityTS=Date.now(),this.logRawCommunication){const f=i.data instanceof ArrayBuffer?(new TextDecoder).decode(i.data):i.data;this.debug(`<<< ${f}`)}t.parseChunk(i.data,this.appendMissingNULLonIncoming)},this._webSocket.onclose=i=>{this.debug(`Connection closed to ${this._webSocket.url}`),this._cleanUp(),this.onWebSocketClose(i)},this._webSocket.onerror=i=>{this.onWebSocketError(i)},this._webSocket.onopen=()=>{const i=Object.assign({},this.connectHeaders);this.debug("Web Socket Opened..."),i["accept-version"]=this.stompVersions.supportedVersions(),i["heart-beat"]=[this.heartbeatOutgoing,this.heartbeatIncoming].join(","),this._transmit({command:"CONNECT",headers:i})}}_setupHeartbeat(t){if(t.version!==A.V1_1&&t.version!==A.V1_2||!t["heart-beat"])return;const[i,f]=t["heart-beat"].split(",").map(I=>parseInt(I,10));if(0!==this.heartbeatOutgoing&&0!==f){const I=Math.max(this.heartbeatOutgoing,f);this.debug(`send PING every ${I}ms`),this._pinger=new j(I,this._client.heartbeatStrategy,this.debug),this._pinger.start(()=>{this._webSocket.readyState===x.OPEN&&(this._webSocket.send(n_LF),this.debug(">>> PING"))})}if(0!==this.heartbeatIncoming&&0!==i){const I=Math.max(this.heartbeatIncoming,i);this.debug(`check PONG every ${I}ms`),this._ponger=setInterval(()=>{const U=Date.now()-this._lastServerActivityTS;U>2*I&&(this.debug(`did not receive server activity for the last ${U}ms`),this._closeOrDiscardWebsocket())},I)}}_closeOrDiscardWebsocket(){this.discardWebsocketOnCommFailure?(this.debug("Discarding websocket, the underlying socket may linger for a while"),this.discardWebsocket()):(this.debug("Issuing close on the websocket"),this._closeWebsocket())}forceDisconnect(){this._webSocket&&(this._webSocket.readyState===x.CONNECTING||this._webSocket.readyState===x.OPEN)&&this._closeOrDiscardWebsocket()}_closeWebsocket(){this._webSocket.onmessage=()=>{},this._webSocket.close()}discardWebsocket(){"function"!=typeof this._webSocket.terminate&&function a(N,t){N.terminate=function(){const i=()=>{};this.onerror=i,this.onmessage=i,this.onopen=i;const f=new Date,I=Math.random().toString().substring(2,8),U=this.onclose;this.onclose=D=>{const B=(new Date).getTime()-f.getTime();t(`Discarded socket (#${I})  closed after ${B}ms, with code/reason: ${D.code}/${D.reason}`)},this.close(),U?.call(N,{code:4001,reason:`Quick discarding socket (#${I}) without waiting for the shutdown sequence.`,wasClean:!1})}}(this._webSocket,t=>this.debug(t)),this._webSocket.terminate()}_transmit(t){const{command:i,headers:f,body:I,binaryBody:U,skipContentLengthHeader:D}=t,B=new r({command:i,headers:f,body:I,binaryBody:U,escapeHeaderValues:this._escapeHeaderValues,skipContentLengthHeader:D});let W=B.serialize();if(this.debug(this.logRawCommunication?`>>> ${W}`:`>>> ${B}`),this.forceBinaryWSFrames&&"string"==typeof W&&(W=(new TextEncoder).encode(W)),"string"==typeof W&&this.splitLargeFrames){let P=W;for(;P.length>0;){const F=P.substring(0,this.maxWebSocketChunkSize);P=P.substring(this.maxWebSocketChunkSize),this._webSocket.send(F),this.debug(`chunk sent = ${F.length}, remaining = ${P.length}`)}}else this._webSocket.send(W)}dispose(){if(this.connected)try{const t=Object.assign({},this.disconnectHeaders);t.receipt||(t.receipt="close-"+this._counter++),this.watchForReceipt(t.receipt,i=>{this._closeWebsocket(),this._cleanUp(),this.onDisconnect(i)}),this._transmit({command:"DISCONNECT",headers:t})}catch(t){this.debug(`Ignoring error during disconnect ${t}`)}else(this._webSocket.readyState===x.CONNECTING||this._webSocket.readyState===x.OPEN)&&this._closeWebsocket()}_cleanUp(){this._connected=!1,this._pinger&&(this._pinger.stop(),this._pinger=void 0),this._ponger&&(clearInterval(this._ponger),this._ponger=void 0)}publish(t){const{destination:i,headers:f,body:I,binaryBody:U,skipContentLengthHeader:D}=t,B=Object.assign({destination:i},f);this._transmit({command:"SEND",headers:B,body:I,binaryBody:U,skipContentLengthHeader:D})}watchForReceipt(t,i){this._receiptWatchers[t]=i}subscribe(t,i,f={}){(f=Object.assign({},f)).id||(f.id="sub-"+this._counter++),f.destination=t,this._subscriptions[f.id]=i,this._transmit({command:"SUBSCRIBE",headers:f});const I=this;return{id:f.id,unsubscribe:U=>I.unsubscribe(f.id,U)}}unsubscribe(t,i={}){i=Object.assign({},i),delete this._subscriptions[t],i.id=t,this._transmit({command:"UNSUBSCRIBE",headers:i})}begin(t){const i=t||"tx-"+this._counter++;this._transmit({command:"BEGIN",headers:{transaction:i}});const f=this;return{id:i,commit(){f.commit(i)},abort(){f.abort(i)}}}commit(t){this._transmit({command:"COMMIT",headers:{transaction:t}})}abort(t){this._transmit({command:"ABORT",headers:{transaction:t}})}ack(t,i,f={}){f=Object.assign({},f),this._connectedVersion===A.V1_2?f.id=t:f["message-id"]=t,f.subscription=i,this._transmit({command:"ACK",headers:f})}nack(t,i,f={}){return f=Object.assign({},f),this._connectedVersion===A.V1_2?f.id=t:f["message-id"]=t,f.subscription=i,this._transmit({command:"NACK",headers:f})}}class O{get webSocket(){return this._stompHandler?._webSocket}get disconnectHeaders(){return this._disconnectHeaders}set disconnectHeaders(t){this._disconnectHeaders=t,this._stompHandler&&(this._stompHandler.disconnectHeaders=this._disconnectHeaders)}get connected(){return!!this._stompHandler&&this._stompHandler.connected}get connectedVersion(){return this._stompHandler?this._stompHandler.connectedVersion:void 0}get active(){return this.state===T.ACTIVE}_changeState(t){this.state=t,this.onChangeState(t)}constructor(t={}){this.stompVersions=A.default,this.connectionTimeout=0,this.reconnectDelay=5e3,this._nextReconnectDelay=0,this.maxReconnectDelay=9e5,this.reconnectTimeMode=L.LINEAR,this.heartbeatIncoming=1e4,this.heartbeatOutgoing=1e4,this.heartbeatStrategy=k.Interval,this.splitLargeFrames=!1,this.maxWebSocketChunkSize=8192,this.forceBinaryWSFrames=!1,this.appendMissingNULLonIncoming=!1,this.discardWebsocketOnCommFailure=!1,this.state=T.INACTIVE;const i=()=>{};this.debug=i,this.beforeConnect=i,this.onConnect=i,this.onDisconnect=i,this.onUnhandledMessage=i,this.onUnhandledReceipt=i,this.onUnhandledFrame=i,this.onStompError=i,this.onWebSocketClose=i,this.onWebSocketError=i,this.logRawCommunication=!1,this.onChangeState=i,this.connectHeaders={},this._disconnectHeaders={},this.configure(t)}configure(t){Object.assign(this,t),this.maxReconnectDelay>0&&this.maxReconnectDelay<this.reconnectDelay&&(this.debug(`Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`),this.maxReconnectDelay=this.reconnectDelay)}activate(){const t=()=>{this.active?this.debug("Already ACTIVE, ignoring request to activate"):(this._changeState(T.ACTIVE),this._nextReconnectDelay=this.reconnectDelay,this._connect())};this.state===T.DEACTIVATING?(this.debug("Waiting for deactivation to finish before activating"),this.deactivate().then(()=>{t()})):t()}_connect(){var t=this;return(0,o.A)(function*(){if(yield t.beforeConnect(t),t._stompHandler)return void t.debug("There is already a stompHandler, skipping the call to connect");if(!t.active)return void t.debug("Client has been marked inactive, will not attempt to connect");t.connectionTimeout>0&&(t._connectionWatcher&&clearTimeout(t._connectionWatcher),t._connectionWatcher=setTimeout(()=>{t.connected||(t.debug(`Connection not established in ${t.connectionTimeout}ms, closing socket`),t.forceDisconnect())},t.connectionTimeout)),t.debug("Opening Web Socket...");const i=t._createWebSocket();t._stompHandler=new v(t,i,{debug:t.debug,stompVersions:t.stompVersions,connectHeaders:t.connectHeaders,disconnectHeaders:t._disconnectHeaders,heartbeatIncoming:t.heartbeatIncoming,heartbeatOutgoing:t.heartbeatOutgoing,heartbeatStrategy:t.heartbeatStrategy,splitLargeFrames:t.splitLargeFrames,maxWebSocketChunkSize:t.maxWebSocketChunkSize,forceBinaryWSFrames:t.forceBinaryWSFrames,logRawCommunication:t.logRawCommunication,appendMissingNULLonIncoming:t.appendMissingNULLonIncoming,discardWebsocketOnCommFailure:t.discardWebsocketOnCommFailure,onConnect:f=>{if(t._connectionWatcher&&(clearTimeout(t._connectionWatcher),t._connectionWatcher=void 0),!t.active)return t.debug("STOMP got connected while deactivate was issued, will disconnect now"),void t._disposeStompHandler();t.onConnect(f)},onDisconnect:f=>{t.onDisconnect(f)},onStompError:f=>{t.onStompError(f)},onWebSocketClose:f=>{t._stompHandler=void 0,t.state===T.DEACTIVATING&&t._changeState(T.INACTIVE),t.onWebSocketClose(f),t.active&&t._schedule_reconnect()},onWebSocketError:f=>{t.onWebSocketError(f)},onUnhandledMessage:f=>{t.onUnhandledMessage(f)},onUnhandledReceipt:f=>{t.onUnhandledReceipt(f)},onUnhandledFrame:f=>{t.onUnhandledFrame(f)}}),t._stompHandler.start()})()}_createWebSocket(){let t;if(this.webSocketFactory)t=this.webSocketFactory();else{if(!this.brokerURL)throw new Error("Either brokerURL or webSocketFactory must be provided");t=new WebSocket(this.brokerURL,this.stompVersions.protocolVersions())}return t.binaryType="arraybuffer",t}_schedule_reconnect(){this._nextReconnectDelay>0&&(this.debug(`STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`),this._reconnector=setTimeout(()=>{this.reconnectTimeMode===L.EXPONENTIAL&&(this._nextReconnectDelay=2*this._nextReconnectDelay,0!==this.maxReconnectDelay&&(this._nextReconnectDelay=Math.min(this._nextReconnectDelay,this.maxReconnectDelay))),this._connect()},this._nextReconnectDelay))}deactivate(){var t=this;return(0,o.A)(function*(i={}){const f=i.force||!1,I=t.active;let U;if(t.state===T.INACTIVE)return t.debug("Already INACTIVE, nothing more to do"),Promise.resolve();if(t._changeState(T.DEACTIVATING),t._nextReconnectDelay=0,t._reconnector&&(clearTimeout(t._reconnector),t._reconnector=void 0),!t._stompHandler||t.webSocket.readyState===x.CLOSED)return t._changeState(T.INACTIVE),Promise.resolve();{const D=t._stompHandler.onWebSocketClose;U=new Promise((B,W)=>{t._stompHandler.onWebSocketClose=P=>{D(P),B()}})}return f?t._stompHandler?.discardWebsocket():I&&t._disposeStompHandler(),U}).apply(this,arguments)}forceDisconnect(){this._stompHandler&&this._stompHandler.forceDisconnect()}_disposeStompHandler(){this._stompHandler&&this._stompHandler.dispose()}publish(t){this._checkConnection(),this._stompHandler.publish(t)}_checkConnection(){if(!this.connected)throw new TypeError("There is no underlying STOMP connection")}watchForReceipt(t,i){this._checkConnection(),this._stompHandler.watchForReceipt(t,i)}subscribe(t,i,f={}){return this._checkConnection(),this._stompHandler.subscribe(t,i,f)}unsubscribe(t,i={}){this._checkConnection(),this._stompHandler.unsubscribe(t,i)}begin(t){return this._checkConnection(),this._stompHandler.begin(t)}commit(t){this._checkConnection(),this._stompHandler.commit(t)}abort(t){this._checkConnection(),this._stompHandler.abort(t)}ack(t,i,f={}){this._checkConnection(),this._stompHandler.ack(t,i,f)}nack(t,i,f={}){this._checkConnection(),this._stompHandler.nack(t,i,f)}}var u=e(25),c=e.n(u),w=e(5312),E=e(540),C=e(8010),R=e(5567);let H=(()=>{class N{constructor(i,f,I){this.http=i,this.authService=f,this.notificationService=I,this.apiUrl=`${w.c.apiUrl}/chats`,this.wsUrl=`${w.c.apiUrl}/ws`,this.stompClient=null,this.connectionStatusSubject=new l.t(!1),this.messageSubject=new s.B,this.typingSubject=new s.B,this.chatsSubject=new l.t([]),this.connectionStatus$=this.connectionStatusSubject.asObservable(),this.messages$=this.messageSubject.asObservable(),this.typing$=this.typingSubject.asObservable(),this.chats$=this.chatsSubject.asObservable(),this.initializeWebSocketConnection()}initializeWebSocketConnection(){this.authService.isAuthenticated()&&this.connect(),this.authService.currentUser$.subscribe(i=>{i?this.connect():this.disconnect()})}connect(){if(this.stompClient?.connected)return;const i=this.authService.getToken();i&&(this.stompClient=new O({webSocketFactory:()=>new(c())(this.wsUrl),connectHeaders:{Authorization:`Bearer ${i}`},debug:f=>{console.log("STOMP Debug:",f)},onConnect:()=>{this.connectionStatusSubject.next(!0),console.log("WebSocket connected successfully"),this.subscribeToUserChannels()},onWebSocketClose:()=>{this.connectionStatusSubject.next(!1),console.log("WebSocket connection closed"),setTimeout(()=>{this.authService.isAuthenticated()&&this.connect()},5e3)},onStompError:f=>{console.error("STOMP error:",f),this.connectionStatusSubject.next(!1)}}),this.stompClient.activate())}subscribeToUserChannels(){!this.stompClient?.connected||!this.authService.getCurrentUser()||this.stompClient.subscribe("/user/queue/errors",f=>{console.error("WebSocket error:",f.body)})}subscribeToChatMessages(i){this.stompClient?.connected&&(this.stompClient.subscribe(`/topic/chat/${i}`,f=>{const I=JSON.parse(f.body);this.messageSubject.next(I);const U=this.authService.getCurrentUser();I.sender.id!==U?.id&&this.notificationService.addMessageNotification(I.sender,I.content,i)}),this.stompClient.subscribe(`/topic/chat/${i}/typing`,f=>{const I=JSON.parse(f.body);this.typingSubject.next(I)}))}sendMessage(i,f){if(!this.stompClient?.connected)throw new Error("WebSocket not connected");const I=this.authService.getToken();if(!I)throw new Error("No authentication token");this.stompClient.publish({destination:`/app/chat/${i}/send`,body:JSON.stringify({chatId:i,content:f}),headers:{Authorization:`Bearer ${I}`}})}sendTypingNotification(i,f){if(!this.stompClient?.connected)return;const I=this.authService.getToken();I&&this.stompClient.publish({destination:`/app/chat/${i}/typing`,body:f?"typing":"stopped",headers:{Authorization:`Bearer ${I}`}})}disconnect(){this.stompClient&&(this.stompClient.deactivate(),this.connectionStatusSubject.next(!1))}createOrGetChat(i){return this.http.post(this.apiUrl,{participantId:i},this.getHttpOptions())}getUserChats(){return this.http.get(this.apiUrl,this.getHttpOptions())}getChatMessages(i,f=0,I=50){const U={page:f.toString(),size:I.toString()};return this.http.get(`${this.apiUrl}/${i}/messages`,{...this.getHttpOptions(),params:U})}markMessagesAsRead(i){return this.http.put(`${this.apiUrl}/${i}/read`,{},this.getHttpOptions())}loadUserChats(){this.getUserChats().subscribe({next:i=>{this.chatsSubject.next(i)},error:i=>{console.error("Failed to load chats:",i)}})}getHttpOptions(){const i=this.authService.getToken();return{headers:new h.Lr({"Content-Type":"application/json",Authorization:`Bearer ${i}`})}}static{this.\u0275fac=function(f){return new(f||N)(E.KVO(h.Qq),E.KVO(C.u),E.KVO(R.J))}}static{this.\u0275prov=E.jDH({token:N,factory:N.\u0275fac,providedIn:"root"})}}return N})()},1993:m=>{m.exports="function"==typeof Object.create?function(e,h){h&&(e.super_=h,e.prototype=Object.create(h.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:function(e,h){if(h){e.super_=h;var l=function(){};l.prototype=h.prototype,e.prototype=new l,e.prototype.constructor=e}}},8629:(m,S)=>{"use strict";var e=Object.prototype.hasOwnProperty;function l(n){try{return decodeURIComponent(n.replace(/\+/g," "))}catch{return null}}function s(n){try{return encodeURIComponent(n)}catch{return null}}S.stringify=function a(n,r){r=r||"";var d,b,p=[];for(b in"string"!=typeof r&&(r="?"),n)if(e.call(n,b)){if(!(d=n[b])&&(null==d||isNaN(d))&&(d=""),b=s(b),d=s(d),null===b||null===d)continue;p.push(b+"="+d)}return p.length?r+p.join("&"):""},S.parse=function o(n){for(var d,r=/([^=?#&]+)=?([^&]*)/g,p={};d=r.exec(n);){var b=l(d[1]),g=l(d[2]);null===b||null===g||b in p||(p[b]=g)}return p}},5852:m=>{"use strict";m.exports=function(e,h){if(h=h.split(":")[0],!(e=+e))return!1;switch(h){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e}},25:(m,S,e)=>{"use strict";var h=e(1049);m.exports=e(9084)(h),"_sockjs_onload"in global&&setTimeout(global._sockjs_onload,1)},4542:(m,S,e)=>{"use strict";var h=e(1993),l=e(6886);function s(){l.call(this),this.initEvent("close",!1,!1),this.wasClean=!1,this.code=0,this.reason=""}h(s,l),m.exports=s},6964:(m,S,e)=>{"use strict";var h=e(1993),l=e(8551);function s(){l.call(this)}h(s,l),s.prototype.removeAllListeners=function(o){o?delete this._listeners[o]:this._listeners={}},s.prototype.once=function(o,a){var n=this,r=!1;this.on(o,function p(){n.removeListener(o,p),r||(r=!0,a.apply(this,arguments))})},s.prototype.emit=function(){var a=this._listeners[arguments[0]];if(a){for(var n=arguments.length,r=new Array(n-1),p=1;p<n;p++)r[p-1]=arguments[p];for(var d=0;d<a.length;d++)a[d].apply(this,r)}},s.prototype.on=s.prototype.addListener=l.prototype.addEventListener,s.prototype.removeListener=l.prototype.removeEventListener,m.exports.b=s},6886:m=>{"use strict";function S(e){this.type=e}S.prototype.initEvent=function(e,h,l){return this.type=e,this.bubbles=h,this.cancelable=l,this.timeStamp=+new Date,this},S.prototype.stopPropagation=function(){},S.prototype.preventDefault=function(){},S.CAPTURING_PHASE=1,S.AT_TARGET=2,S.BUBBLING_PHASE=3,m.exports=S},8551:m=>{"use strict";function S(){this._listeners={}}S.prototype.addEventListener=function(e,h){e in this._listeners||(this._listeners[e]=[]);var l=this._listeners[e];-1===l.indexOf(h)&&(l=l.concat([h])),this._listeners[e]=l},S.prototype.removeEventListener=function(e,h){var l=this._listeners[e];if(l){var s=l.indexOf(h);if(-1!==s)return void(l.length>1?this._listeners[e]=l.slice(0,s).concat(l.slice(s+1)):delete this._listeners[e])}},S.prototype.dispatchEvent=function(){var e=arguments[0],h=e.type,l=1===arguments.length?[e]:Array.apply(null,arguments);if(this["on"+h]&&this["on"+h].apply(this,l),h in this._listeners)for(var s=this._listeners[h],o=0;o<s.length;o++)s[o].apply(this,l)},m.exports=S},5714:(m,S,e)=>{"use strict";var h=e(1993),l=e(6886);function s(o){l.call(this),this.initEvent("message",!1,!1),this.data=o}h(s,l),m.exports=s},183:(m,S,e)=>{"use strict";var h=e(9915);function l(s){this._transport=s,s.on("message",this._transportMessage.bind(this)),s.on("close",this._transportClose.bind(this))}l.prototype._transportClose=function(s,o){h.postMessage("c",JSON.stringify([s,o]))},l.prototype._transportMessage=function(s){h.postMessage("t",s)},l.prototype._send=function(s){this._transport.send(s)},l.prototype._close=function(){this._transport.close(),this._transport.removeAllListeners()},m.exports=l},7958:(m,S,e)=>{"use strict";var h=e(2630),l=e(1059),s=e(183),o=e(7094),a=e(9915),n=e(6);m.exports=function(p,d){var g,b={};d.forEach(function(y){y.facadeTransport&&(b[y.facadeTransport.transportName]=y.facadeTransport)}),b[o.transportName]=o,p.bootstrap_iframe=function(){var y;a.currentWindowId=n.hash.slice(1),l.attachEvent("message",function(T){if(T.source===parent&&(typeof g>"u"&&(g=T.origin),T.origin===g)){var L;try{L=JSON.parse(T.data)}catch{return}if(L.windowId===a.currentWindowId)switch(L.type){case"s":var k;try{k=JSON.parse(L.data)}catch{break}var j=k[0],A=k[1],v=k[2],O=k[3];if(j!==p.version)throw new Error('Incompatible SockJS! Main site uses: "'+j+'", the iframe: "'+p.version+'".');if(!h.isOriginEqual(v,n.href)||!h.isOriginEqual(O,n.href))throw new Error("Can't connect to different domain from within an iframe. ("+n.href+", "+v+", "+O+")");y=new s(new b[A](v,O));break;case"m":y._send(L.data);break;case"c":y&&y._close(),y=null}}}),a.postMessage("s")}}},4036:(m,S,e)=>{"use strict";var h=e(6964).b,l=e(1993),s=e(7824);function a(n,r){h.call(this);var p=this,d=+new Date;this.xo=new r("GET",n),this.xo.once("finish",function(b,g){var y,x;if(200===b){if(x=+new Date-d,g)try{y=JSON.parse(g)}catch{}s.isObject(y)||(y={})}p.emit("finish",y,x),p.removeAllListeners()})}l(a,h),a.prototype.close=function(){this.removeAllListeners(),this.xo.close()},m.exports=a},7094:(m,S,e)=>{"use strict";var h=e(1993),l=e(6964).b,s=e(9781),o=e(4036);function a(n){var r=this;l.call(this),this.ir=new o(n,s),this.ir.once("finish",function(p,d){r.ir=null,r.emit("message",JSON.stringify([p,d]))})}h(a,l),a.transportName="iframe-info-receiver",a.prototype.close=function(){this.ir&&(this.ir.close(),this.ir=null),this.removeAllListeners()},m.exports=a},4934:(m,S,e)=>{"use strict";var h=e(6964).b,l=e(1993),s=e(1059),o=e(6531),a=e(7094);function r(p,d){var b=this;h.call(this);var g=function(){var y=b.ifr=new o(a.transportName,d,p);y.once("message",function(x){if(x){var T;try{T=JSON.parse(x)}catch{return b.emit("finish"),void b.close()}b.emit("finish",T[0],T[1])}b.close()}),y.once("close",function(){b.emit("finish"),b.close()})};global.document.body?g():s.attachEvent("load",g)}l(r,h),r.enabled=function(){return o.enabled()},r.prototype.close=function(){this.ifr&&this.ifr.close(),this.removeAllListeners(),this.ifr=null},m.exports=r},4779:(m,S,e)=>{"use strict";var h=e(6964).b,l=e(1993),s=e(2630),o=e(3103),a=e(53),n=e(9781),r=e(1749),p=e(4934),d=e(4036);function g(y,x){var T=this;h.call(this),setTimeout(function(){T.doXhr(y,x)},0)}l(g,h),g._getReceiver=function(y,x,T){return T.sameOrigin?new d(x,n):a.enabled?new d(x,a):o.enabled&&T.sameScheme?new d(x,o):p.enabled()?new p(y,x):new d(x,r)},g.prototype.doXhr=function(y,x){var T=this,L=s.addPath(y,"/info");this.xo=g._getReceiver(y,L,x),this.timeoutRef=setTimeout(function(){T._cleanup(!1),T.emit("finish")},g.timeout),this.xo.once("finish",function(k,j){T._cleanup(!0),T.emit("finish",k,j)})},g.prototype._cleanup=function(y){clearTimeout(this.timeoutRef),this.timeoutRef=null,!y&&this.xo&&this.xo.close(),this.xo=null},g.prototype.close=function(){this.removeAllListeners(),this._cleanup(!1)},g.timeout=8e3,m.exports=g},6:m=>{"use strict";m.exports=global.location||{origin:"http://localhost:80",protocol:"http:",host:"localhost",port:80,href:"http://localhost/",hash:""}},9084:(m,S,e)=>{"use strict";e(7015);var A,h=e(3711),l=e(1993),s=e(6944),o=e(196),a=e(2630),n=e(1059),r=e(2850),p=e(7824),d=e(9427),b=e(9697),g=e(6886),y=e(8551),x=e(6),T=e(4542),L=e(5714),k=e(4779);function v(u,c,w){if(!(this instanceof v))return new v(u,c,w);if(arguments.length<1)throw new TypeError("Failed to construct 'SockJS: 1 argument required, but only 0 present");y.call(this),this.readyState=v.CONNECTING,this.extensions="",this.protocol="",(w=w||{}).protocols_whitelist&&b.warn("'protocols_whitelist' is DEPRECATED. Use 'transports' instead."),this._transportsWhitelist=w.transports,this._transportOptions=w.transportOptions||{},this._timeout=w.timeout||0;var E=w.sessionId||8;if("function"==typeof E)this._generateSessionId=E;else{if("number"!=typeof E)throw new TypeError("If sessionId is used in the options, it needs to be a number or a function.");this._generateSessionId=function(){return s.string(E)}}this._server=w.server||s.numberString(1e3);var C=new h(u);if(!C.host||!C.protocol)throw new SyntaxError("The URL '"+u+"' is invalid");if(C.hash)throw new SyntaxError("The URL must not contain a fragment");if("http:"!==C.protocol&&"https:"!==C.protocol)throw new SyntaxError("The URL's scheme must be either 'http:' or 'https:'. '"+C.protocol+"' is not allowed.");if("https:"===x.protocol&&"https:"!==C.protocol&&!a.isLoopbackAddr(C.hostname))throw new Error("SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS");c?Array.isArray(c)||(c=[c]):c=[];var H=c.sort();H.forEach(function(t,i){if(!t)throw new SyntaxError("The protocols entry '"+t+"' is invalid.");if(i<H.length-1&&t===H[i+1])throw new SyntaxError("The protocols entry '"+t+"' is duplicated.")});var N=a.getOrigin(x.href);this._origin=N?N.toLowerCase():null,C.set("pathname",C.pathname.replace(/\/+$/,"")),this.url=C.href,this._urlInfo={nullOrigin:!d.hasDomain(),sameOrigin:a.isOriginEqual(this.url,x.href),sameScheme:a.isSchemeEqual(this.url,x.href)},this._ir=new k(this.url,this._urlInfo),this._ir.once("finish",this._receiveInfo.bind(this))}function O(u){return 1e3===u||u>=3e3&&u<=4999}l(v,y),v.prototype.close=function(u,c){if(u&&!O(u))throw new Error("InvalidAccessError: Invalid code");if(c&&c.length>123)throw new SyntaxError("reason argument has an invalid length");this.readyState!==v.CLOSING&&this.readyState!==v.CLOSED&&this._close(u||1e3,c||"Normal closure",!0)},v.prototype.send=function(u){if("string"!=typeof u&&(u=""+u),this.readyState===v.CONNECTING)throw new Error("InvalidStateError: The connection has not been established yet");this.readyState===v.OPEN&&this._transport.send(o.quote(u))},v.version=e(8469),v.CONNECTING=0,v.OPEN=1,v.CLOSING=2,v.CLOSED=3,v.prototype._receiveInfo=function(u,c){if(this._ir=null,u){this._rto=this.countRTO(c),this._transUrl=u.base_url?u.base_url:this.url,u=p.extend(u,this._urlInfo);var w=A.filterToEnabled(this._transportsWhitelist,u);this._transports=w.main,this._connect()}else this._close(1002,"Cannot connect to server")},v.prototype._connect=function(){for(var u=this._transports.shift();u;u=this._transports.shift()){if(u.needBody&&(!global.document.body||typeof global.document.readyState<"u"&&"complete"!==global.document.readyState&&"interactive"!==global.document.readyState))return this._transports.unshift(u),void n.attachEvent("load",this._connect.bind(this));var c=Math.max(this._timeout,this._rto*u.roundTrips||5e3);this._transportTimeoutId=setTimeout(this._transportTimeout.bind(this),c);var C=new u(a.addPath(this._transUrl,"/"+this._server+"/"+this._generateSessionId()),this._transUrl,this._transportOptions[u.transportName]);return C.on("message",this._transportMessage.bind(this)),C.once("close",this._transportClose.bind(this)),C.transportName=u.transportName,void(this._transport=C)}this._close(2e3,"All transports failed",!1)},v.prototype._transportTimeout=function(){this.readyState===v.CONNECTING&&(this._transport&&this._transport.close(),this._transportClose(2007,"Transport timed out"))},v.prototype._transportMessage=function(u){var C,c=this,w=u.slice(0,1),E=u.slice(1);switch(w){case"o":return void this._open();case"h":return void this.dispatchEvent(new g("heartbeat"))}if(E)try{C=JSON.parse(E)}catch{}if(!(typeof C>"u"))switch(w){case"a":Array.isArray(C)&&C.forEach(function(R){c.dispatchEvent(new L(R))});break;case"m":this.dispatchEvent(new L(C));break;case"c":Array.isArray(C)&&2===C.length&&this._close(C[0],C[1],!0)}},v.prototype._transportClose=function(u,c){this._transport&&(this._transport.removeAllListeners(),this._transport=null,this.transport=null),O(u)||2e3===u||this.readyState!==v.CONNECTING?this._close(u,c):this._connect()},v.prototype._open=function(){this.readyState===v.CONNECTING?(this._transportTimeoutId&&(clearTimeout(this._transportTimeoutId),this._transportTimeoutId=null),this.readyState=v.OPEN,this.transport=this._transport.transportName,this.dispatchEvent(new g("open"))):this._close(1006,"Server lost session")},v.prototype._close=function(u,c,w){var E=!1;if(this._ir&&(E=!0,this._ir.close(),this._ir=null),this._transport&&(this._transport.close(),this._transport=null,this.transport=null),this.readyState===v.CLOSED)throw new Error("InvalidStateError: SockJS has already been closed");this.readyState=v.CLOSING,setTimeout(function(){this.readyState=v.CLOSED,E&&this.dispatchEvent(new g("error"));var C=new T("close");C.wasClean=w||!1,C.code=u||1e3,C.reason=c,this.dispatchEvent(C),this.onmessage=this.onclose=this.onerror=null}.bind(this),0)},v.prototype.countRTO=function(u){return u>100?4*u:300+u},m.exports=function(u){return A=r(u),e(7958)(v,u),v}},7015:()=>{"use strict";var p,m=Array.prototype,S=Object.prototype,e=Function.prototype,h=String.prototype,l=m.slice,s=S.toString,o=function(u){return"[object Function]"===S.toString.call(u)},n=function(c){return"[object String]"===s.call(c)},r=Object.defineProperty&&function(){try{return Object.defineProperty({},"x",{}),!0}catch{return!1}}();p=r?function(u,c,w,E){!E&&c in u||Object.defineProperty(u,c,{configurable:!0,enumerable:!1,writable:!0,value:w})}:function(u,c,w,E){!E&&c in u||(u[c]=w)};var d=function(u,c,w){for(var E in c)S.hasOwnProperty.call(c,E)&&p(u,E,c[E],w)},b=function(u){if(null==u)throw new TypeError("can't convert "+u+" to object");return Object(u)};function x(){}d(e,{bind:function(c){var w=this;if(!o(w))throw new TypeError("Function.prototype.bind called on incompatible "+w);for(var E=l.call(arguments,1),R=Math.max(0,w.length-E.length),H=[],N=0;N<R;N++)H.push("$"+N);var t=Function("binder","return function ("+H.join(",")+"){ return binder.apply(this, arguments); }")(function(){if(this instanceof t){var i=w.apply(this,E.concat(l.call(arguments)));return Object(i)===i?i:this}return w.apply(c,E.concat(l.call(arguments)))});return w.prototype&&(x.prototype=w.prototype,t.prototype=new x,x.prototype=null),t}}),d(Array,{isArray:function(c){return"[object Array]"===s.call(c)}});var c,w,E,T=Object("a"),L="a"!==T[0]||!(0 in T);d(m,{forEach:function(c){var w=b(this),E=L&&n(this)?this.split(""):w,C=arguments[1],R=-1,H=E.length>>>0;if(!o(c))throw new TypeError;for(;++R<H;)R in E&&c.call(C,E[R],R,w)}},(w=!0,E=!0,(c=m.forEach)&&(c.call("foo",function(C,R,H){"object"!=typeof H&&(w=!1)}),c.call([1],function(){E="string"==typeof this},"x")),!(c&&w&&E)));var j=Array.prototype.indexOf&&-1!==[0,1].indexOf(1,2);d(m,{indexOf:function(c){var w=L&&n(this)?this.split(""):b(this),E=w.length>>>0;if(!E)return-1;var C=0;for(arguments.length>1&&(C=function g(u){var c=+u;return c!=c?c=0:0!==c&&c!==1/0&&c!==-1/0&&(c=(c>0||-1)*Math.floor(Math.abs(c))),c}(arguments[1])),C=C>=0?C:Math.max(0,E+C);C<E;C++)if(C in w&&w[C]===c)return C;return-1}},j);var u,A=h.split;2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||"t"==="tesst".split(/(s)*/)[1]||4!=="test".split(/(?:)/,-1).length||"".split(/.?/).length||".".split(/()()/).length>1?(u=void 0===/()??/.exec("")[1],h.split=function(c,w){var E=this;if(void 0===c&&0===w)return[];if("[object RegExp]"!==s.call(c))return A.call(this,c,w);var N,t,i,f,C=[],R=(c.ignoreCase?"i":"")+(c.multiline?"m":"")+(c.extended?"x":"")+(c.sticky?"y":""),H=0;for(c=new RegExp(c.source,R+"g"),E+="",u||(N=new RegExp("^"+c.source+"$(?!\\s)",R)),w=void 0===w?-1>>>0:function y(u){return u>>>0}(w);(t=c.exec(E))&&!((i=t.index+t[0].length)>H&&(C.push(E.slice(H,t.index)),!u&&t.length>1&&t[0].replace(N,function(){for(var I=1;I<arguments.length-2;I++)void 0===arguments[I]&&(t[I]=void 0)}),t.length>1&&t.index<E.length&&m.push.apply(C,t.slice(1)),f=t[0].length,H=i,C.length>=w));)c.lastIndex===t.index&&c.lastIndex++;return H===E.length?(f||!c.test(""))&&C.push(""):C.push(E.slice(H)),C.length>w?C.slice(0,w):C}):"0".split(void 0,0).length&&(h.split=function(c,w){return void 0===c&&0===w?[]:A.call(this,c,w)});var v=h.substr;d(h,{substr:function(c,w){return v.call(this,c<0&&(c=this.length+c)<0?0:c,w)}},"".substr&&"b"!=="0b".substr(-1))},1049:(m,S,e)=>{"use strict";m.exports=[e(1240),e(9720),e(3356),e(6178),e(5568)(e(6178)),e(8940),e(5568)(e(8940)),e(5543),e(5483),e(5568)(e(5543)),e(9143)]},5067:(m,S,e)=>{"use strict";var h=e(6964).b,l=e(1993),s=e(1059),o=e(2630),a=global.XMLHttpRequest;function r(b,g,y,x){var T=this;h.call(this),setTimeout(function(){T._start(b,g,y,x)},0)}l(r,h),r.prototype._start=function(b,g,y,x){var T=this;try{this.xhr=new a}catch{}if(!this.xhr)return this.emit("finish",0,"no xhr support"),void this._cleanup();g=o.addQuery(g,"t="+ +new Date),this.unloadRef=s.unloadAdd(function(){T._cleanup(!0)});try{this.xhr.open(b,g,!0),this.timeout&&"timeout"in this.xhr&&(this.xhr.timeout=this.timeout,this.xhr.ontimeout=function(){T.emit("finish",0,""),T._cleanup(!1)})}catch(k){return this.emit("finish",0,""),void this._cleanup(!1)}if((!x||!x.noCredentials)&&r.supportsCORS&&(this.xhr.withCredentials=!0),x&&x.headers)for(var L in x.headers)this.xhr.setRequestHeader(L,x.headers[L]);this.xhr.onreadystatechange=function(){if(T.xhr){var j,A,k=T.xhr;switch(k.readyState){case 3:try{A=k.status,j=k.responseText}catch{}1223===A&&(A=204),200===A&&j&&j.length>0&&T.emit("chunk",A,j);break;case 4:1223===(A=k.status)&&(A=204),(12005===A||12029===A)&&(A=0),T.emit("finish",A,k.responseText),T._cleanup(!1)}}};try{T.xhr.send(y)}catch{T.emit("finish",0,""),T._cleanup(!1)}},r.prototype._cleanup=function(b){if(this.xhr){if(this.removeAllListeners(),s.unloadDel(this.unloadRef),this.xhr.onreadystatechange=function(){},this.xhr.ontimeout&&(this.xhr.ontimeout=null),b)try{this.xhr.abort()}catch{}this.unloadRef=this.xhr=null}},r.prototype.close=function(){this._cleanup(!0)},r.enabled=!!a;var p=["Active"].concat("Object").join("X");!r.enabled&&p in global&&(r.enabled=!!new(a=function(){try{return new global[p]("Microsoft.XMLHTTP")}catch{return null}}));var d=!1;try{d="withCredentials"in new a}catch{}r.supportsCORS=d,m.exports=r},3521:m=>{m.exports=global.EventSource},6419:m=>{"use strict";var S=global.WebSocket||global.MozWebSocket;m.exports=S?function(h){return new S(h)}:void 0},6178:(m,S,e)=>{"use strict";var h=e(1993),l=e(6599),s=e(3024),o=e(53),a=e(3521);function n(r){if(!n.enabled())throw new Error("Transport created when disabled");l.call(this,r,"/eventsource",s,o)}h(n,l),n.enabled=function(){return!!a},n.transportName="eventsource",n.roundTrips=2,m.exports=n},8940:(m,S,e)=>{"use strict";var h=e(1993),l=e(90),s=e(9781),o=e(6599);function a(n){if(!l.enabled)throw new Error("Transport created when disabled");o.call(this,n,"/htmlfile",l,s)}h(a,o),a.enabled=function(n){return l.enabled&&n.sameOrigin},a.transportName="htmlfile",a.roundTrips=2,m.exports=a},6531:(m,S,e)=>{"use strict";var h=e(1993),l=e(6964).b,s=e(8469),o=e(2630),a=e(9915),n=e(1059),r=e(6944);function d(b,g,y){if(!d.enabled())throw new Error("Transport created when disabled");l.call(this);var x=this;this.origin=o.getOrigin(y),this.baseUrl=y,this.transUrl=g,this.transport=b,this.windowId=r.string(8);var T=o.addPath(y,"/iframe.html")+"#"+this.windowId;this.iframeObj=a.createIframe(T,function(L){x.emit("close",1006,"Unable to load an iframe ("+L+")"),x.close()}),this.onmessageCallback=this._message.bind(this),n.attachEvent("message",this.onmessageCallback)}h(d,l),d.prototype.close=function(){if(this.removeAllListeners(),this.iframeObj){n.detachEvent("message",this.onmessageCallback);try{this.postMessage("c")}catch{}this.iframeObj.cleanup(),this.iframeObj=null,this.onmessageCallback=this.iframeObj=null}},d.prototype._message=function(b){if(o.isOriginEqual(b.origin,this.origin)){var g;try{g=JSON.parse(b.data)}catch{return}if(g.windowId===this.windowId)switch(g.type){case"s":this.iframeObj.loaded(),this.postMessage("s",JSON.stringify([s,this.transport,this.transUrl,this.baseUrl]));break;case"t":this.emit("message",g.data);break;case"c":var y;try{y=JSON.parse(g.data)}catch{return}this.emit("close",y[0],y[1]),this.close()}}},d.prototype.postMessage=function(b,g){this.iframeObj.post(JSON.stringify({windowId:this.windowId,type:b,data:g||""}),this.origin)},d.prototype.send=function(b){this.postMessage("m",b)},d.enabled=function(){return a.iframeEnabled},d.transportName="iframe",d.roundTrips=2,m.exports=d},9143:(m,S,e)=>{"use strict";var h=e(1993),l=e(5222),s=e(979),o=e(1759);function a(n){if(!a.enabled())throw new Error("Transport created when disabled");l.call(this,n,"/jsonp",o,s)}h(a,l),a.enabled=function(){return!!global.document},a.transportName="jsonp-polling",a.roundTrips=1,a.needBody=!0,m.exports=a},6599:(m,S,e)=>{"use strict";var h=e(1993),l=e(2630),s=e(5222);function n(r,p,d,b){s.call(this,r,p,function a(r){return function(p,d,b){var g={};"string"==typeof d&&(g.headers={"Content-type":"text/plain"});var y=l.addPath(p,"/xhr_send"),x=new r("POST",y,d,g);return x.once("finish",function(T){if(x=null,200!==T&&204!==T)return b(new Error("http status "+T));b()}),function(){x.close(),x=null;var T=new Error("Aborted");T.code=1e3,b(T)}}}(b),d,b)}h(n,s),m.exports=n},5624:(m,S,e)=>{"use strict";var h=e(1993),l=e(6964).b;function o(a,n){l.call(this),this.sendBuffer=[],this.sender=n,this.url=a}h(o,l),o.prototype.send=function(a){this.sendBuffer.push(a),this.sendStop||this.sendSchedule()},o.prototype.sendScheduleWait=function(){var n,a=this;this.sendStop=function(){a.sendStop=null,clearTimeout(n)},n=setTimeout(function(){a.sendStop=null,a.sendSchedule()},25)},o.prototype.sendSchedule=function(){var a=this;if(this.sendBuffer.length>0){var n="["+this.sendBuffer.join(",")+"]";this.sendStop=this.sender(this.url,n,function(r){a.sendStop=null,r?(a.emit("close",r.code||1006,"Sending error: "+r),a.close()):a.sendScheduleWait()}),this.sendBuffer=[]}},o.prototype._cleanup=function(){this.removeAllListeners()},o.prototype.close=function(){this._cleanup(),this.sendStop&&(this.sendStop(),this.sendStop=null)},m.exports=o},5568:(m,S,e)=>{"use strict";var h=e(1993),l=e(6531),s=e(7824);m.exports=function(o){function a(n,r){l.call(this,o.transportName,n,r)}return h(a,l),a.enabled=function(n,r){if(!global.document)return!1;var p=s.extend({},r);return p.sameOrigin=!0,o.enabled(p)&&l.enabled()},a.transportName="iframe-"+o.transportName,a.needBody=!0,a.roundTrips=l.roundTrips+o.roundTrips-1,a.facadeTransport=o,a}},3500:(m,S,e)=>{"use strict";var h=e(1993),l=e(6964).b;function o(a,n,r){l.call(this),this.Receiver=a,this.receiveUrl=n,this.AjaxObject=r,this._scheduleReceiver()}h(o,l),o.prototype._scheduleReceiver=function(){var a=this,n=this.poll=new this.Receiver(this.receiveUrl,this.AjaxObject);n.on("message",function(r){a.emit("message",r)}),n.once("close",function(r,p){a.poll=n=null,a.pollIsClosing||("network"===p?a._scheduleReceiver():(a.emit("close",r||1006,p),a.removeAllListeners()))})},o.prototype.abort=function(){this.removeAllListeners(),this.pollIsClosing=!0,this.poll&&this.poll.abort()},m.exports=o},5222:(m,S,e)=>{"use strict";var h=e(1993),l=e(2630),s=e(5624),o=e(3500);function n(r,p,d,b,g){var y=l.addPath(r,p),x=this;s.call(this,r,d),this.poll=new o(b,y,g),this.poll.on("message",function(T){x.emit("message",T)}),this.poll.once("close",function(T,L){x.poll=null,x.emit("close",T,L),x.close()})}h(n,s),n.prototype.close=function(){s.prototype.close.call(this),this.removeAllListeners(),this.poll&&(this.poll.abort(),this.poll=null)},m.exports=n},3024:(m,S,e)=>{"use strict";var h=e(1993),l=e(6964).b,s=e(3521);function a(n){l.call(this);var r=this,p=this.es=new s(n);p.onmessage=function(d){r.emit("message",decodeURI(d.data))},p.onerror=function(d){var b=2!==p.readyState?"network":"permanent";r._cleanup(),r._close(b)}}h(a,l),a.prototype.abort=function(){this._cleanup(),this._close("user")},a.prototype._cleanup=function(){var n=this.es;n&&(n.onmessage=n.onerror=null,n.close(),this.es=null)},a.prototype._close=function(n){var r=this;setTimeout(function(){r.emit("close",null,n),r.removeAllListeners()},200)},m.exports=a},90:(m,S,e)=>{"use strict";var h=e(1993),l=e(9915),s=e(2630),o=e(6964).b,a=e(6944);function r(d){o.call(this);var b=this;l.polluteGlobalNamespace(),this.id="a"+a.string(6),d=s.addQuery(d,"c="+decodeURIComponent(l.WPrefix+"."+this.id));var g=r.htmlfileEnabled?l.createHtmlfile:l.createIframe;global[l.WPrefix][this.id]={start:function(){b.iframeObj.loaded()},message:function(y){b.emit("message",y)},stop:function(){b._cleanup(),b._close("network")}},this.iframeObj=g(d,function(){b._cleanup(),b._close("permanent")})}h(r,o),r.prototype.abort=function(){this._cleanup(),this._close("user")},r.prototype._cleanup=function(){this.iframeObj&&(this.iframeObj.cleanup(),this.iframeObj=null),delete global[l.WPrefix][this.id]},r.prototype._close=function(d){this.emit("close",null,d),this.removeAllListeners()},r.htmlfileEnabled=!1;var p=["Active"].concat("Object").join("X");if(p in global)try{r.htmlfileEnabled=!!new global[p]("htmlfile")}catch{}r.enabled=r.htmlfileEnabled||l.iframeEnabled,m.exports=r},979:(m,S,e)=>{"use strict";var h=e(9915),l=e(6944),s=e(9427),o=e(2630),a=e(1993),n=e(6964).b;function p(d){var b=this;n.call(this),h.polluteGlobalNamespace(),this.id="a"+l.string(6);var g=o.addQuery(d,"c="+encodeURIComponent(h.WPrefix+"."+this.id));global[h.WPrefix][this.id]=this._callback.bind(this),this._createScript(g),this.timeoutId=setTimeout(function(){b._abort(new Error("JSONP script loaded abnormally (timeout)"))},p.timeout)}a(p,n),p.prototype.abort=function(){if(global[h.WPrefix][this.id]){var d=new Error("JSONP user aborted read");d.code=1e3,this._abort(d)}},p.timeout=35e3,p.scriptErrorTimeout=1e3,p.prototype._callback=function(d){this._cleanup(),!this.aborting&&(d&&this.emit("message",d),this.emit("close",null,"network"),this.removeAllListeners())},p.prototype._abort=function(d){this._cleanup(),this.aborting=!0,this.emit("close",d.code,d.message),this.removeAllListeners()},p.prototype._cleanup=function(){if(clearTimeout(this.timeoutId),this.script2&&(this.script2.parentNode.removeChild(this.script2),this.script2=null),this.script){var d=this.script;d.parentNode.removeChild(d),d.onreadystatechange=d.onerror=d.onload=d.onclick=null,this.script=null}delete global[h.WPrefix][this.id]},p.prototype._scriptError=function(){var d=this;this.errorTimer||(this.errorTimer=setTimeout(function(){d.loadedOkay||d._abort(new Error("JSONP script loaded abnormally (onerror)"))},p.scriptErrorTimeout))},p.prototype._createScript=function(d){var y,b=this,g=this.script=global.document.createElement("script");if(g.id="a"+l.string(8),g.src=d,g.type="text/javascript",g.charset="UTF-8",g.onerror=this._scriptError.bind(this),g.onload=function(){b._abort(new Error("JSONP script loaded abnormally (onload)"))},g.onreadystatechange=function(){if(/loaded|closed/.test(g.readyState)){if(g&&g.htmlFor&&g.onclick){b.loadedOkay=!0;try{g.onclick()}catch{}}g&&b._abort(new Error("JSONP script loaded abnormally (onreadystatechange)"))}},typeof g.async>"u"&&global.document.attachEvent)if(s.isOpera())(y=this.script2=global.document.createElement("script")).text="try{var a = document.getElementById('"+g.id+"'); if(a)a.onerror();}catch(x){};",g.async=y.async=!1;else{try{g.htmlFor=g.id,g.event="onclick"}catch{}g.async=!0}typeof g.async<"u"&&(g.async=!0);var x=global.document.getElementsByTagName("head")[0];x.insertBefore(g,x.firstChild),y&&x.insertBefore(y,x.firstChild)},m.exports=p},5847:(m,S,e)=>{"use strict";var h=e(1993),l=e(6964).b;function o(a,n){l.call(this);var r=this;this.bufferPosition=0,this.xo=new n("POST",a,null),this.xo.on("chunk",this._chunkHandler.bind(this)),this.xo.once("finish",function(p,d){r._chunkHandler(p,d),r.xo=null,r.emit("close",null,200===p?"network":"permanent"),r._cleanup()})}h(o,l),o.prototype._chunkHandler=function(a,n){if(200===a&&n)for(var r=-1;;this.bufferPosition+=r+1){var p=n.slice(this.bufferPosition);if(-1===(r=p.indexOf("\n")))break;var d=p.slice(0,r);d&&this.emit("message",d)}},o.prototype._cleanup=function(){this.removeAllListeners()},o.prototype.abort=function(){this.xo&&(this.xo.close(),this.emit("close",null,"user"),this.xo=null),this._cleanup()},m.exports=o},1759:(m,S,e)=>{"use strict";var o,a,h=e(6944),l=e(2630);m.exports=function(p,d,b){o||function r(){(o=global.document.createElement("form")).style.display="none",o.style.position="absolute",o.method="POST",o.enctype="application/x-www-form-urlencoded",o.acceptCharset="UTF-8",(a=global.document.createElement("textarea")).name="d",o.appendChild(a),global.document.body.appendChild(o)}();var g="a"+h.string(8);o.target=g,o.action=l.addQuery(l.addPath(p,"/jsonp_send"),"i="+g);var y=function n(p){try{return global.document.createElement('<iframe name="'+p+'">')}catch{var d=global.document.createElement("iframe");return d.name=p,d}}(g);y.id=g,y.style.display="none",o.appendChild(y);try{a.value=d}catch{}o.submit();var x=function(T){y.onerror&&(y.onreadystatechange=y.onerror=y.onload=null,setTimeout(function(){y.parentNode.removeChild(y),y=null},500),a.value="",b(T))};return y.onerror=function(){x()},y.onload=function(){x()},y.onreadystatechange=function(T){"complete"===y.readyState&&x()},function(){x(new Error("Aborted"))}}},3103:(m,S,e)=>{"use strict";var h=e(6964).b,l=e(1993),s=e(1059),o=e(9427),a=e(2630);function r(p,d,b){var g=this;h.call(this),setTimeout(function(){g._start(p,d,b)},0)}l(r,h),r.prototype._start=function(p,d,b){var g=this,y=new global.XDomainRequest;d=a.addQuery(d,"t="+ +new Date),y.onerror=function(){g._error()},y.ontimeout=function(){g._error()},y.onprogress=function(){g.emit("chunk",200,y.responseText)},y.onload=function(){g.emit("finish",200,y.responseText),g._cleanup(!1)},this.xdr=y,this.unloadRef=s.unloadAdd(function(){g._cleanup(!0)});try{this.xdr.open(p,d),this.timeout&&(this.xdr.timeout=this.timeout),this.xdr.send(b)}catch{this._error()}},r.prototype._error=function(){this.emit("finish",0,""),this._cleanup(!1)},r.prototype._cleanup=function(p){if(this.xdr){if(this.removeAllListeners(),s.unloadDel(this.unloadRef),this.xdr.ontimeout=this.xdr.onerror=this.xdr.onprogress=this.xdr.onload=null,p)try{this.xdr.abort()}catch{}this.unloadRef=this.xdr=null}},r.prototype.close=function(){this._cleanup(!0)},r.enabled=!(!global.XDomainRequest||!o.hasDomain()),m.exports=r},53:(m,S,e)=>{"use strict";var h=e(1993),l=e(5067);function s(o,a,n,r){l.call(this,o,a,n,r)}h(s,l),s.enabled=l.enabled&&l.supportsCORS,m.exports=s},1749:(m,S,e)=>{"use strict";var h=e(6964).b;function s(){var o=this;h.call(this),this.to=setTimeout(function(){o.emit("finish",200,"{}")},s.timeout)}e(1993)(s,h),s.prototype.close=function(){clearTimeout(this.to)},s.timeout=2e3,m.exports=s},9781:(m,S,e)=>{"use strict";var h=e(1993),l=e(5067);function s(o,a,n){l.call(this,o,a,n,{noCredentials:!0})}h(s,l),s.enabled=l.enabled,m.exports=s},1240:(m,S,e)=>{"use strict";var h=e(1059),l=e(2630),s=e(1993),o=e(6964).b,a=e(6419);function r(p,d,b){if(!r.enabled())throw new Error("Transport created when disabled");o.call(this);var g=this,y=l.addPath(p,"/websocket");y="https"===y.slice(0,5)?"wss"+y.slice(5):"ws"+y.slice(4),this.url=y,this.ws=new a(this.url,[],b),this.ws.onmessage=function(x){g.emit("message",x.data)},this.unloadRef=h.unloadAdd(function(){g.ws.close()}),this.ws.onclose=function(x){g.emit("close",x.code,x.reason),g._cleanup()},this.ws.onerror=function(x){g.emit("close",1006,"WebSocket connection broken"),g._cleanup()}}s(r,o),r.prototype.send=function(p){this.ws.send("["+p+"]")},r.prototype.close=function(){var p=this.ws;this._cleanup(),p&&p.close()},r.prototype._cleanup=function(){var p=this.ws;p&&(p.onmessage=p.onclose=p.onerror=null),h.unloadDel(this.unloadRef),this.unloadRef=this.ws=null,this.removeAllListeners()},r.enabled=function(){return!!a},r.transportName="websocket",r.roundTrips=2,m.exports=r},5483:(m,S,e)=>{"use strict";var h=e(1993),l=e(6599),s=e(3356),o=e(5847),a=e(3103);function n(r){if(!a.enabled)throw new Error("Transport created when disabled");l.call(this,r,"/xhr",o,a)}h(n,l),n.enabled=s.enabled,n.transportName="xdr-polling",n.roundTrips=2,m.exports=n},3356:(m,S,e)=>{"use strict";var h=e(1993),l=e(6599),s=e(5847),o=e(3103);function a(n){if(!o.enabled)throw new Error("Transport created when disabled");l.call(this,n,"/xhr_streaming",s,o)}h(a,l),a.enabled=function(n){return!n.cookie_needed&&!n.nullOrigin&&o.enabled&&n.sameScheme},a.transportName="xdr-streaming",a.roundTrips=2,m.exports=a},5543:(m,S,e)=>{"use strict";var h=e(1993),l=e(6599),s=e(5847),o=e(53),a=e(9781);function n(r){if(!a.enabled&&!o.enabled)throw new Error("Transport created when disabled");l.call(this,r,"/xhr",s,o)}h(n,l),n.enabled=function(r){return!r.nullOrigin&&(!(!a.enabled||!r.sameOrigin)||o.enabled)},n.transportName="xhr-polling",n.roundTrips=2,m.exports=n},9720:(m,S,e)=>{"use strict";var h=e(1993),l=e(6599),s=e(5847),o=e(53),a=e(9781),n=e(9427);function r(p){if(!a.enabled&&!o.enabled)throw new Error("Transport created when disabled");l.call(this,p,"/xhr_streaming",s,o)}h(r,l),r.enabled=function(p){return!p.nullOrigin&&!n.isOpera()&&o.enabled},r.transportName="xhr-streaming",r.roundTrips=2,r.needBody=!!global.document,m.exports=r},5893:m=>{"use strict";m.exports.randomBytes=global.crypto&&global.crypto.getRandomValues?function(S){var e=new Uint8Array(S);return global.crypto.getRandomValues(e),e}:function(S){for(var e=new Array(S),h=0;h<S;h++)e[h]=Math.floor(256*Math.random());return e}},9427:m=>{"use strict";m.exports={isOpera:function(){return global.navigator&&/opera/i.test(global.navigator.userAgent)},isKonqueror:function(){return global.navigator&&/konqueror/i.test(global.navigator.userAgent)},hasDomain:function(){if(!global.document)return!0;try{return!!global.document.domain}catch{return!1}}}},196:m=>{"use strict";var e,S=/[\x00-\x1f\ud800-\udfff\ufffe\uffff\u0300-\u0333\u033d-\u0346\u034a-\u034c\u0350-\u0352\u0357-\u0358\u035c-\u0362\u0374\u037e\u0387\u0591-\u05af\u05c4\u0610-\u0617\u0653-\u0654\u0657-\u065b\u065d-\u065e\u06df-\u06e2\u06eb-\u06ec\u0730\u0732-\u0733\u0735-\u0736\u073a\u073d\u073f-\u0741\u0743\u0745\u0747\u07eb-\u07f1\u0951\u0958-\u095f\u09dc-\u09dd\u09df\u0a33\u0a36\u0a59-\u0a5b\u0a5e\u0b5c-\u0b5d\u0e38-\u0e39\u0f43\u0f4d\u0f52\u0f57\u0f5c\u0f69\u0f72-\u0f76\u0f78\u0f80-\u0f83\u0f93\u0f9d\u0fa2\u0fa7\u0fac\u0fb9\u1939-\u193a\u1a17\u1b6b\u1cda-\u1cdb\u1dc0-\u1dcf\u1dfc\u1dfe\u1f71\u1f73\u1f75\u1f77\u1f79\u1f7b\u1f7d\u1fbb\u1fbe\u1fc9\u1fcb\u1fd3\u1fdb\u1fe3\u1feb\u1fee-\u1fef\u1ff9\u1ffb\u1ffd\u2000-\u2001\u20d0-\u20d1\u20d4-\u20d7\u20e7-\u20e9\u2126\u212a-\u212b\u2329-\u232a\u2adc\u302b-\u302c\uaab2-\uaab3\uf900-\ufa0d\ufa10\ufa12\ufa15-\ufa1e\ufa20\ufa22\ufa25-\ufa26\ufa2a-\ufa2d\ufa30-\ufa6d\ufa70-\ufad9\ufb1d\ufb1f\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufb4e\ufff0-\uffff]/g;m.exports={quote:function(l){var s=JSON.stringify(l);return S.lastIndex=0,S.test(s)?(e||(e=function(l){var s,o={},a=[];for(s=0;s<65536;s++)a.push(String.fromCharCode(s));return l.lastIndex=0,a.join("").replace(l,function(n){return o[n]="\\u"+("0000"+n.charCodeAt(0).toString(16)).slice(-4),""}),l.lastIndex=0,o}(S)),s.replace(S,function(o){return e[o]})):s}}},1059:(m,S,e)=>{"use strict";var h=e(6944),l={},s=!1,o=global.chrome&&global.chrome.app&&global.chrome.app.runtime;m.exports={attachEvent:function(n,r){typeof global.addEventListener<"u"?global.addEventListener(n,r,!1):global.document&&global.attachEvent&&(global.document.attachEvent("on"+n,r),global.attachEvent("on"+n,r))},detachEvent:function(n,r){typeof global.addEventListener<"u"?global.removeEventListener(n,r,!1):global.document&&global.detachEvent&&(global.document.detachEvent("on"+n,r),global.detachEvent("on"+n,r))},unloadAdd:function(n){if(o)return null;var r=h.string(8);return l[r]=n,s&&setTimeout(this.triggerUnloadCallbacks,0),r},unloadDel:function(n){n in l&&delete l[n]},triggerUnloadCallbacks:function(){for(var n in l)l[n](),delete l[n]}},o||m.exports.attachEvent("unload",function(){s||(s=!0,m.exports.triggerUnloadCallbacks())})},9915:(m,S,e)=>{"use strict";var h=e(1059),l=e(9427);m.exports={WPrefix:"_jp",currentWindowId:null,polluteGlobalNamespace:function(){m.exports.WPrefix in global||(global[m.exports.WPrefix]={})},postMessage:function(o,a){global.parent!==global&&global.parent.postMessage(JSON.stringify({windowId:m.exports.currentWindowId,type:o,data:a||""}),"*")},createIframe:function(o,a){var r,p,n=global.document.createElement("iframe"),d=function(){clearTimeout(r);try{n.onload=null}catch{}n.onerror=null},b=function(){n&&(d(),setTimeout(function(){n&&n.parentNode.removeChild(n),n=null},0),h.unloadDel(p))},g=function(x){n&&(b(),a(x))};return n.src=o,n.style.display="none",n.style.position="absolute",n.onerror=function(){g("onerror")},n.onload=function(){clearTimeout(r),r=setTimeout(function(){g("onload timeout")},2e3)},global.document.body.appendChild(n),r=setTimeout(function(){g("timeout")},15e3),p=h.unloadAdd(b),{post:function(x,T){setTimeout(function(){try{n&&n.contentWindow&&n.contentWindow.postMessage(x,T)}catch{}},0)},cleanup:b,loaded:d}},createHtmlfile:function(o,a){var p,d,b,n=["Active"].concat("Object").join("X"),r=new global[n]("htmlfile"),g=function(){clearTimeout(p),b.onerror=null},y=function(){r&&(g(),h.unloadDel(d),b.parentNode.removeChild(b),b=r=null,CollectGarbage())},x=function(k){r&&(y(),a(k))};r.open(),r.write('<html><script>document.domain="'+global.document.domain+'";<\/script></html>'),r.close(),r.parentWindow[m.exports.WPrefix]=global[m.exports.WPrefix];var L=r.createElement("div");return r.body.appendChild(L),b=r.createElement("iframe"),L.appendChild(b),b.src=o,b.onerror=function(){x("onerror")},p=setTimeout(function(){x("timeout")},15e3),d=h.unloadAdd(y),{post:function(k,j){try{setTimeout(function(){b&&b.contentWindow&&b.contentWindow.postMessage(k,j)},0)}catch{}},cleanup:y,loaded:g}}},m.exports.iframeEnabled=!1,global.document&&(m.exports.iframeEnabled=("function"==typeof global.postMessage||"object"==typeof global.postMessage)&&!l.isKonqueror())},9697:m=>{"use strict";var S={};["log","debug","warn"].forEach(function(e){var h;try{h=global.console&&global.console[e]&&global.console[e].apply}catch{}S[e]=h?function(){return global.console[e].apply(global.console,arguments)}:"log"===e?function(){}:S.log}),m.exports=S},7824:m=>{"use strict";m.exports={isObject:function(S){var e=typeof S;return"function"===e||"object"===e&&!!S},extend:function(S){if(!this.isObject(S))return S;for(var e,h,l=1,s=arguments.length;l<s;l++)for(h in e=arguments[l])Object.prototype.hasOwnProperty.call(e,h)&&(S[h]=e[h]);return S}}},6944:(m,S,e)=>{"use strict";var h=e(5893);m.exports={string:function(s){for(var a=h.randomBytes(s),n=[],r=0;r<s;r++)n.push("abcdefghijklmnopqrstuvwxyz012345".substr(a[r]%32,1));return n.join("")},number:function(s){return Math.floor(Math.random()*s)},numberString:function(s){var o=(""+(s-1)).length;return(new Array(o+1).join("0")+this.number(s)).slice(-o)}}},2850:m=>{"use strict";m.exports=function(e){return{filterToEnabled:function(h,l){var s={main:[],facade:[]};return h?"string"==typeof h&&(h=[h]):h=[],e.forEach(function(o){if(o){if("websocket"===o.transportName&&!1===l.websocket)return;if(h.length&&-1===h.indexOf(o.transportName))return;o.enabled(l)&&(s.main.push(o),o.facadeTransport&&s.facade.push(o.facadeTransport))}}),s}}}},2630:(m,S,e)=>{"use strict";var h=e(3711);m.exports={getOrigin:function(s){if(!s)return null;var o=new h(s);if("file:"===o.protocol)return null;var a=o.port;return a||(a="https:"===o.protocol?"443":"80"),o.protocol+"//"+o.hostname+":"+a},isOriginEqual:function(s,o){return this.getOrigin(s)===this.getOrigin(o)},isSchemeEqual:function(s,o){return s.split(":")[0]===o.split(":")[0]},addPath:function(s,o){var a=s.split("?");return a[0]+o+(a[1]?"?"+a[1]:"")},addQuery:function(s,o){return s+(-1===s.indexOf("?")?"?"+o:"&"+o)},isLoopbackAddr:function(s){return/^127\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})$/i.test(s)||/^\[::1\]$/.test(s)}}},8469:m=>{m.exports="1.6.1"},3711:(m,S,e)=>{"use strict";var h=e(5852),l=e(8629),s=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,o=/[\n\r\t]/g,a=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,n=/:\d+$/,r=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,p=/^[a-zA-Z]:/;function d(v){return(v||"").toString().replace(s,"")}var b=[["#","hash"],["?","query"],function(O,u){return x(u.protocol)?O.replace(/\\/g,"/"):O},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],g={hash:1,query:1};function y(v){var O;O=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};var E,c={},w=typeof(v=v||O.location||{});if("blob:"===v.protocol)c=new k(unescape(v.pathname),{});else if("string"===w)for(E in c=new k(v,{}),g)delete c[E];else if("object"===w){for(E in v)E in g||(c[E]=v[E]);void 0===c.slashes&&(c.slashes=a.test(v.href))}return c}function x(v){return"file:"===v||"ftp:"===v||"http:"===v||"https:"===v||"ws:"===v||"wss:"===v}function T(v,O){v=(v=d(v)).replace(o,""),O=O||{};var R,u=r.exec(v),c=u[1]?u[1].toLowerCase():"",w=!!u[2],E=!!u[3],C=0;return w?E?(R=u[2]+u[3]+u[4],C=u[2].length+u[3].length):(R=u[2]+u[4],C=u[2].length):E?(R=u[3]+u[4],C=u[3].length):R=u[4],"file:"===c?C>=2&&(R=R.slice(2)):x(c)?R=u[4]:c?w&&(R=R.slice(2)):C>=2&&x(O.protocol)&&(R=u[4]),{protocol:c,slashes:w||x(c),slashesCount:C,rest:R}}function k(v,O,u){if(v=(v=d(v)).replace(o,""),!(this instanceof k))return new k(v,O,u);var c,w,E,C,R,H,N=b.slice(),t=typeof O,i=this,f=0;for("object"!==t&&"string"!==t&&(u=O,O=null),u&&"function"!=typeof u&&(u=l.parse),c=!(w=T(v||"",O=y(O))).protocol&&!w.slashes,i.slashes=w.slashes||c&&O.slashes,i.protocol=w.protocol||O.protocol||"",v=w.rest,("file:"===w.protocol&&(2!==w.slashesCount||p.test(v))||!w.slashes&&(w.protocol||w.slashesCount<2||!x(i.protocol)))&&(N[3]=[/(.*)/,"pathname"]);f<N.length;f++)"function"!=typeof(C=N[f])?(H=C[1],(E=C[0])!=E?i[H]=v:"string"==typeof E?~(R="@"===E?v.lastIndexOf(E):v.indexOf(E))&&("number"==typeof C[2]?(i[H]=v.slice(0,R),v=v.slice(R+C[2])):(i[H]=v.slice(R),v=v.slice(0,R))):(R=E.exec(v))&&(i[H]=R[1],v=v.slice(0,R.index)),i[H]=i[H]||c&&C[3]&&O[H]||"",C[4]&&(i[H]=i[H].toLowerCase())):v=C(v,i);u&&(i.query=u(i.query)),c&&O.slashes&&"/"!==i.pathname.charAt(0)&&(""!==i.pathname||""!==O.pathname)&&(i.pathname=function L(v,O){if(""===v)return O;for(var u=(O||"/").split("/").slice(0,-1).concat(v.split("/")),c=u.length,w=u[c-1],E=!1,C=0;c--;)"."===u[c]?u.splice(c,1):".."===u[c]?(u.splice(c,1),C++):C&&(0===c&&(E=!0),u.splice(c,1),C--);return E&&u.unshift(""),("."===w||".."===w)&&u.push(""),u.join("/")}(i.pathname,O.pathname)),"/"!==i.pathname.charAt(0)&&x(i.protocol)&&(i.pathname="/"+i.pathname),h(i.port,i.protocol)||(i.host=i.hostname,i.port=""),i.username=i.password="",i.auth&&(~(R=i.auth.indexOf(":"))?(i.username=i.auth.slice(0,R),i.username=encodeURIComponent(decodeURIComponent(i.username)),i.password=i.auth.slice(R+1),i.password=encodeURIComponent(decodeURIComponent(i.password))):i.username=encodeURIComponent(decodeURIComponent(i.auth)),i.auth=i.password?i.username+":"+i.password:i.username),i.origin="file:"!==i.protocol&&x(i.protocol)&&i.host?i.protocol+"//"+i.host:"null",i.href=i.toString()}k.prototype={set:function j(v,O,u){var c=this;switch(v){case"query":"string"==typeof O&&O.length&&(O=(u||l.parse)(O)),c[v]=O;break;case"port":c[v]=O,h(O,c.protocol)?O&&(c.host=c.hostname+":"+O):(c.host=c.hostname,c[v]="");break;case"hostname":c[v]=O,c.port&&(O+=":"+c.port),c.host=O;break;case"host":c[v]=O,n.test(O)?(O=O.split(":"),c.port=O.pop(),c.hostname=O.join(":")):(c.hostname=O,c.port="");break;case"protocol":c.protocol=O.toLowerCase(),c.slashes=!u;break;case"pathname":case"hash":if(O){var w="pathname"===v?"/":"#";c[v]=O.charAt(0)!==w?w+O:O}else c[v]=O;break;case"username":case"password":c[v]=encodeURIComponent(O);break;case"auth":var E=O.indexOf(":");~E?(c.username=O.slice(0,E),c.username=encodeURIComponent(decodeURIComponent(c.username)),c.password=O.slice(E+1),c.password=encodeURIComponent(decodeURIComponent(c.password))):c.username=encodeURIComponent(decodeURIComponent(O))}for(var C=0;C<b.length;C++){var R=b[C];R[4]&&(c[R[1]]=c[R[1]].toLowerCase())}return c.auth=c.password?c.username+":"+c.password:c.username,c.origin="file:"!==c.protocol&&x(c.protocol)&&c.host?c.protocol+"//"+c.host:"null",c.href=c.toString(),c},toString:function A(v){(!v||"function"!=typeof v)&&(v=l.stringify);var O,u=this,c=u.host,w=u.protocol;w&&":"!==w.charAt(w.length-1)&&(w+=":");var E=w+(u.protocol&&u.slashes||x(u.protocol)?"//":"");return u.username?(E+=u.username,u.password&&(E+=":"+u.password),E+="@"):u.password?(E+=":"+u.password,E+="@"):"file:"!==u.protocol&&x(u.protocol)&&!c&&"/"!==u.pathname&&(E+="@"),(":"===c[c.length-1]||n.test(u.hostname)&&!u.port)&&(c+=":"),E+=c+u.pathname,(O="object"==typeof u.query?v(u.query):u.query)&&(E+="?"!==O.charAt(0)?"?"+O:O),u.hash&&(E+=u.hash),E}},k.extractProtocol=T,k.location=y,k.trimLeft=d,k.qs=l,m.exports=k}}]);