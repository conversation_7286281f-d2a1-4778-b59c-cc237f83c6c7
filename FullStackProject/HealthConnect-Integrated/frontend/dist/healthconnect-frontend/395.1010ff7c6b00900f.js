"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[395],{8395:(S,d,a)=>{a.r(d),a.d(d,{AuthModule:()=>D});var l=a(6448),p=a(3887),o=a(4341),e=a(540),m=a(8010),c=a(177);function f(t,s){if(1&t&&(e.j41(0,"div",24),e.nrm(1,"i",25),e.EFF(2),e.k0s()),2&t){const r=e.XpG();e.R7$(2),e.SpI(" ",r.errorMessage," ")}}function u(t,s){1&t&&(e.j41(0,"div",26)(1,"div",27)(2,"span",28),e.EFF(3,"Loading..."),e.k0s()()())}function g(t,s){if(1&t&&(e.j41(0,"div",46),e.EFF(1),e.k0s()),2&t){const r=e.XpG(2);e.R7$(1),e.SpI(" ",r.getFieldError("email")," ")}}function h(t,s){if(1&t&&(e.j41(0,"div",46),e.EFF(1),e.k0s()),2&t){const r=e.XpG(2);e.R7$(1),e.SpI(" ",r.getFieldError("password")," ")}}function b(t,s){1&t&&e.nrm(0,"span",47)}function F(t,s){1&t&&(e.j41(0,"span"),e.EFF(1,"Signing In..."),e.k0s())}function v(t,s){1&t&&(e.j41(0,"span"),e.EFF(1,"Sign In"),e.k0s())}function k(t,s){if(1&t){const r=e.RV6();e.j41(0,"form",29),e.bIt("ngSubmit",function(){e.eBV(r);const n=e.XpG();return e.Njj(n.onSubmit())}),e.j41(1,"div",30)(2,"label",31),e.EFF(3,"Email Address"),e.k0s(),e.j41(4,"div",32)(5,"span",33),e.nrm(6,"i",34),e.k0s(),e.nrm(7,"input",35),e.k0s(),e.DNE(8,g,2,1,"div",36),e.k0s(),e.j41(9,"div",30)(10,"label",37),e.EFF(11,"Password"),e.k0s(),e.j41(12,"div",32)(13,"span",33),e.nrm(14,"i",38),e.k0s(),e.nrm(15,"input",39),e.k0s(),e.DNE(16,h,2,1,"div",36),e.k0s(),e.j41(17,"div",40),e.nrm(18,"input",41),e.j41(19,"label",42),e.EFF(20," Remember me "),e.k0s()(),e.j41(21,"button",43),e.DNE(22,b,1,0,"span",44),e.DNE(23,F,2,0,"span",45),e.DNE(24,v,2,0,"span",45),e.k0s()()}if(2&t){const r=e.XpG();e.Y8G("formGroup",r.loginForm),e.R7$(7),e.AVh("is-invalid",r.isFieldInvalid("email")),e.R7$(1),e.Y8G("ngIf",r.isFieldInvalid("email")),e.R7$(7),e.AVh("is-invalid",r.isFieldInvalid("password")),e.R7$(1),e.Y8G("ngIf",r.isFieldInvalid("password")),e.R7$(5),e.Y8G("disabled",r.isLoading),e.R7$(1),e.Y8G("ngIf",r.isLoading),e.R7$(1),e.Y8G("ngIf",r.isLoading),e.R7$(1),e.Y8G("ngIf",!r.isLoading)}}function x(t,s){if(1&t&&(e.j41(0,"div",15),e.nrm(1,"i",16),e.EFF(2),e.k0s()),2&t){const r=e.XpG();e.R7$(2),e.SpI(" ",r.successMessage," ")}}function E(t,s){if(1&t&&(e.j41(0,"div",17),e.nrm(1,"i",18),e.EFF(2),e.k0s()),2&t){const r=e.XpG();e.R7$(2),e.SpI(" ",r.errorMessage," ")}}function C(t,s){1&t&&(e.j41(0,"div",19)(1,"div",20)(2,"span",21),e.EFF(3,"Loading..."),e.k0s()(),e.j41(4,"p",22),e.EFF(5,"Initializing form..."),e.k0s()())}function y(t,s){if(1&t&&(e.j41(0,"div",57),e.EFF(1),e.k0s()),2&t){const r=e.XpG(2);e.R7$(1),e.SpI(" ",r.getFieldError("fullName")," ")}}function I(t,s){if(1&t&&(e.j41(0,"div",57),e.EFF(1),e.k0s()),2&t){const r=e.XpG(2);e.R7$(1),e.SpI(" ",r.getFieldError("email")," ")}}function j(t,s){if(1&t&&(e.j41(0,"div",57),e.EFF(1),e.k0s()),2&t){const r=e.XpG(2);e.R7$(1),e.SpI(" ",r.getFieldError("password")," ")}}function w(t,s){if(1&t&&(e.j41(0,"div",57),e.EFF(1),e.k0s()),2&t){const r=e.XpG(2);e.R7$(1),e.SpI(" ",r.getFieldError("confirmPassword")," ")}}function R(t,s){if(1&t&&(e.j41(0,"div",57),e.EFF(1),e.k0s()),2&t){const r=e.XpG(3);e.R7$(1),e.SpI(" ",r.getFieldError("specialization")," ")}}function N(t,s){if(1&t&&(e.j41(0,"div",57),e.EFF(1),e.k0s()),2&t){const r=e.XpG(3);e.R7$(1),e.SpI(" ",r.getFieldError("licenseNumber")," ")}}function P(t,s){if(1&t&&(e.j41(0,"div",58)(1,"h6",59),e.nrm(2,"i",34),e.EFF(3,"Doctor Information "),e.k0s(),e.j41(4,"div",26)(5,"div",41)(6,"label",60),e.EFF(7,"Specialization *"),e.k0s(),e.nrm(8,"input",61),e.DNE(9,R,2,1,"div",38),e.k0s(),e.j41(10,"div",41)(11,"label",62),e.EFF(12,"License Number *"),e.k0s(),e.nrm(13,"input",63),e.DNE(14,N,2,1,"div",38),e.k0s(),e.j41(15,"div",41)(16,"label",64),e.EFF(17,"Hospital/Clinic"),e.k0s(),e.nrm(18,"input",65),e.k0s(),e.j41(19,"div",41)(20,"label",66),e.EFF(21,"Years of Experience"),e.k0s(),e.nrm(22,"input",67),e.k0s()()()),2&t){const r=e.XpG(2);e.R7$(8),e.AVh("is-invalid",r.isFieldInvalid("specialization")),e.R7$(1),e.Y8G("ngIf",r.isFieldInvalid("specialization")),e.R7$(4),e.AVh("is-invalid",r.isFieldInvalid("licenseNumber")),e.R7$(1),e.Y8G("ngIf",r.isFieldInvalid("licenseNumber"))}}function T(t,s){1&t&&e.nrm(0,"span",68)}function M(t,s){1&t&&(e.j41(0,"span"),e.EFF(1,"Creating Account..."),e.k0s())}function G(t,s){1&t&&(e.j41(0,"span"),e.EFF(1,"Create Account"),e.k0s())}function $(t,s){if(1&t){const r=e.RV6();e.j41(0,"form",23),e.bIt("ngSubmit",function(){e.eBV(r);const n=e.XpG();return e.Njj(n.onSubmit())}),e.j41(1,"div",24)(2,"label",25),e.EFF(3,"I am a:"),e.k0s(),e.j41(4,"div",26)(5,"div",27)(6,"div",28),e.nrm(7,"input",29),e.j41(8,"label",30),e.nrm(9,"i",31),e.EFF(10,"Patient "),e.k0s()()(),e.j41(11,"div",27)(12,"div",28),e.nrm(13,"input",32),e.j41(14,"label",33),e.nrm(15,"i",34),e.EFF(16,"Doctor "),e.k0s()()()()(),e.j41(17,"div",26)(18,"div",35)(19,"label",36),e.EFF(20,"Full Name *"),e.k0s(),e.nrm(21,"input",37),e.DNE(22,y,2,1,"div",38),e.k0s(),e.j41(23,"div",35)(24,"label",39),e.EFF(25,"Email Address *"),e.k0s(),e.nrm(26,"input",40),e.DNE(27,I,2,1,"div",38),e.k0s(),e.j41(28,"div",41)(29,"label",42),e.EFF(30,"Password *"),e.k0s(),e.nrm(31,"input",43),e.DNE(32,j,2,1,"div",38),e.k0s(),e.j41(33,"div",41)(34,"label",44),e.EFF(35,"Confirm Password *"),e.k0s(),e.nrm(36,"input",45),e.DNE(37,w,2,1,"div",38),e.k0s()(),e.DNE(38,P,23,6,"div",46),e.j41(39,"div",47)(40,"h6",48),e.nrm(41,"i",49),e.EFF(42,"Contact Information (Optional) "),e.k0s(),e.j41(43,"div",26)(44,"div",41)(45,"label",50),e.EFF(46,"Phone Number"),e.k0s(),e.nrm(47,"input",51),e.k0s(),e.j41(48,"div",41)(49,"label",52),e.EFF(50,"Address"),e.k0s(),e.nrm(51,"input",53),e.k0s()()(),e.j41(52,"button",54),e.DNE(53,T,1,0,"span",55),e.DNE(54,M,2,0,"span",56),e.DNE(55,G,2,0,"span",56),e.k0s()()}if(2&t){const r=e.XpG();e.Y8G("formGroup",r.registerForm),e.R7$(21),e.AVh("is-invalid",r.isFieldInvalid("fullName")),e.R7$(1),e.Y8G("ngIf",r.isFieldInvalid("fullName")),e.R7$(4),e.AVh("is-invalid",r.isFieldInvalid("email")),e.R7$(1),e.Y8G("ngIf",r.isFieldInvalid("email")),e.R7$(4),e.AVh("is-invalid",r.isFieldInvalid("password")),e.R7$(1),e.Y8G("ngIf",r.isFieldInvalid("password")),e.R7$(4),e.AVh("is-invalid",r.isFieldInvalid("confirmPassword")),e.R7$(1),e.Y8G("ngIf",r.isFieldInvalid("confirmPassword")),e.R7$(1),e.Y8G("ngIf","DOCTOR"===r.selectedRole),e.R7$(14),e.Y8G("disabled",r.isLoading),e.R7$(1),e.Y8G("ngIf",r.isLoading),e.R7$(1),e.Y8G("ngIf",r.isLoading),e.R7$(1),e.Y8G("ngIf",!r.isLoading)}}const O=[{path:"",redirectTo:"login",pathMatch:"full"},{path:"login",component:(()=>{class t{constructor(r,i,n,L){this.formBuilder=r,this.authService=i,this.router=n,this.route=L,this.isLoading=!1,this.errorMessage="",this.returnUrl=""}ngOnInit(){this.returnUrl=this.route.snapshot.queryParams.returnUrl||"/",this.authService.isAuthenticated()?this.redirectToDashboard():this.initializeForm()}initializeForm(){this.loginForm=this.formBuilder.group({email:["",[o.k0.required,o.k0.email]],password:["",[o.k0.required,o.k0.minLength(6)]],rememberMe:[!1]})}onSubmit(){this.loginForm&&!this.loginForm.invalid?(this.isLoading=!0,this.errorMessage="",this.authService.login(this.loginForm.value).subscribe({next:i=>{this.isLoading=!1,this.redirectToDashboard()},error:i=>{this.isLoading=!1,this.errorMessage=i.message||"Login failed. Please try again."}})):this.markFormGroupTouched()}redirectToDashboard(){const r=this.authService.getCurrentUser();r&&this.router.navigate("DOCTOR"===r.role?["/doctor/dashboard"]:"PATIENT"===r.role?["/patient/dashboard"]:["/"])}markFormGroupTouched(){this.loginForm&&Object.keys(this.loginForm.controls).forEach(r=>{this.loginForm.get(r)?.markAsTouched()})}get email(){return this.loginForm?.get("email")}get password(){return this.loginForm?.get("password")}isFieldInvalid(r){if(!this.loginForm)return!1;const i=this.loginForm.get(r);return!(!i||!i.invalid||!i.dirty&&!i.touched)}getFieldError(r){if(!this.loginForm)return"";const i=this.loginForm.get(r);if(i?.errors){if(i.errors.required)return`${r.charAt(0).toUpperCase()+r.slice(1)} is required`;if(i.errors.email)return"Please enter a valid email address";if(i.errors.minlength)return`${r.charAt(0).toUpperCase()+r.slice(1)} must be at least ${i.errors.minlength.requiredLength} characters`}return""}static{this.\u0275fac=function(i){return new(i||t)(e.rXU(o.ok),e.rXU(m.u),e.rXU(l.Ix),e.rXU(l.nX))}}static{this.\u0275cmp=e.VBU({type:t,selectors:[["app-login"]],decls:44,vars:3,consts:[[1,"container-fluid","vh-100"],[1,"row","h-100"],[1,"col-md-6","d-none","d-md-flex","align-items-center","justify-content-center","bg-primary","text-white"],[1,"text-center"],[1,"bi","bi-heart-pulse","display-1","mb-4"],[1,"display-4","fw-bold","mb-3"],[1,"lead"],[1,"mt-4"],[1,"d-flex","align-items-center","justify-content-center","mb-2"],[1,"bi","bi-check-circle","me-2"],[1,"d-flex","align-items-center","justify-content-center"],[1,"col-md-6","d-flex","align-items-center","justify-content-center"],[1,"w-100",2,"max-width","400px"],[1,"text-center","mb-4"],[1,"fw-bold","text-dark"],[1,"text-muted"],["class","alert alert-danger","role","alert",4,"ngIf"],["class","text-center py-4",4,"ngIf"],["novalidate","",3,"formGroup","ngSubmit",4,"ngIf"],[1,"text-center","mt-4"],["routerLink","/auth/register",1,"text-primary","text-decoration-none","fw-semibold"],[1,"mt-4","p-3","bg-light","rounded"],[1,"fw-semibold","mb-2"],[1,"text-muted","d-block"],["role","alert",1,"alert","alert-danger"],[1,"bi","bi-exclamation-triangle","me-2"],[1,"text-center","py-4"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],["novalidate","",3,"formGroup","ngSubmit"],[1,"mb-3"],["for","email",1,"form-label"],[1,"input-group"],[1,"input-group-text"],[1,"bi","bi-envelope"],["type","email","id","email","formControlName","email","placeholder","Enter your email",1,"form-control"],["class","invalid-feedback",4,"ngIf"],["for","password",1,"form-label"],[1,"bi","bi-lock"],["type","password","id","password","formControlName","password","placeholder","Enter your password",1,"form-control"],[1,"mb-3","form-check"],["type","checkbox","id","rememberMe","formControlName","rememberMe",1,"form-check-input"],["for","rememberMe",1,"form-check-label"],["type","submit",1,"btn","btn-primary","w-100","py-2",3,"disabled"],["class","spinner-border spinner-border-sm me-2","role","status",4,"ngIf"],[4,"ngIf"],[1,"invalid-feedback"],["role","status",1,"spinner-border","spinner-border-sm","me-2"]],template:function(i,n){1&i&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3),e.nrm(4,"i",4),e.j41(5,"h1",5),e.EFF(6,"HealthConnect"),e.k0s(),e.j41(7,"p",6),e.EFF(8,"Your trusted medical platform for seamless healthcare management"),e.k0s(),e.j41(9,"div",7)(10,"div",8),e.nrm(11,"i",9),e.j41(12,"span"),e.EFF(13,"Secure Patient-Doctor Communication"),e.k0s()(),e.j41(14,"div",8),e.nrm(15,"i",9),e.j41(16,"span"),e.EFF(17,"Easy Appointment Scheduling"),e.k0s()(),e.j41(18,"div",10),e.nrm(19,"i",9),e.j41(20,"span"),e.EFF(21,"AI-Powered Health Assistance"),e.k0s()()()()(),e.j41(22,"div",11)(23,"div",12)(24,"div",13)(25,"h2",14),e.EFF(26,"Welcome Back"),e.k0s(),e.j41(27,"p",15),e.EFF(28,"Sign in to your HealthConnect account"),e.k0s()(),e.DNE(29,f,3,1,"div",16),e.DNE(30,u,4,0,"div",17),e.DNE(31,k,25,11,"form",18),e.j41(32,"div",19)(33,"p",15),e.EFF(34," Don't have an account? "),e.j41(35,"a",20),e.EFF(36," Sign up here "),e.k0s()()(),e.j41(37,"div",21)(38,"h6",22),e.EFF(39,"Demo Credentials:"),e.k0s(),e.j41(40,"small",23),e.EFF(41,"Doctor: <EMAIL> / password123"),e.k0s(),e.j41(42,"small",23),e.EFF(43,"Patient: <EMAIL> / password123"),e.k0s()()()()()()),2&i&&(e.R7$(29),e.Y8G("ngIf",n.errorMessage),e.R7$(1),e.Y8G("ngIf",!n.loginForm),e.R7$(1),e.Y8G("ngIf",n.loginForm))},dependencies:[c.bT,o.qT,o.me,o.Zm,o.BC,o.cb,o.j4,o.JD,l.Wk],styles:[".vh-100[_ngcontent-%COMP%]{min-height:100vh}.bg-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#0d6efd 0%,#0b5ed7 100%)}.input-group-text[_ngcontent-%COMP%]{background-color:#f8f9fa;border-right:none}.form-control[_ngcontent-%COMP%]{border-left:none}.form-control[_ngcontent-%COMP%]:focus{box-shadow:none;border-color:#86b7fe}.form-control[_ngcontent-%COMP%]:focus + .input-group-text[_ngcontent-%COMP%]{border-color:#86b7fe}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#0d6efd 0%,#0b5ed7 100%);border:none;font-weight:500}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#0b5ed7 0%,#0a58ca 100%);transform:translateY(-1px);box-shadow:0 4px 8px #0d6efd4d}.btn-primary[_ngcontent-%COMP%]:disabled{background:#6c757d;transform:none;box-shadow:none}.alert[_ngcontent-%COMP%]{border:none;border-radius:8px}.form-check-input[_ngcontent-%COMP%]:checked{background-color:#0d6efd;border-color:#0d6efd}@media (max-width: 768px){.container-fluid[_ngcontent-%COMP%]{padding:1rem}.display-1[_ngcontent-%COMP%]{font-size:3rem}.display-4[_ngcontent-%COMP%]{font-size:2rem}}"]})}}return t})()},{path:"register",component:(()=>{class t{constructor(r,i,n){this.formBuilder=r,this.authService=i,this.router=n,this.isLoading=!1,this.errorMessage="",this.successMessage="",this.selectedRole="PATIENT"}ngOnInit(){this.authService.isAuthenticated()?this.redirectToDashboard():this.initializeForm()}initializeForm(){this.registerForm=this.formBuilder.group({fullName:["",[o.k0.required,o.k0.minLength(2),o.k0.maxLength(100)]],email:["",[o.k0.required,o.k0.email]],password:["",[o.k0.required,o.k0.minLength(6)]],confirmPassword:["",[o.k0.required]],role:["PATIENT",[o.k0.required]],phoneNumber:[""],address:[""],specialization:[""],licenseNumber:[""],affiliation:[""],yearsOfExperience:[""]},{validators:this.passwordMatchValidator}),this.registerForm.get("role")?.valueChanges.subscribe(r=>{this.selectedRole=r,this.updateValidators()})}passwordMatchValidator(r){const i=r.get("password"),n=r.get("confirmPassword");return i&&n&&i.value!==n.value?n.setErrors({passwordMismatch:!0}):n?.errors?.passwordMismatch&&(delete n.errors.passwordMismatch,0===Object.keys(n.errors).length&&n.setErrors(null)),null}updateValidators(){const r=this.registerForm.get("specialization"),i=this.registerForm.get("licenseNumber");"DOCTOR"===this.selectedRole?(r?.setValidators([o.k0.required]),i?.setValidators([o.k0.required])):(r?.clearValidators(),i?.clearValidators()),r?.updateValueAndValidity(),i?.updateValueAndValidity()}onSubmit(){this.registerForm&&!this.registerForm.invalid?(this.isLoading=!0,this.errorMessage="",this.successMessage="",this.authService.register(this.registerForm.value).subscribe({next:i=>{this.isLoading=!1,this.successMessage="Registration successful! Redirecting to dashboard...",setTimeout(()=>{this.redirectToDashboard()},2e3)},error:i=>{this.isLoading=!1,this.errorMessage=i.message||"Registration failed. Please try again."}})):this.markFormGroupTouched()}redirectToDashboard(){const r=this.authService.getCurrentUser();r&&this.router.navigate("DOCTOR"===r.role?["/doctor/dashboard"]:"PATIENT"===r.role?["/patient/dashboard"]:["/"])}markFormGroupTouched(){this.registerForm&&Object.keys(this.registerForm.controls).forEach(r=>{this.registerForm.get(r)?.markAsTouched()})}isFieldInvalid(r){if(!this.registerForm)return!1;const i=this.registerForm.get(r);return!(!i||!i.invalid||!i.dirty&&!i.touched)}getFieldError(r){if(!this.registerForm)return"";const i=this.registerForm.get(r);if(i?.errors){if(i.errors.required)return`${this.getFieldDisplayName(r)} is required`;if(i.errors.email)return"Please enter a valid email address";if(i.errors.minlength)return`${this.getFieldDisplayName(r)} must be at least ${i.errors.minlength.requiredLength} characters`;if(i.errors.maxlength)return`${this.getFieldDisplayName(r)} must not exceed ${i.errors.maxlength.requiredLength} characters`;if(i.errors.passwordMismatch)return"Passwords do not match"}return""}getFieldDisplayName(r){return{fullName:"Full Name",email:"Email",password:"Password",confirmPassword:"Confirm Password",phoneNumber:"Phone Number",specialization:"Specialization",licenseNumber:"License Number",affiliation:"Affiliation",yearsOfExperience:"Years of Experience"}[r]||r}static{this.\u0275fac=function(i){return new(i||t)(e.rXU(o.ok),e.rXU(m.u),e.rXU(l.Ix))}}static{this.\u0275cmp=e.VBU({type:t,selectors:[["app-register"]],decls:20,vars:4,consts:[[1,"container-fluid","min-vh-100","py-4"],[1,"row","justify-content-center"],[1,"col-md-8","col-lg-6"],[1,"card","shadow"],[1,"card-body","p-4"],[1,"text-center","mb-4"],[1,"bi","bi-heart-pulse","text-primary","display-4","mb-3"],[1,"fw-bold","text-dark"],[1,"text-muted"],["class","alert alert-success","role","alert",4,"ngIf"],["class","alert alert-danger","role","alert",4,"ngIf"],["class","text-center py-4",4,"ngIf"],["novalidate","",3,"formGroup","ngSubmit",4,"ngIf"],[1,"text-center","mt-4"],["routerLink","/auth/login",1,"text-primary","text-decoration-none","fw-semibold"],["role","alert",1,"alert","alert-success"],[1,"bi","bi-check-circle","me-2"],["role","alert",1,"alert","alert-danger"],[1,"bi","bi-exclamation-triangle","me-2"],[1,"text-center","py-4"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2","text-muted"],["novalidate","",3,"formGroup","ngSubmit"],[1,"mb-4"],[1,"form-label","fw-semibold"],[1,"row"],[1,"col-6"],[1,"form-check"],["type","radio","name","role","id","rolePatient","value","PATIENT","formControlName","role",1,"form-check-input"],["for","rolePatient",1,"form-check-label"],[1,"bi","bi-person","me-2"],["type","radio","name","role","id","roleDoctor","value","DOCTOR","formControlName","role",1,"form-check-input"],["for","roleDoctor",1,"form-check-label"],[1,"bi","bi-person-badge","me-2"],[1,"col-12","mb-3"],["for","fullName",1,"form-label"],["type","text","id","fullName","formControlName","fullName","placeholder","Enter your full name",1,"form-control"],["class","invalid-feedback",4,"ngIf"],["for","email",1,"form-label"],["type","email","id","email","formControlName","email","placeholder","Enter your email",1,"form-control"],[1,"col-md-6","mb-3"],["for","password",1,"form-label"],["type","password","id","password","formControlName","password","placeholder","Create a password",1,"form-control"],["for","confirmPassword",1,"form-label"],["type","password","id","confirmPassword","formControlName","confirmPassword","placeholder","Confirm your password",1,"form-control"],["class","border-top pt-3 mb-3",4,"ngIf"],[1,"border-top","pt-3","mb-4"],[1,"fw-semibold","mb-3"],[1,"bi","bi-telephone","me-2"],["for","phoneNumber",1,"form-label"],["type","tel","id","phoneNumber","formControlName","phoneNumber","placeholder","Your phone number",1,"form-control"],["for","address",1,"form-label"],["type","text","id","address","formControlName","address","placeholder","Your address",1,"form-control"],["type","submit",1,"btn","btn-primary","w-100","py-2",3,"disabled"],["class","spinner-border spinner-border-sm me-2","role","status",4,"ngIf"],[4,"ngIf"],[1,"invalid-feedback"],[1,"border-top","pt-3","mb-3"],[1,"fw-semibold","text-primary","mb-3"],["for","specialization",1,"form-label"],["type","text","id","specialization","formControlName","specialization","placeholder","e.g., Cardiology",1,"form-control"],["for","licenseNumber",1,"form-label"],["type","text","id","licenseNumber","formControlName","licenseNumber","placeholder","Medical license number",1,"form-control"],["for","affiliation",1,"form-label"],["type","text","id","affiliation","formControlName","affiliation","placeholder","Your workplace",1,"form-control"],["for","yearsOfExperience",1,"form-label"],["type","number","id","yearsOfExperience","formControlName","yearsOfExperience","placeholder","0","min","0","max","50",1,"form-control"],["role","status",1,"spinner-border","spinner-border-sm","me-2"]],template:function(i,n){1&i&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div",5),e.nrm(6,"i",6),e.j41(7,"h2",7),e.EFF(8,"Join HealthConnect"),e.k0s(),e.j41(9,"p",8),e.EFF(10,"Create your account to get started"),e.k0s()(),e.DNE(11,x,3,1,"div",9),e.DNE(12,E,3,1,"div",10),e.DNE(13,C,6,0,"div",11),e.DNE(14,$,56,18,"form",12),e.j41(15,"div",13)(16,"p",8),e.EFF(17," Already have an account? "),e.j41(18,"a",14),e.EFF(19," Sign in here "),e.k0s()()()()()()()()),2&i&&(e.R7$(11),e.Y8G("ngIf",n.successMessage),e.R7$(1),e.Y8G("ngIf",n.errorMessage),e.R7$(1),e.Y8G("ngIf",!n.registerForm),e.R7$(1),e.Y8G("ngIf",n.registerForm))},dependencies:[c.bT,o.qT,o.me,o.Q0,o.Fm,o.BC,o.cb,o.VZ,o.zX,o.j4,o.JD,l.Wk],styles:[".min-vh-100[_ngcontent-%COMP%]{min-height:100vh}.card[_ngcontent-%COMP%]{border:none;border-radius:12px;box-shadow:0 4px 6px #0000001a}.form-check-input[_ngcontent-%COMP%]:checked{background-color:#0d6efd;border-color:#0d6efd}.form-check-label[_ngcontent-%COMP%]{cursor:pointer;font-weight:500}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#0d6efd 0%,#0b5ed7 100%);border:none;font-weight:500;border-radius:8px}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#0b5ed7 0%,#0a58ca 100%);transform:translateY(-1px);box-shadow:0 4px 8px #0d6efd4d}.btn-primary[_ngcontent-%COMP%]:disabled{background:#6c757d;transform:none;box-shadow:none}.border-top[_ngcontent-%COMP%]{border-color:#e9ecef!important}.text-primary[_ngcontent-%COMP%]{color:#0d6efd!important}.alert[_ngcontent-%COMP%]{border:none;border-radius:8px}.form-control[_ngcontent-%COMP%]{border-radius:6px;border:1px solid #ced4da}.form-control[_ngcontent-%COMP%]:focus{border-color:#86b7fe;box-shadow:0 0 0 .25rem #0d6efd40}@media (max-width: 768px){.container-fluid[_ngcontent-%COMP%]{padding:1rem}.card-body[_ngcontent-%COMP%]{padding:2rem 1.5rem!important}}"]})}}return t})()}];let D=(()=>{class t{static{this.\u0275fac=function(i){return new(i||t)}}static{this.\u0275mod=e.$C({type:t})}static{this.\u0275inj=e.G2t({imports:[p.G,l.iI.forChild(O)]})}}return t})()}}]);