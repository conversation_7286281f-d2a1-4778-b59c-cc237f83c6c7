"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[440],{9440:(K,p,s)=>{s.r(p),s.d(p,{PatientModule:()=>Z});var m=s(6448),_=s(3887),f=s(2693),t=s(540),g=s(8010),b=s(3443),h=s(9545),u=s(8211),d=s(177),C=s(3285);function v(e,a){if(1&e&&(t.j41(0,"span",30),t.EFF(1),t.k0s()),2&e){const n=t.XpG().$implicit;t.R7$(1),t.SpI(" ",n.lastMessage.content.length>30?n.lastMessage.content.substring(0,30)+"...":n.lastMessage.content," ")}}function P(e,a){1&e&&(t.j41(0,"span",30),t.EFF(1,"No messages yet"),t.k0s())}function M(e,a){if(1&e&&(t.j41(0,"div",32),t.EFF(1),t.k0s()),2&e){const n=t.XpG().$implicit;t.R7$(1),t.SpI(" ",n.unreadCount," ")}}function k(e,a){if(1&e){const n=t.RV6();t.j41(0,"div",22),t.bIt("click",function(){const c=t.eBV(n).$implicit,r=t.XpG(2);return t.Njj(r.navigateToChat(c.id))}),t.j41(1,"div",23),t.nrm(2,"img",24),t.k0s(),t.j41(3,"div",25)(4,"div",26),t.EFF(5),t.k0s(),t.j41(6,"div",27),t.DNE(7,v,2,1,"span",28),t.DNE(8,P,2,0,"span",28),t.k0s()(),t.j41(9,"div",29)(10,"small",30),t.EFF(11),t.nI1(12,"date"),t.k0s(),t.DNE(13,M,2,1,"div",31),t.k0s()()}if(2&e){const n=a.$implicit,i=t.XpG(2);let o,c,r;t.R7$(2),t.Y8G("src",(null==(o=i.getOtherParticipant(n))?null:o.avatar)||"/assets/images/default-avatar.png",t.B4B)("alt",null==(c=i.getOtherParticipant(n))?null:c.fullName),t.R7$(3),t.JRh(null==(r=i.getOtherParticipant(n))?null:r.fullName),t.R7$(2),t.Y8G("ngIf",n.lastMessage),t.R7$(1),t.Y8G("ngIf",!n.lastMessage),t.R7$(3),t.SpI(" ",t.i5U(12,7,n.updatedAt,"short")," "),t.R7$(2),t.Y8G("ngIf",n.unreadCount>0)}}function O(e,a){if(1&e&&(t.j41(0,"div",17)(1,"h6",18),t.nrm(2,"i",19),t.EFF(3,"Recent Conversations "),t.k0s(),t.j41(4,"div",20),t.DNE(5,k,14,10,"div",21),t.k0s()()),2&e){const n=t.XpG();t.R7$(5),t.Y8G("ngForOf",n.recentChats)}}const x=function(e,a){return{appointmentId:e,doctorId:a,chatType:"PRE_APPOINTMENT",buttonText:"Pre-Chat",buttonClass:"btn-outline-info",size:"sm",showIcon:!1}},F=function(e){return{doctorId:e,chatType:"GENERAL",buttonText:"General",buttonClass:"btn-outline-primary",size:"sm",showIcon:!1}};function w(e,a){if(1&e&&(t.j41(0,"div",36)(1,"div",37)(2,"div",38)(3,"strong"),t.EFF(4),t.k0s()(),t.j41(5,"div",39)(6,"small",30),t.EFF(7),t.nI1(8,"date"),t.k0s()(),t.j41(9,"div",40)(10,"span",41),t.EFF(11),t.k0s()()(),t.j41(12,"div",42)(13,"div",43),t.nrm(14,"app-chat-access",44)(15,"app-chat-access",44),t.k0s()()()),2&e){const n=a.$implicit,i=t.XpG(2);t.R7$(4),t.JRh(n.doctor.fullName),t.R7$(3),t.Lme(" ",t.i5U(8,6,n.date,"mediumDate")," at ",n.startTime," "),t.R7$(4),t.SpI(" In ",i.getTimeUntilAppointment(n)," "),t.R7$(3),t.Y8G("config",t.l_i(9,x,n.id,n.doctor.id)),t.R7$(1),t.Y8G("config",t.eq3(12,F,n.doctor.id))}}function j(e,a){if(1&e&&(t.j41(0,"div")(1,"h6",18),t.nrm(2,"i",33),t.EFF(3,"Upcoming Appointments "),t.k0s(),t.j41(4,"div",34),t.DNE(5,w,16,14,"div",35),t.k0s()()),2&e){const n=t.XpG();t.R7$(5),t.Y8G("ngForOf",n.upcomingAppointments)}}function y(e,a){1&e&&(t.j41(0,"div",45)(1,"div",46),t.nrm(2,"i",47),t.j41(3,"h6",30),t.EFF(4,"No recent conversations"),t.k0s(),t.j41(5,"p",18),t.EFF(6,"Start chatting with your healthcare providers"),t.k0s(),t.j41(7,"button",48),t.nrm(8,"i",49),t.EFF(9," Book Appointment "),t.k0s()()())}const E=function(){return{chatType:"URGENT",buttonText:"Urgent",buttonClass:"btn-outline-danger",size:"sm"}};let R=(()=>{class e{constructor(n,i,o,c){this.chatService=n,this.appointmentService=i,this.authService=o,this.router=c,this.recentChats=[],this.upcomingAppointments=[],this.loading=!1}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.loadRecentChats(),this.loadUpcomingAppointments()}loadRecentChats(){this.chatService.getUserChats().subscribe({next:n=>{this.recentChats=n.slice(0,3)},error:n=>{console.error("Error loading recent chats:",n)}})}loadUpcomingAppointments(){"PATIENT"===this.currentUser?.role&&this.appointmentService.getPatientAppointments().subscribe({next:n=>{const i=new Date;this.upcomingAppointments=n.filter(o=>new Date(`${o.date}T${o.startTime}`)>i).slice(0,3)},error:n=>{console.error("Error loading appointments:",n)}})}navigateToChat(n){n?this.router.navigate(["/chat"],{queryParams:{chatId:n}}):this.router.navigate(["/chat"])}startAppointmentChat(n,i){this.appointmentService.createAppointmentChat(n.id,"PATIENT"===this.currentUser.role?n.doctor.id:n.patient.id,i,`${i.replace("_"," ")} discussion for appointment on ${n.date}`).subscribe({next:c=>{this.router.navigate(["/chat"],{queryParams:{chatId:c.id,appointmentId:n.id}})},error:c=>{console.error("Error creating appointment chat:",c)}})}isBeforeAppointment(n){return new Date(`${n.date}T${n.startTime}`)>new Date}getTimeUntilAppointment(n){const i=new Date(`${n.date}T${n.startTime}`),o=new Date,c=i.getTime()-o.getTime(),r=Math.floor(c/36e5),l=Math.floor(r/24);return l>0?`${l} day${l>1?"s":""}`:r>0?`${r} hour${r>1?"s":""}`:"Soon"}getOtherParticipant(n){return"PATIENT"===this.currentUser.role?n.doctor:n.patient}static{this.\u0275fac=function(i){return new(i||e)(t.rXU(u.m),t.rXU(h.h),t.rXU(g.u),t.rXU(m.Ix))}}static{this.\u0275cmp=t.VBU({type:e,selectors:[["app-quick-chat-widget"]],decls:21,vars:5,consts:[[1,"quick-chat-widget"],[1,"card"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"fas","fa-comments","text-primary","me-2"],["type","button",1,"btn","btn-sm","btn-outline-primary",3,"click"],[1,"fas","fa-external-link-alt","me-1"],[1,"card-body"],["class","mb-4",4,"ngIf"],[4,"ngIf"],["class","no-data",4,"ngIf"],[1,"quick-actions","mt-3","pt-3","border-top"],[1,"row","g-2"],[1,"col-6"],["type","button",1,"btn","btn-outline-success","btn-sm","w-100",3,"click"],[1,"fas","fa-plus","me-1"],[1,"w-100",3,"config"],[1,"mb-4"],[1,"text-muted","mb-3"],[1,"fas","fa-clock","me-2"],[1,"chat-list"],["class","chat-item",3,"click",4,"ngFor","ngForOf"],[1,"chat-item",3,"click"],[1,"chat-avatar"],[1,"rounded-circle",3,"src","alt"],[1,"chat-info"],[1,"chat-name"],[1,"chat-preview"],["class","text-muted",4,"ngIf"],[1,"chat-meta"],[1,"text-muted"],["class","badge bg-primary",4,"ngIf"],[1,"badge","bg-primary"],[1,"fas","fa-calendar-alt","me-2"],[1,"appointment-list"],["class","appointment-item",4,"ngFor","ngForOf"],[1,"appointment-item"],[1,"appointment-info"],[1,"appointment-doctor"],[1,"appointment-details"],[1,"appointment-time-left"],[1,"badge","bg-info"],[1,"appointment-actions"],["role","group",1,"btn-group-vertical"],[3,"config"],[1,"no-data"],[1,"text-center","py-4"],[1,"fas","fa-comments","fa-3x","text-muted","mb-3"],["type","button","routerLink","/appointments/book",1,"btn","btn-primary","btn-sm"],[1,"fas","fa-calendar-plus","me-2"]],template:function(i,o){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h6",3),t.nrm(4,"i",4),t.EFF(5," Quick Chat Access "),t.k0s(),t.j41(6,"button",5),t.bIt("click",function(){return o.navigateToChat()}),t.nrm(7,"i",6),t.EFF(8," View All "),t.k0s()(),t.j41(9,"div",7),t.DNE(10,O,6,1,"div",8),t.DNE(11,j,6,1,"div",9),t.DNE(12,y,10,0,"div",10),t.j41(13,"div",11)(14,"div",12)(15,"div",13)(16,"button",14),t.bIt("click",function(){return o.navigateToChat()}),t.nrm(17,"i",15),t.EFF(18," New Chat "),t.k0s()(),t.j41(19,"div",13),t.nrm(20,"app-chat-access",16),t.k0s()()()()()()),2&i&&(t.R7$(10),t.Y8G("ngIf",o.recentChats.length>0),t.R7$(1),t.Y8G("ngIf",o.upcomingAppointments.length>0),t.R7$(1),t.Y8G("ngIf",0===o.recentChats.length&&0===o.upcomingAppointments.length),t.R7$(8),t.Y8G("config",t.lJ4(4,E)))},dependencies:[d.Sq,d.bT,m.Wk,C.Q,d.vh],styles:[".quick-chat-widget[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{border:none;box-shadow:0 2px 8px #0000001a;border-radius:12px;overflow:hidden}.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff 0%,#0056b3 100%);color:#fff;border-bottom:none;padding:1rem 1.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#fff}.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]{border-color:#ffffff80;color:#fff}.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;border-color:#fff}.quick-chat-widget[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:1.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem;border-radius:8px;cursor:pointer;transition:all .2s ease;margin-bottom:.5rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;transform:translate(2px)}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]{flex-shrink:0;margin-right:.75rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:40px;height:40px;object-fit:cover}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-info[_ngcontent-%COMP%]{flex:1;min-width:0}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-info[_ngcontent-%COMP%]   .chat-name[_ngcontent-%COMP%]{font-weight:500;color:#212529;margin-bottom:.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-info[_ngcontent-%COMP%]   .chat-preview[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]{flex-shrink:0;text-align:right}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;margin-top:.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:1rem;border:1px solid #e9ecef;border-radius:8px;margin-bottom:.75rem;background:#f8f9fa}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]{flex:1}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]   .appointment-doctor[_ngcontent-%COMP%]{margin-bottom:.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]   .appointment-details[_ngcontent-%COMP%]{margin-bottom:.5rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]   .appointment-time-left[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]{flex-shrink:0;margin-left:1rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin-bottom:.25rem;font-size:.75rem;padding:.25rem .5rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child{margin-bottom:0}.quick-chat-widget[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]{text-align:center;padding:2rem 1rem}.quick-chat-widget[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.quick-chat-widget[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.875rem;padding:.5rem .75rem}.quick-chat-widget[_ngcontent-%COMP%]   h6.text-muted[_ngcontent-%COMP%]{font-size:.875rem;font-weight:600;text-transform:uppercase;letter-spacing:.5px;margin-bottom:1rem}.quick-chat-widget[_ngcontent-%COMP%]   h6.text-muted[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.7}@media (max-width: 768px){.quick-chat-widget[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:1rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{padding:.5rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;height:32px}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]{margin-left:0;margin-top:.75rem;width:100%}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]{flex-direction:row;width:100%}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{flex:1;margin-bottom:0;margin-right:.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child{margin-right:0}}.quick-chat-widget[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInUp .3s ease-out}@keyframes _ngcontent-%COMP%_slideInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}"]})}}return e})();var $=s(9339),T=s(4002);const D=["chatWindow"];function I(e,a){1&e&&(t.j41(0,"div",17)(1,"div",18)(2,"span",19),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",20),t.EFF(5,"Loading your dashboard..."),t.k0s()())}function G(e,a){if(1&e&&(t.j41(0,"div",21),t.nrm(1,"i",22),t.EFF(2),t.k0s()),2&e){const n=t.XpG();t.R7$(2),t.SpI(" ",n.error," ")}}function A(e,a){if(1&e&&(t.j41(0,"div",56)(1,"div",47)(2,"div",57),t.nrm(3,"i"),t.j41(4,"h6",58),t.EFF(5),t.k0s(),t.j41(6,"h4",59),t.EFF(7),t.j41(8,"small",60),t.EFF(9),t.k0s()(),t.j41(10,"span"),t.EFF(11),t.nI1(12,"titlecase"),t.k0s()()()()),2&e){const n=a.$implicit,i=t.XpG(2);t.R7$(3),t.STu("bi bi-",n.icon," display-6 ",n.color," mb-2"),t.R7$(2),t.JRh(n.name),t.R7$(2),t.SpI("",n.value," "),t.R7$(2),t.JRh(n.unit),t.R7$(1),t.HbH(i.getStatusBadgeClass(n.status)),t.R7$(1),t.JRh(t.bMT(12,10,n.status))}}function N(e,a){if(1&e){const n=t.RV6();t.j41(0,"div",56)(1,"div",61),t.bIt("click",function(){const c=t.eBV(n).$implicit,r=t.XpG(2);return t.Njj(r.navigateTo(c.route))}),t.j41(2,"div",57)(3,"div",62),t.nrm(4,"i"),t.k0s(),t.j41(5,"h6",58),t.EFF(6),t.k0s(),t.j41(7,"p",63),t.EFF(8),t.k0s()()()()}if(2&e){const n=a.$implicit;t.R7$(3),t.HbH(n.color),t.R7$(1),t.ZvI("bi bi-",n.icon," text-white fs-4"),t.R7$(2),t.JRh(n.title),t.R7$(2),t.JRh(n.description)}}function S(e,a){if(1&e){const n=t.RV6();t.j41(0,"div",64),t.nrm(1,"i",65),t.j41(2,"p"),t.EFF(3,"No upcoming appointments"),t.k0s(),t.j41(4,"button",66),t.bIt("click",function(){t.eBV(n);const o=t.XpG(2);return t.Njj(o.navigateTo("/appointments/book"))}),t.nrm(5,"i",67),t.EFF(6,"Book Appointment "),t.k0s()()}}function q(e,a){if(1&e){const n=t.RV6();t.j41(0,"div",68)(1,"div",69)(2,"div",70)(3,"div",71),t.nrm(4,"i"),t.k0s()(),t.j41(5,"div")(6,"h6",72),t.EFF(7),t.k0s(),t.j41(8,"p",73),t.nrm(9,"i",74),t.EFF(10),t.nrm(11,"i",75),t.EFF(12),t.k0s(),t.j41(13,"span"),t.EFF(14),t.nI1(15,"titlecase"),t.k0s()()(),t.j41(16,"div",76)(17,"button",38),t.bIt("click",function(){const c=t.eBV(n).$implicit,r=t.XpG(2);return t.Njj(r.navigateTo("/appointments/"+c.id))}),t.nrm(18,"i",77),t.EFF(19,"View Details "),t.k0s()()()}if(2&e){const n=a.$implicit,i=t.XpG(2);t.R7$(4),t.ZvI("bi bi-","VIDEO_CALL"===n.type?"camera-video":"geo-alt"," text-primary"),t.R7$(3),t.SpI("Dr. ",n.doctor.fullName,""),t.R7$(3),t.SpI("",n.date," "),t.R7$(2),t.SpI("",n.startTime," "),t.R7$(1),t.HbH(i.getStatusBadgeClass(n.status)),t.R7$(1),t.SpI(" ",t.bMT(15,9,n.status)," ")}}function U(e,a){if(1&e){const n=t.RV6();t.j41(0,"div",64),t.nrm(1,"i",78),t.j41(2,"p"),t.EFF(3,"No messages yet"),t.k0s(),t.j41(4,"button",66),t.bIt("click",function(){t.eBV(n);const o=t.XpG(2);return t.Njj(o.openChatModal())}),t.nrm(5,"i",79),t.EFF(6,"Start Conversation "),t.k0s()()}}function X(e,a){if(1&e&&(t.j41(0,"p",73),t.EFF(1),t.nI1(2,"slice"),t.k0s()),2&e){const n=t.XpG().$implicit;t.R7$(1),t.Lme(" ",t.brH(2,2,n.lastMessage.content,0,50),"",n.lastMessage.content.length>50?"...":""," ")}}function Y(e,a){if(1&e&&(t.j41(0,"small",60),t.EFF(1),t.k0s()),2&e){const n=t.XpG().$implicit,i=t.XpG(2);t.R7$(1),t.SpI(" ",i.formatChatTime(n.lastMessage.createdAt)," ")}}function B(e,a){if(1&e&&(t.j41(0,"span",86),t.EFF(1),t.k0s()),2&e){const n=t.XpG().$implicit;t.R7$(1),t.SpI(" ",n.unreadCount," ")}}function L(e,a){if(1&e){const n=t.RV6();t.j41(0,"div",80)(1,"div",69)(2,"div",70),t.nrm(3,"img",81),t.k0s(),t.j41(4,"div")(5,"h6",72),t.EFF(6),t.k0s(),t.DNE(7,X,3,6,"p",82),t.DNE(8,Y,2,1,"small",83),t.k0s()(),t.j41(9,"div",76),t.DNE(10,B,2,1,"span",84),t.j41(11,"button",38),t.bIt("click",function(){const c=t.eBV(n).$implicit,r=t.XpG(2);return t.Njj(r.openChat(c))}),t.nrm(12,"i",85),t.EFF(13,"Open "),t.k0s()()()}if(2&e){const n=a.$implicit;t.R7$(3),t.Y8G("src",n.doctor.avatar||"/assets/images/default-avatar.png",t.B4B)("alt",n.doctor.fullName),t.R7$(3),t.SpI("Dr. ",n.doctor.fullName,""),t.R7$(1),t.Y8G("ngIf",n.lastMessage),t.R7$(1),t.Y8G("ngIf",n.lastMessage),t.R7$(2),t.Y8G("ngIf",n.unreadCount>0)}}function V(e,a){1&e&&t.nrm(0,"hr",91)}function H(e,a){if(1&e&&(t.j41(0,"div",87)(1,"div",70)(2,"div",88),t.nrm(3,"i"),t.k0s()(),t.j41(4,"div",89)(5,"h6",72),t.EFF(6),t.k0s(),t.j41(7,"p",73),t.EFF(8),t.k0s(),t.j41(9,"small",60),t.EFF(10),t.k0s()(),t.DNE(11,V,1,0,"hr",90),t.k0s()),2&e){const n=a.$implicit,i=a.last;t.R7$(3),t.STu("bi bi-",n.icon," ",n.color,""),t.R7$(3),t.JRh(n.title),t.R7$(2),t.JRh(n.description),t.R7$(2),t.JRh(n.time),t.R7$(1),t.Y8G("ngIf",!i)}}function W(e,a){1&e&&t.nrm(0,"hr",91)}function Q(e,a){if(1&e&&(t.j41(0,"div",92)(1,"div",70)(2,"div",93),t.nrm(3,"i"),t.k0s()(),t.j41(4,"div",89)(5,"h6",72),t.EFF(6),t.k0s(),t.j41(7,"p",94),t.EFF(8),t.k0s()(),t.DNE(9,W,1,0,"hr",90),t.k0s()),2&e){const n=a.$implicit,i=a.last;t.R7$(3),t.ZvI("bi bi-",n.icon," text-primary"),t.R7$(3),t.JRh(n.title),t.R7$(2),t.JRh(n.description),t.R7$(1),t.Y8G("ngIf",!i)}}function J(e,a){if(1&e){const n=t.RV6();t.j41(0,"div")(1,"div",23)(2,"div",24)(3,"div",25)(4,"div")(5,"h1",26),t.EFF(6),t.k0s(),t.j41(7,"p",27),t.EFF(8,"Here's your health overview for today"),t.k0s()(),t.j41(9,"button",28),t.bIt("click",function(){t.eBV(n);const o=t.XpG();return t.Njj(o.refreshData())}),t.nrm(10,"i",29),t.EFF(11,"Refresh "),t.k0s()()()(),t.j41(12,"div",23)(13,"div",24)(14,"h5",30),t.nrm(15,"i",31),t.EFF(16,"Health Metrics "),t.k0s()(),t.DNE(17,A,13,12,"div",32),t.k0s(),t.j41(18,"div",23)(19,"div",24)(20,"h5",30),t.nrm(21,"i",33),t.EFF(22,"Quick Actions "),t.k0s()(),t.DNE(23,N,9,7,"div",32),t.k0s(),t.j41(24,"div",23)(25,"div",24)(26,"div",34)(27,"div",35)(28,"h6",36),t.nrm(29,"i",37),t.EFF(30,"Upcoming Appointments "),t.k0s(),t.j41(31,"button",38),t.bIt("click",function(){t.eBV(n);const o=t.XpG();return t.Njj(o.navigateTo("/appointments"))}),t.EFF(32," View All "),t.k0s()(),t.j41(33,"div",39),t.DNE(34,S,7,0,"div",40),t.DNE(35,q,20,11,"div",41),t.k0s()()()(),t.j41(36,"div",23)(37,"div",24)(38,"div",34)(39,"div",35)(40,"h6",36),t.nrm(41,"i",9),t.EFF(42,"Recent Messages "),t.k0s(),t.j41(43,"button",38),t.bIt("click",function(){t.eBV(n);const o=t.XpG();return t.Njj(o.openChatModal())}),t.nrm(44,"i",42),t.EFF(45,"New Message "),t.k0s()(),t.j41(46,"div",39),t.DNE(47,U,7,0,"div",40),t.DNE(48,L,14,6,"div",43),t.k0s()()()(),t.j41(49,"div",44)(50,"div",45),t.nrm(51,"app-quick-chat-widget"),t.k0s(),t.j41(52,"div",46)(53,"div",47)(54,"div",48)(55,"h6",36),t.nrm(56,"i",49),t.EFF(57,"Recent Activities "),t.k0s()(),t.j41(58,"div",39),t.DNE(59,H,12,8,"div",50),t.k0s()()(),t.j41(60,"div",46)(61,"div",47)(62,"div",48)(63,"h6",36),t.nrm(64,"i",51),t.EFF(65,"Health Tips "),t.k0s()(),t.j41(66,"div",39),t.DNE(67,Q,10,6,"div",52),t.k0s()()()(),t.j41(68,"div",44)(69,"div",24)(70,"div",53),t.nrm(71,"i",54),t.j41(72,"div")(73,"h6",55),t.EFF(74,"Emergency Contact"),t.k0s(),t.j41(75,"p",36),t.EFF(76,"For medical emergencies, call "),t.j41(77,"strong"),t.EFF(78,"911"),t.k0s(),t.EFF(79," or visit your nearest emergency room."),t.k0s()()()()()()}if(2&e){const n=t.XpG();t.R7$(6),t.Lme("",n.getGreeting(),", ",null==n.currentUser?null:n.currentUser.fullName,"!"),t.R7$(11),t.Y8G("ngForOf",n.healthMetrics),t.R7$(6),t.Y8G("ngForOf",n.quickActions),t.R7$(11),t.Y8G("ngIf",0===n.upcomingAppointments.length),t.R7$(1),t.Y8G("ngForOf",n.upcomingAppointments),t.R7$(12),t.Y8G("ngIf",0===n.recentChats.length),t.R7$(1),t.Y8G("ngForOf",n.recentChats),t.R7$(11),t.Y8G("ngForOf",n.recentActivities),t.R7$(8),t.Y8G("ngForOf",n.healthTips)}}const z=[{path:"",redirectTo:"dashboard",pathMatch:"full"},{path:"dashboard",component:(()=>{class e{constructor(n,i,o,c,r){this.authService=n,this.userService=i,this.appointmentService=o,this.chatService=c,this.router=r,this.currentUser=null,this.isLoading=!0,this.error="",this.appointments=[],this.upcomingAppointments=[],this.recentChats=[],this.healthMetrics=[{name:"Heart Rate",value:"72",unit:"bpm",status:"normal",icon:"heart-pulse",color:"text-success"},{name:"Blood Pressure",value:"120/80",unit:"mmHg",status:"normal",icon:"activity",color:"text-success"},{name:"Weight",value:"70",unit:"kg",status:"normal",icon:"speedometer2",color:"text-info"},{name:"Temperature",value:"98.6",unit:"\xb0F",status:"normal",icon:"thermometer-half",color:"text-success"}],this.quickActions=[{title:"Book Appointment",description:"Schedule a consultation with a doctor",icon:"calendar-plus",color:"bg-primary",route:"/appointments/book"},{title:"Find Doctors",description:"Browse available healthcare providers",icon:"search",color:"bg-info",route:"/appointments/doctors"},{title:"Health Assistant",description:"Get AI-powered health guidance",icon:"robot",color:"bg-success",route:"/health-bot"},{title:"Messages",description:"Chat with your healthcare providers",icon:"chat-dots",color:"bg-warning",route:"/chat"}],this.recentActivities=[{title:"Appointment Scheduled",description:"Consultation with Dr. Smith on Dec 15, 2024",time:"2 hours ago",icon:"calendar-check",color:"text-primary"},{title:"Health Metrics Updated",description:"Blood pressure and weight recorded",time:"1 day ago",icon:"graph-up",color:"text-success"},{title:"Message Received",description:"New message from Dr. Johnson",time:"2 days ago",icon:"envelope",color:"text-info"}],this.healthTips=[{title:"Stay Hydrated",description:"Drink at least 8 glasses of water daily for optimal health.",icon:"droplet"},{title:"Regular Exercise",description:"Aim for 30 minutes of moderate exercise 5 days a week.",icon:"bicycle"},{title:"Healthy Sleep",description:"Get 7-9 hours of quality sleep each night.",icon:"moon"}]}ngOnInit(){this.loadUserData(),this.loadAppointments(),this.loadRecentChats()}loadUserData(){this.authService.currentUser$.subscribe({next:n=>{this.currentUser=n,this.isLoading=!1},error:n=>{this.error="Failed to load user data",this.isLoading=!1}})}loadAppointments(){this.appointmentService.getPatientAppointments().subscribe({next:n=>{this.appointments=n,this.upcomingAppointments=n.filter(i=>new Date(i.date)>=new Date).slice(0,3),this.updateRecentActivities()},error:n=>{console.error("Failed to load appointments:",n)}})}updateRecentActivities(){const n=this.appointments.filter(i=>"SCHEDULED"===i.status||"CONFIRMED"===i.status).slice(0,2);this.recentActivities=[...n.map(i=>({title:"Appointment Scheduled",description:`Consultation with Dr. ${i.doctor?.fullName} on ${i.date}`,time:this.getTimeAgo(i.createdAt),icon:"calendar-check",color:"text-primary"})),...this.recentActivities.slice(n.length)]}getTimeAgo(n){const i=new Date,o=new Date(n),c=Math.floor((i.getTime()-o.getTime())/36e5);return c<1?"Just now":c<24?`${c} hours ago`:`${Math.floor(c/24)} days ago`}navigateTo(n){this.router.navigate([n])}getGreeting(){const n=(new Date).getHours();return n<12?"Good morning":n<18?"Good afternoon":"Good evening"}getStatusBadgeClass(n){switch(n){case"normal":return"badge bg-success";case"warning":return"badge bg-warning";case"danger":return"badge bg-danger";default:return"badge bg-secondary"}}refreshData(){this.isLoading=!0,this.loadAppointments(),this.loadRecentChats(),setTimeout(()=>{this.isLoading=!1},1e3)}loadRecentChats(){this.chatService.getUserChats().subscribe({next:n=>{this.recentChats=n.slice(0,3)},error:n=>{console.error("Failed to load chats:",n)}})}openChatModal(){const n=document.getElementById("chatModal");n&&new window.bootstrap.Modal(n).show()}openChat(n){this.openChatModal(),setTimeout(()=>{this.onChatSelected(n)},300)}onChatSelected(n){this.chatWindow&&this.chatWindow.loadChat(n)}formatChatTime(n){const i=new Date(n),c=((new Date).getTime()-i.getTime())/36e5;return c<1?"Just now":c<24?i.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):i.toLocaleDateString()}static{this.\u0275fac=function(i){return new(i||e)(t.rXU(g.u),t.rXU(b.D),t.rXU(h.h),t.rXU(u.m),t.rXU(m.Ix))}}static{this.\u0275cmp=t.VBU({type:e,selectors:[["app-patient-dashboard"]],viewQuery:function(i,o){if(1&i&&t.GBs(D,5),2&i){let c;t.mGM(c=t.lsd())&&(o.chatWindow=c.first)}},decls:19,vars:3,consts:[[1,"container-fluid","py-4"],["class","text-center py-5",4,"ngIf"],["class","alert alert-danger","role","alert",4,"ngIf"],[4,"ngIf"],["id","chatModal","tabindex","-1","aria-labelledby","chatModalLabel","aria-hidden","true",1,"modal","fade"],[1,"modal-dialog","modal-xl"],[1,"modal-content"],[1,"modal-header"],["id","chatModalLabel",1,"modal-title"],[1,"bi","bi-chat-dots","me-2"],["type","button","data-bs-dismiss","modal","aria-label","Close",1,"btn-close"],[1,"modal-body","p-0",2,"height","600px"],[1,"row","h-100","g-0"],[1,"col-md-4","border-end"],[3,"chatSelected"],[1,"col-md-8"],["chatWindow",""],[1,"text-center","py-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-3","text-muted"],["role","alert",1,"alert","alert-danger"],[1,"bi","bi-exclamation-triangle","me-2"],[1,"row","mb-4"],[1,"col-12"],[1,"d-flex","justify-content-between","align-items-center"],[1,"h3","mb-1"],[1,"text-muted","mb-0"],[1,"btn","btn-outline-primary",3,"click"],[1,"bi","bi-arrow-clockwise","me-2"],[1,"mb-3"],[1,"bi","bi-heart-pulse","me-2","text-primary"],["class","col-md-3 col-sm-6 mb-3",4,"ngFor","ngForOf"],[1,"bi","bi-lightning","me-2","text-primary"],[1,"card"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"bi","bi-calendar-check","me-2"],[1,"btn","btn-sm","btn-outline-primary",3,"click"],[1,"card-body"],["class","text-center py-4 text-muted",4,"ngIf"],["class","appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded border",4,"ngFor","ngForOf"],[1,"bi","bi-chat-plus","me-1"],["class","chat-preview d-flex align-items-center justify-content-between p-3 mb-2 rounded border",4,"ngFor","ngForOf"],[1,"row"],[1,"col-lg-4","mb-4"],[1,"col-md-4","mb-4"],[1,"card","h-100"],[1,"card-header"],[1,"bi","bi-clock-history","me-2"],["class","activity-item d-flex align-items-start mb-3",4,"ngFor","ngForOf"],[1,"bi","bi-lightbulb","me-2"],["class","tip-item d-flex align-items-start mb-3",4,"ngFor","ngForOf"],["role","alert",1,"alert","alert-info","d-flex","align-items-center"],[1,"bi","bi-info-circle","me-3","fs-4"],[1,"alert-heading","mb-1"],[1,"col-md-3","col-sm-6","mb-3"],[1,"card-body","text-center"],[1,"card-title"],[1,"mb-2"],[1,"text-muted"],[1,"card","h-100","action-card",3,"click"],[1,"rounded-circle","d-inline-flex","align-items-center","justify-content-center","mb-3",2,"width","60px","height","60px"],[1,"card-text","text-muted","small"],[1,"text-center","py-4","text-muted"],[1,"bi","bi-calendar-x","display-6","mb-3"],[1,"btn","btn-primary",3,"click"],[1,"bi","bi-calendar-plus","me-2"],[1,"appointment-item","d-flex","align-items-center","justify-content-between","p-3","mb-2","rounded","border"],[1,"d-flex","align-items-center"],[1,"flex-shrink-0","me-3"],[1,"rounded-circle","bg-light","d-flex","align-items-center","justify-content-center",2,"width","45px","height","45px"],[1,"mb-1"],[1,"mb-1","text-muted","small"],[1,"bi","bi-calendar","me-1"],[1,"bi","bi-clock","ms-2","me-1"],[1,"flex-shrink-0"],[1,"bi","bi-eye","me-1"],[1,"bi","bi-chat-square-text","display-6","mb-3"],[1,"bi","bi-chat-plus","me-2"],[1,"chat-preview","d-flex","align-items-center","justify-content-between","p-3","mb-2","rounded","border"],[1,"rounded-circle",2,"width","45px","height","45px","object-fit","cover",3,"src","alt"],["class","mb-1 text-muted small",4,"ngIf"],["class","text-muted",4,"ngIf"],["class","badge bg-primary rounded-pill me-2",4,"ngIf"],[1,"bi","bi-chat","me-1"],[1,"badge","bg-primary","rounded-pill","me-2"],[1,"activity-item","d-flex","align-items-start","mb-3"],[1,"rounded-circle","bg-light","d-flex","align-items-center","justify-content-center",2,"width","40px","height","40px"],[1,"flex-grow-1"],["class","my-3",4,"ngIf"],[1,"my-3"],[1,"tip-item","d-flex","align-items-start","mb-3"],[1,"rounded-circle","bg-primary","bg-opacity-10","d-flex","align-items-center","justify-content-center",2,"width","40px","height","40px"],[1,"mb-0","text-muted","small"]],template:function(i,o){1&i&&(t.j41(0,"div",0),t.DNE(1,I,6,0,"div",1),t.DNE(2,G,3,1,"div",2),t.DNE(3,J,80,10,"div",3),t.k0s(),t.j41(4,"div",4)(5,"div",5)(6,"div",6)(7,"div",7)(8,"h5",8),t.nrm(9,"i",9),t.EFF(10,"Messages "),t.k0s(),t.nrm(11,"button",10),t.k0s(),t.j41(12,"div",11)(13,"div",12)(14,"div",13)(15,"app-chat-list",14),t.bIt("chatSelected",function(r){return o.onChatSelected(r)}),t.k0s()(),t.j41(16,"div",15),t.nrm(17,"app-chat-window",null,16),t.k0s()()()()()()),2&i&&(t.R7$(1),t.Y8G("ngIf",o.isLoading),t.R7$(1),t.Y8G("ngIf",o.error&&!o.isLoading),t.R7$(1),t.Y8G("ngIf",!o.isLoading&&!o.error))},dependencies:[d.Sq,d.bT,R,$.q,T.E,d.P9,d.PV],styles:[".action-card[_ngcontent-%COMP%]{cursor:pointer;transition:all .3s ease;border:none;box-shadow:0 2px 4px #0000001a}.action-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #00000026}.card[_ngcontent-%COMP%]{border:none;border-radius:12px;box-shadow:0 2px 4px #0000001a}.card-header[_ngcontent-%COMP%]{background-color:transparent;border-bottom:1px solid #e9ecef;font-weight:600}.activity-item[_ngcontent-%COMP%], .tip-item[_ngcontent-%COMP%]{border-bottom:1px solid #f8f9fa;padding-bottom:1rem}.activity-item[_ngcontent-%COMP%]:last-child, .tip-item[_ngcontent-%COMP%]:last-child{border-bottom:none;padding-bottom:0}.bg-primary[_ngcontent-%COMP%]{background-color:#0d6efd!important}.bg-info[_ngcontent-%COMP%]{background-color:#0dcaf0!important}.bg-success[_ngcontent-%COMP%]{background-color:#198754!important}.bg-warning[_ngcontent-%COMP%]{background-color:#ffc107!important}.text-primary[_ngcontent-%COMP%]{color:#0d6efd!important}.badge[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}@media (max-width: 768px){.container-fluid[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.card-body[_ngcontent-%COMP%]{padding:1rem}.display-6[_ngcontent-%COMP%]{font-size:2rem}}"]})}}return e})()}];let Z=(()=>{class e{static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275mod=t.$C({type:e})}static{this.\u0275inj=t.G2t({imports:[_.G,f.ChatModule,m.iI.forChild(z)]})}}return e})()}}]);