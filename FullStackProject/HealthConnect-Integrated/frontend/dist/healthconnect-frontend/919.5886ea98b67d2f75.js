"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[919],{7337:(C,_,d)=>{d.d(_,{Y:()=>l,y:()=>f});var f=function(o){return o.PENDING="PENDING",o.SCHEDULED="SCHEDULED",o.CONFIRMED="CONFIRMED",o.COMPLETED="COMPLETED",o.CANCELLED="CANCELLED",o.NO_SHOW="NO_SHOW",o}(f||{}),l=function(o){return o.IN_PERSON="IN_PERSON",o.VIDEO_CALL="VIDEO_CALL",o}(l||{})},9545:(C,_,d)=>{d.d(_,{h:()=>p});var f=d(1626),l=d(7337),o=d(540);let p=(()=>{class m{constructor(s){this.http=s,this.apiUrl="http://localhost:8080/api"}getDoctors(s){let c=new f.Nl;return s&&(c=c.set("specialization",s)),this.http.get(`${this.apiUrl}/doctors`,{params:c})}getDoctor(s){return this.http.get(`${this.apiUrl}/doctors/${s}`)}getSpecializations(){return this.http.get(`${this.apiUrl}/doctors/specializations`)}getAvailableTimeSlots(s,c){const g=(new f.Nl).set("date",c);return this.http.get(`${this.apiUrl}/doctors/${s}/time-slots`,{params:g})}getAppointments(s,c,g,v){let h=new f.Nl;return s&&(h=h.set("status",s)),c&&(h=h.set("type",c)),g&&(h=h.set("startDate",g)),v&&(h=h.set("endDate",v)),this.http.get(`${this.apiUrl}/appointments`,{params:h})}getAppointment(s){return this.http.get(`${this.apiUrl}/appointments/${s}`)}createAppointment(s){return this.http.post(`${this.apiUrl}/appointments`,s)}updateAppointment(s,c){return this.http.put(`${this.apiUrl}/appointments/${s}`,c)}cancelAppointment(s){return this.http.delete(`${this.apiUrl}/appointments/${s}`)}getTodayAppointments(){return this.http.get(`${this.apiUrl}/appointments/today`)}getPatientAppointments(){return this.http.get(`${this.apiUrl}/appointments`)}createAppointmentChat(s,c,g="PRE_APPOINTMENT",v){return this.http.post(`${this.apiUrl}/chats/appointment/${s}`,{participantId:c,chatType:g,subject:v})}getStatusDisplayName(s){switch(s){case l.y.PENDING:return"Pending";case l.y.SCHEDULED:return"Scheduled";case l.y.CONFIRMED:return"Confirmed";case l.y.COMPLETED:return"Completed";case l.y.CANCELLED:return"Cancelled";case l.y.NO_SHOW:return"No Show";default:return s}}getTypeDisplayName(s){switch(s){case l.Y.IN_PERSON:return"In Person";case l.Y.VIDEO_CALL:return"Video Call";default:return s}}getStatusBadgeClass(s){switch(s){case l.y.PENDING:return"badge-warning";case l.y.SCHEDULED:return"badge-info";case l.y.CONFIRMED:return"badge-primary";case l.y.COMPLETED:return"badge-success";case l.y.CANCELLED:return"badge-danger";default:return"badge-secondary"}}static{this.\u0275fac=function(c){return new(c||m)(o.KVO(f.Qq))}}static{this.\u0275prov=o.jDH({token:m,factory:m.\u0275fac,providedIn:"root"})}}return m})()},4719:(C,_,d)=>{d.d(_,{U:()=>U});var f=d(8359);class l extends f.yU{constructor(e,i){super()}schedule(e,i=0){return this}}const o={setInterval(n,e,...i){const{delegate:a}=o;return a?.setInterval?a.setInterval(n,e,...i):setInterval(n,e,...i)},clearInterval(n){const{delegate:e}=o;return(e?.clearInterval||clearInterval)(n)},delegate:void 0};var p=d(7908);const b={now:()=>(b.delegate||Date).now(),delegate:void 0};class s{constructor(e,i=s.now){this.schedulerActionCtor=e,this.now=i}schedule(e,i=0,a){return new this.schedulerActionCtor(this,e).schedule(a,i)}}s.now=b.now;const g=new class c extends s{constructor(e,i=s.now){super(e,i),this.actions=[],this._active=!1}flush(e){const{actions:i}=this;if(this._active)return void i.push(e);let a;this._active=!0;do{if(a=e.execute(e.state,e.delay))break}while(e=i.shift());if(this._active=!1,a){for(;e=i.shift();)e.unsubscribe();throw a}}}(class m extends l{constructor(e,i){super(e,i),this.scheduler=e,this.work=i,this.pending=!1}schedule(e,i=0){var a;if(this.closed)return this;this.state=e;const r=this.id,u=this.scheduler;return null!=r&&(this.id=this.recycleAsyncId(u,r,i)),this.pending=!0,this.delay=i,this.id=null!==(a=this.id)&&void 0!==a?a:this.requestAsyncId(u,this.id,i),this}requestAsyncId(e,i,a=0){return o.setInterval(e.flush.bind(e,this),a)}recycleAsyncId(e,i,a=0){if(null!=a&&this.delay===a&&!1===this.pending)return i;null!=i&&o.clearInterval(i)}execute(e,i){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;const a=this._execute(e,i);if(a)return a;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(e,i){let r,a=!1;try{this.work(e)}catch(u){a=!0,r=u||new Error("Scheduled action threw falsy error")}if(a)return this.unsubscribe(),r}unsubscribe(){if(!this.closed){const{id:e,scheduler:i}=this,{actions:a}=i;this.work=this.state=this.scheduler=null,this.pending=!1,(0,p.o)(a,this),null!=e&&(this.id=this.recycleAsyncId(i,e,null)),this.delay=null,super.unsubscribe()}}}),v=g;var h=d(1985),P=d(9470);var t=d(540),I=d(177);function N(n,e){if(1&n&&(t.j41(0,"span",4),t.EFF(1),t.k0s()),2&n){const i=t.XpG();t.R7$(1),t.JRh(i.getStatusText())}}function x(n,e){if(1&n&&(t.j41(0,"div",9),t.nrm(1,"i",10),t.j41(2,"small",11),t.EFF(3),t.k0s()()),2&n){const i=t.XpG(2);t.R7$(3),t.JRh(i.availability.expectedResponseTime)}}function w(n,e){if(1&n&&(t.j41(0,"div",9),t.nrm(1,"i",12),t.j41(2,"small",11),t.EFF(3),t.k0s()()),2&n){const i=t.XpG(2);t.R7$(3),t.Lme(" Chat hours: ",i.availability.chatStartTime," - ",i.availability.chatEndTime," ")}}function A(n,e){if(1&n&&(t.j41(0,"div",9),t.nrm(1,"i",13),t.j41(2,"small",11),t.EFF(3),t.k0s()()),2&n){const i=t.XpG(2);t.R7$(3),t.JRh(i.availability.customMessage)}}function T(n,e){if(1&n&&(t.j41(0,"div",9),t.nrm(1,"i",14),t.j41(2,"small",11),t.EFF(3),t.k0s()()),2&n){const i=t.XpG(2);t.R7$(3),t.SpI("Last seen ",i.getLastSeenText(),"")}}function S(n,e){1&n&&(t.j41(0,"div",15)(1,"div",16),t.nrm(2,"i",17),t.j41(3,"small"),t.EFF(4,"Outside chat hours"),t.k0s()()())}function R(n,e){1&n&&(t.j41(0,"div",18)(1,"div",19),t.nrm(2,"i",20),t.j41(3,"small"),t.EFF(4,"Currently in consultation"),t.k0s()()())}function $(n,e){1&n&&(t.j41(0,"div",18)(1,"div",21),t.nrm(2,"i",22),t.j41(3,"small"),t.EFF(4,"Emergency contacts only"),t.k0s()()())}function L(n,e){if(1&n&&(t.j41(0,"div",5),t.DNE(1,x,4,1,"div",6),t.DNE(2,w,4,2,"div",6),t.DNE(3,A,4,1,"div",6),t.DNE(4,T,4,1,"div",6),t.DNE(5,S,5,0,"div",7),t.DNE(6,R,5,0,"div",8),t.DNE(7,$,5,0,"div",8),t.k0s()),2&n){const i=t.XpG();t.R7$(1),t.Y8G("ngIf",i.availability.expectedResponseTime),t.R7$(1),t.Y8G("ngIf",i.availability.chatStartTime&&i.availability.chatEndTime),t.R7$(1),t.Y8G("ngIf",i.availability.customMessage),t.R7$(1),t.Y8G("ngIf","OFFLINE"===i.availability.status&&i.availability.lastSeen),t.R7$(1),t.Y8G("ngIf",!i.isWithinChatHours()),t.R7$(1),t.Y8G("ngIf","BUSY"===i.availability.status),t.R7$(1),t.Y8G("ngIf","DO_NOT_DISTURB"===i.availability.status)}}let U=(()=>{class n{constructor(){this.showDetails=!0,this.size="md",this.availability=null,this.loading=!1}ngOnInit(){this.loadAvailability(),this.startPeriodicRefresh()}ngOnDestroy(){this.refreshSubscription&&this.refreshSubscription.unsubscribe()}loadAvailability(){this.availability={status:"ONLINE",expectedResponseTime:"Within 2 hours",customMessage:"Available for consultations",chatStartTime:"09:00",chatEndTime:"17:00"}}startPeriodicRefresh(){this.refreshSubscription=function D(n=0,e=g){return n<0&&(n=0),function E(n=0,e,i=v){let a=-1;return null!=e&&((0,P.m)(e)?i=e:a=e),new h.c(r=>{let u=function M(n){return n instanceof Date&&!isNaN(n)}(n)?+n-i.now():n;u<0&&(u=0);let y=0;return i.schedule(function(){r.closed||(r.next(y++),0<=a?this.schedule(void 0,a):r.complete())},u)})}(n,n,e)}(3e5).subscribe(()=>{this.loadAvailability()})}getStatusIcon(){if(!this.availability)return"fas fa-circle text-secondary";switch(this.availability.status){case"ONLINE":return"fas fa-circle text-success";case"BUSY":return"fas fa-circle text-warning";case"AWAY":return"fas fa-circle text-info";case"DO_NOT_DISTURB":return"fas fa-minus-circle text-danger";default:return"fas fa-circle text-secondary"}}getStatusText(){if(!this.availability)return"Unknown";switch(this.availability.status){case"ONLINE":return"Online";case"BUSY":return"Busy";case"AWAY":return"Away";case"DO_NOT_DISTURB":return"Do Not Disturb";default:return"Offline"}}getStatusClass(){if(!this.availability)return"status-offline";switch(this.availability.status){case"ONLINE":return"status-online";case"BUSY":return"status-busy";case"AWAY":return"status-away";case"DO_NOT_DISTURB":return"status-dnd";default:return"status-offline"}}isAvailable(){return"ONLINE"===this.availability?.status||"AWAY"===this.availability?.status}getLastSeenText(){if(!this.availability?.lastSeen)return"";const i=new Date(this.availability.lastSeen),r=(new Date).getTime()-i.getTime(),u=Math.floor(r/6e4),y=Math.floor(u/60),O=Math.floor(y/24);return u<1?"Just now":u<60?`${u} minute${u>1?"s":""} ago`:y<24?`${y} hour${y>1?"s":""} ago`:`${O} day${O>1?"s":""} ago`}isWithinChatHours(){if(!this.availability?.chatStartTime||!this.availability?.chatEndTime)return!0;const a=(new Date).toTimeString().slice(0,5);return a>=this.availability.chatStartTime&&a<=this.availability.chatEndTime}static{this.\u0275fac=function(a){return new(a||n)}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-doctor-availability"]],inputs:{doctorId:"doctorId",showDetails:"showDetails",size:"size"},decls:5,vars:8,consts:[[1,"doctor-availability"],[1,"status-indicator"],["class","status-text ms-2",4,"ngIf"],["class","availability-details mt-2",4,"ngIf"],[1,"status-text","ms-2"],[1,"availability-details","mt-2"],["class","detail-item",4,"ngIf"],["class","availability-warning mt-2",4,"ngIf"],["class","status-message mt-2",4,"ngIf"],[1,"detail-item"],[1,"fas","fa-clock","text-muted","me-2"],[1,"text-muted"],[1,"fas","fa-calendar-clock","text-muted","me-2"],[1,"fas","fa-comment","text-muted","me-2"],[1,"fas","fa-eye","text-muted","me-2"],[1,"availability-warning","mt-2"],[1,"alert","alert-warning","py-1","px-2","mb-0"],[1,"fas","fa-exclamation-triangle","me-1"],[1,"status-message","mt-2"],[1,"alert","alert-info","py-1","px-2","mb-0"],[1,"fas","fa-user-clock","me-1"],[1,"alert","alert-danger","py-1","px-2","mb-0"],[1,"fas","fa-ban","me-1"]],template:function(a,r){1&a&&(t.j41(0,"div",0)(1,"div",1),t.nrm(2,"i"),t.DNE(3,N,2,1,"span",2),t.k0s(),t.DNE(4,L,8,7,"div",3),t.k0s()),2&a&&(t.HbH("size-"+r.size),t.R7$(1),t.HbH(r.getStatusClass()),t.R7$(1),t.HbH(r.getStatusIcon()),t.R7$(1),t.Y8G("ngIf","sm"!==r.size),t.R7$(1),t.Y8G("ngIf",r.showDetails&&r.availability&&"sm"!==r.size))},dependencies:[I.bT],styles:[".doctor-availability[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{display:flex;align-items:center}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-online[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#28a745}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-busy[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#ffc107}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-away[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#17a2b8}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-dnd[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#dc3545}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-offline[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#6c757d}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:.25rem}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:14px;font-size:.75rem}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{font-size:.75rem;border-radius:4px}.doctor-availability.size-sm[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{font-size:.875rem}.doctor-availability.size-sm[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{font-size:1.125rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{font-weight:500}.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]{margin-top:.75rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{margin-bottom:.5rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:16px;font-size:.875rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.875rem}.status-online[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.7}to{opacity:1}}@media (max-width: 576px){.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-bottom:.25rem}}"]})}}return n})()}}]);