"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[792],{4978:(tt,ye,R)=>{R.d(ye,{q:()=>ae});var d=R(540),w=R(8010),de=R(6448);let ae=(()=>{class X{constructor(ie,ge){this.authService=ie,this.router=ge}canActivate(ie,ge){if(this.authService.isAuthenticated()){const le=ie.data.roles;if(le&&le.length>0&&!this.authService.hasRole(le)){const Z=this.authService.getCurrentUser();return this.router.navigate("DOCTOR"===Z?.role?["/doctor/dashboard"]:"PATIENT"===Z?.role?["/patient/dashboard"]:["/auth/login"]),!1}return!0}return this.router.navigate(["/auth/login"],{queryParams:{returnUrl:ge.url}}),!1}static{this.\u0275fac=function(ge){return new(ge||X)(d.KVO(w.u),d.KVO(de.Ix))}}static{this.\u0275prov=d.jDH({token:X,factory:X.\u0275fac,providedIn:"root"})}}return X})()},8010:(tt,ye,R)=>{R.d(ye,{u:()=>le});var d=R(4412),w=R(8141),de=R(9437),ae=R(8810),X=R(5312),oe=R(540),ie=R(1626),ge=R(6448);let le=(()=>{class pe{constructor(ne,be){this.http=ne,this.router=be,this.API_URL=X.c.apiUrl+"/auth",this.currentUserSubject=new d.t(null),this.currentUser$=this.currentUserSubject.asObservable(),this.loadUserFromStorage()}loadUserFromStorage(){const ne=this.getToken(),be=localStorage.getItem("currentUser");if(ne&&be&&!this.isTokenExpired(ne))try{const Ne=JSON.parse(be);this.currentUserSubject.next(Ne)}catch(Ne){console.error("Error parsing stored user data:",Ne),this.clearAuthData()}else this.clearAuthData()}clearAuthData(){localStorage.removeItem("token"),localStorage.removeItem("currentUser"),this.currentUserSubject.next(null)}register(ne){return this.http.post(`${this.API_URL}/register`,ne).pipe((0,w.M)(be=>{be.token&&this.setAuthData(be)}),(0,de.W)(this.handleError))}login(ne){return this.http.post(`${this.API_URL}/login`,ne).pipe((0,w.M)(be=>{be.token&&this.setAuthData(be)}),(0,de.W)(this.handleError))}logout(){this.clearAuthData(),this.router.url.includes("/auth")||this.router.navigate(["/auth/login"])}setAuthData(ne){localStorage.setItem("token",ne.token);const be={id:ne.id,fullName:ne.fullName,email:ne.email,role:ne.role,avatar:ne.avatar,specialization:ne.specialization,licenseNumber:ne.licenseNumber,affiliation:ne.affiliation,yearsOfExperience:ne.yearsOfExperience,phoneNumber:ne.phoneNumber,address:ne.address,createdAt:ne.createdAt,updatedAt:ne.updatedAt};localStorage.setItem("currentUser",JSON.stringify(be)),this.currentUserSubject.next(be)}getToken(){return localStorage.getItem("token")}getCurrentUser(){return this.currentUserSubject.value}isAuthenticated(){const ne=this.getToken();return!!ne&&!this.isTokenExpired(ne)}hasRole(ne){const be=this.getCurrentUser();return!!be&&ne.includes(be.role)}isTokenExpired(ne){try{const be=JSON.parse(atob(ne.split(".")[1])),Ne=Math.floor(Date.now()/1e3);return be.exp<Ne}catch{return!0}}handleError(ne){let be="An error occurred";return ne.error?"string"==typeof ne.error?be=ne.error:ne.error.message&&(be=ne.error.message):ne.message&&(be=ne.message),(0,ae.$)(()=>new Error(be))}static{this.\u0275fac=function(be){return new(be||pe)(oe.KVO(ie.Qq),oe.KVO(ge.Ix))}}static{this.\u0275prov=oe.jDH({token:pe,factory:pe.\u0275fac,providedIn:"root"})}}return pe})()},5567:(tt,ye,R)=>{R.d(ye,{J:()=>ae});var d=R(467),w=R(4412),de=R(540);let ae=(()=>{class X{constructor(){this.notifications$=new w.t([]),this.unreadCount$=new w.t(0),this.loadNotifications()}getNotifications(){return this.notifications$.asObservable()}getUnreadCount(){return this.unreadCount$.asObservable()}addNotification(ie){const ge={...ie,id:this.generateId(),timestamp:new Date,read:!1},pe=[ge,...this.notifications$.value];this.notifications$.next(pe),this.updateUnreadCount(),this.saveNotifications(pe),this.showBrowserNotification(ge)}markAsRead(ie){const ge=this.notifications$.value.map(le=>le.id===ie?{...le,read:!0}:le);this.notifications$.next(ge),this.updateUnreadCount(),this.saveNotifications(ge)}markAllAsRead(){const ie=this.notifications$.value.map(ge=>({...ge,read:!0}));this.notifications$.next(ie),this.updateUnreadCount(),this.saveNotifications(ie)}removeNotification(ie){const ge=this.notifications$.value.filter(le=>le.id!==ie);this.notifications$.next(ge),this.updateUnreadCount(),this.saveNotifications(ge)}clearAll(){this.notifications$.next([]),this.unreadCount$.next(0),this.saveNotifications([])}addMessageNotification(ie,ge,le){this.addNotification({type:"message",title:`New message from ${ie.fullName}`,message:ge.length>100?ge.substring(0,100)+"...":ge,priority:"medium",fromUser:{id:ie.id,name:ie.fullName,avatar:ie.avatar},actionUrl:`/chat?chatId=${le}`,actionText:"Reply"})}addAppointmentNotification(ie,ge){let le="",pe="",Z="medium";switch(ie){case"booked":le="Appointment Booked",pe=`Your appointment with ${ge.doctor.fullName} is scheduled for ${ge.date}`;break;case"reminder":le="Appointment Reminder",pe=`Your appointment with ${ge.doctor.fullName} is in 1 hour`,Z="high";break;case"cancelled":le="Appointment Cancelled",pe=`Your appointment with ${ge.doctor.fullName} has been cancelled`,Z="high"}this.addNotification({type:"appointment",title:le,message:pe,priority:Z,actionUrl:`/appointments/${ge.id}`,actionText:"View Details"})}addUrgentNotification(ie,ge,le){this.addNotification({type:"urgent",title:ie,message:ge,priority:"urgent",actionUrl:le,actionText:le?"View":void 0})}loadNotifications(){const ie=localStorage.getItem("healthconnect_notifications");if(ie)try{const ge=JSON.parse(ie).map(le=>({...le,timestamp:new Date(le.timestamp)}));this.notifications$.next(ge),this.updateUnreadCount()}catch(ge){console.error("Error loading notifications:",ge)}}saveNotifications(ie){try{localStorage.setItem("healthconnect_notifications",JSON.stringify(ie))}catch(ge){console.error("Error saving notifications:",ge)}}updateUnreadCount(){const ie=this.notifications$.value.filter(ge=>!ge.read).length;this.unreadCount$.next(ie)}generateId(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}showBrowserNotification(ie){var ge=this;return(0,d.A)(function*(){"Notification"in window&&("granted"===Notification.permission?new Notification(ie.title,{body:ie.message,icon:"/assets/icons/icon-192x192.png",badge:"/assets/icons/icon-72x72.png",tag:ie.id}):"denied"!==Notification.permission&&"granted"===(yield Notification.requestPermission())&&ge.showBrowserNotification(ie))})()}requestNotificationPermission(){return(0,d.A)(function*(){return"Notification"in window&&("granted"===Notification.permission||"denied"!==Notification.permission&&"granted"===(yield Notification.requestPermission()))})()}static{this.\u0275fac=function(ge){return new(ge||X)}}static{this.\u0275prov=de.jDH({token:X,factory:X.\u0275fac,providedIn:"root"})}}return X})()},3443:(tt,ye,R)=>{R.d(ye,{D:()=>ge});var d=R(1626),w=R(9437),de=R(8141),ae=R(8810),X=R(5312),oe=R(540),ie=R(8010);let ge=(()=>{class le{constructor(Z,ne){this.http=Z,this.authService=ne,this.API_URL=X.c.apiUrl+"/users"}getHeaders(){const Z=this.authService.getToken();return new d.Lr({"Content-Type":"application/json",Authorization:`Bearer ${Z}`})}getCurrentUserProfile(){return this.http.get(`${this.API_URL}/me`,{headers:this.getHeaders()}).pipe((0,w.W)(this.handleError))}updateProfile(Z){return this.http.put(`${this.API_URL}/me`,Z,{headers:this.getHeaders()}).pipe((0,de.M)(ne=>{const be=this.authService.getCurrentUser();if(be){const Ne={...be,...ne};localStorage.setItem("currentUser",JSON.stringify(Ne))}}),(0,w.W)(this.handleError))}getUserById(Z){return this.http.get(`${this.API_URL}/${Z}`,{headers:this.getHeaders()}).pipe((0,w.W)(this.handleError))}handleError(Z){let ne="An error occurred";return Z.error?"string"==typeof Z.error?ne=Z.error:Z.error.message&&(ne=Z.error.message):Z.message&&(ne=Z.message),(0,ae.$)(()=>new Error(ne))}static{this.\u0275fac=function(ne){return new(ne||le)(oe.KVO(d.Qq),oe.KVO(ie.u))}}static{this.\u0275prov=oe.jDH({token:le,factory:le.\u0275fac,providedIn:"root"})}}return le})()},3887:(tt,ye,R)=>{R.d(ye,{G:()=>X});var d=R(177),w=R(4341),de=R(6448),ae=R(540);let X=(()=>{class oe{static{this.\u0275fac=function(le){return new(le||oe)}}static{this.\u0275mod=ae.$C({type:oe})}static{this.\u0275inj=ae.G2t({imports:[d.MD,w.X1,w.YN,de.iI,d.MD,w.X1,w.YN,de.iI]})}}return oe})()},5312:(tt,ye,R)=>{R.d(ye,{c:()=>d});const d={production:!1,apiUrl:"http://localhost:8080/api",appName:"HealthConnect",version:"1.0.0"}},5169:(tt,ye,R)=>{var d=R(345),w=R(540);class de{}class ae{}const X="*";function le(E,a=null){return{type:2,steps:E,options:a}}function pe(E){return{type:6,styles:E,offset:null}}class jt{constructor(a=0,l=0){this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._originalOnDoneFns=[],this._originalOnStartFns=[],this._started=!1,this._destroyed=!1,this._finished=!1,this._position=0,this.parentPlayer=null,this.totalTime=a+l}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(a=>a()),this._onDoneFns=[])}onStart(a){this._originalOnStartFns.push(a),this._onStartFns.push(a)}onDone(a){this._originalOnDoneFns.push(a),this._onDoneFns.push(a)}onDestroy(a){this._onDestroyFns.push(a)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(a=>a()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(a=>a()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(a){this._position=this.totalTime?a*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(a){const l="start"==a?this._onStartFns:this._onDoneFns;l.forEach(y=>y()),l.length=0}}class Qt{constructor(a){this._onDoneFns=[],this._onStartFns=[],this._finished=!1,this._started=!1,this._destroyed=!1,this._onDestroyFns=[],this.parentPlayer=null,this.totalTime=0,this.players=a;let l=0,y=0,M=0;const N=this.players.length;0==N?queueMicrotask(()=>this._onFinish()):this.players.forEach(L=>{L.onDone(()=>{++l==N&&this._onFinish()}),L.onDestroy(()=>{++y==N&&this._onDestroy()}),L.onStart(()=>{++M==N&&this._onStart()})}),this.totalTime=this.players.reduce((L,U)=>Math.max(L,U.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(a=>a()),this._onDoneFns=[])}init(){this.players.forEach(a=>a.init())}onStart(a){this._onStartFns.push(a)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(a=>a()),this._onStartFns=[])}onDone(a){this._onDoneFns.push(a)}onDestroy(a){this._onDestroyFns.push(a)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(a=>a.play())}pause(){this.players.forEach(a=>a.pause())}restart(){this.players.forEach(a=>a.restart())}finish(){this._onFinish(),this.players.forEach(a=>a.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(a=>a.destroy()),this._onDestroyFns.forEach(a=>a()),this._onDestroyFns=[])}reset(){this.players.forEach(a=>a.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(a){const l=a*this.totalTime;this.players.forEach(y=>{const M=y.totalTime?Math.min(1,l/y.totalTime):1;y.setPosition(M)})}getPosition(){const a=this.players.reduce((l,y)=>null===l||y.totalTime>l.totalTime?y:l,null);return null!=a?a.getPosition():0}beforeDestroy(){this.players.forEach(a=>{a.beforeDestroy&&a.beforeDestroy()})}triggerCallback(a){const l="start"==a?this._onStartFns:this._onDoneFns;l.forEach(y=>y()),l.length=0}}function Ve(E){return new w.wOt(3e3,!1)}function ht(E){switch(E.length){case 0:return new jt;case 1:return E[0];default:return new Qt(E)}}function Ct(E,a,l=new Map,y=new Map){const M=[],N=[];let L=-1,U=null;if(a.forEach(q=>{const ue=q.get("offset"),Pe=ue==L,Ue=Pe&&U||new Map;q.forEach((It,yt)=>{let st=yt,_t=It;if("offset"!==yt)switch(st=E.normalizePropertyName(st,M),_t){case"!":_t=l.get(yt);break;case X:_t=y.get(yt);break;default:_t=E.normalizeStyleValue(yt,st,_t,M)}Ue.set(st,_t)}),Pe||N.push(Ue),U=Ue,L=ue}),M.length)throw function at(E){return new w.wOt(3502,!1)}();return N}function En(E,a,l,y){switch(a){case"start":E.onStart(()=>y(l&&Hn(l,"start",E)));break;case"done":E.onDone(()=>y(l&&Hn(l,"done",E)));break;case"destroy":E.onDestroy(()=>y(l&&Hn(l,"destroy",E)))}}function Hn(E,a,l){const N=kn(E.element,E.triggerName,E.fromState,E.toState,a||E.phaseName,l.totalTime??E.totalTime,!!l.disabled),L=E._data;return null!=L&&(N._data=L),N}function kn(E,a,l,y,M="",N=0,L){return{element:E,triggerName:a,fromState:l,toState:y,phaseName:M,totalTime:N,disabled:!!L}}function Nt(E,a,l){let y=E.get(a);return y||E.set(a,y=l),y}function hn(E){const a=E.indexOf(":");return[E.substring(1,a),E.slice(a+1)]}const ti=(()=>typeof document>"u"?null:document.documentElement)();function Zt(E){const a=E.parentNode||E.host||null;return a===ti?null:a}let ze=null,Gr=!1;function wn(E,a){for(;a;){if(a===E)return!0;a=Zt(a)}return!1}function Ze(E,a,l){if(l)return Array.from(E.querySelectorAll(a));const y=E.querySelector(a);return y?[y]:[]}let yi=(()=>{class E{validateStyleProperty(l){return function kr(E){ze||(ze=function gr(){return typeof document<"u"?document.body:null}()||{},Gr=!!ze.style&&"WebkitAppearance"in ze.style);let a=!0;return ze.style&&!function Xi(E){return"ebkit"==E.substring(1,6)}(E)&&(a=E in ze.style,!a&&Gr&&(a="Webkit"+E.charAt(0).toUpperCase()+E.slice(1)in ze.style)),a}(l)}matchesElement(l,y){return!1}containsElement(l,y){return wn(l,y)}getParentElement(l){return Zt(l)}query(l,y,M){return Ze(l,y,M)}computeStyle(l,y,M){return M||""}animate(l,y,M,N,L,U=[],q){return new jt(M,N)}static{this.\u0275fac=function(y){return new(y||E)}}static{this.\u0275prov=w.jDH({token:E,factory:E.\u0275fac})}}return E})(),ot=(()=>{class E{static{this.NOOP=new yi}}return E})();const br=1e3,Mr="ng-enter",ce="ng-leave",K="ng-trigger",V=".ng-trigger",z="ng-animating",me=".ng-animating";function Fe(E){if("number"==typeof E)return E;const a=E.match(/^(-?[\.\d]+)(m?s)/);return!a||a.length<2?0:Ye(parseFloat(a[1]),a[2])}function Ye(E,a){return"s"===a?E*br:E}function bt(E,a,l){return E.hasOwnProperty("duration")?E:function Pt(E,a,l){let M,N=0,L="";if("string"==typeof E){const U=E.match(/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i);if(null===U)return a.push(Ve()),{duration:0,delay:0,easing:""};M=Ye(parseFloat(U[1]),U[2]);const q=U[3];null!=q&&(N=Ye(parseFloat(q),U[4]));const ue=U[5];ue&&(L=ue)}else M=E;if(!l){let U=!1,q=a.length;M<0&&(a.push(function nt(){return new w.wOt(3100,!1)}()),U=!0),N<0&&(a.push(function Re(){return new w.wOt(3101,!1)}()),U=!0),U&&a.splice(q,0,Ve())}return{duration:M,delay:N,easing:L}}(E,a,l)}function Yt(E,a={}){return Object.keys(E).forEach(l=>{a[l]=E[l]}),a}function zn(E){const a=new Map;return Object.keys(E).forEach(l=>{a.set(l,E[l])}),a}function j(E,a=new Map,l){if(l)for(let[y,M]of l)a.set(y,M);for(let[y,M]of E)a.set(y,M);return a}function $(E,a,l){a.forEach((y,M)=>{const N=Jt(M);l&&!l.has(M)&&l.set(M,E.style[N]),E.style[N]=y})}function fe(E,a){a.forEach((l,y)=>{const M=Jt(y);E.style[M]=""})}function Se(E){return Array.isArray(E)?1==E.length?E[0]:le(E):E}const Oe=new RegExp("{{\\s*(.+?)\\s*}}","g");function Wt(E){let a=[];if("string"==typeof E){let l;for(;l=Oe.exec(E);)a.push(l[1]);Oe.lastIndex=0}return a}function ke(E,a,l){const y=E.toString(),M=y.replace(Oe,(N,L)=>{let U=a[L];return null==U&&(l.push(function Ee(E){return new w.wOt(3003,!1)}()),U=""),U.toString()});return M==y?E:M}function rn(E){const a=[];let l=E.next();for(;!l.done;)a.push(l.value),l=E.next();return a}const ir=/-+([a-z0-9])/g;function Jt(E){return E.replace(ir,(...a)=>a[1].toUpperCase())}function dt(E,a,l){switch(a.type){case 7:return E.visitTrigger(a,l);case 0:return E.visitState(a,l);case 1:return E.visitTransition(a,l);case 2:return E.visitSequence(a,l);case 3:return E.visitGroup(a,l);case 4:return E.visitAnimate(a,l);case 5:return E.visitKeyframes(a,l);case 6:return E.visitStyle(a,l);case 8:return E.visitReference(a,l);case 9:return E.visitAnimateChild(a,l);case 10:return E.visitAnimateRef(a,l);case 11:return E.visitQuery(a,l);case 12:return E.visitStagger(a,l);default:throw function rt(E){return new w.wOt(3004,!1)}()}}function Rt(E,a){return window.getComputedStyle(E)[a]}const Tr="*";function vr(E,a){const l=[];return"string"==typeof E?E.split(/\s*,\s*/).forEach(y=>function ri(E,a,l){if(":"==E[0]){const q=function Lr(E,a){switch(E){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(l,y)=>parseFloat(y)>parseFloat(l);case":decrement":return(l,y)=>parseFloat(y)<parseFloat(l);default:return a.push(function we(E){return new w.wOt(3016,!1)}()),"* => *"}}(E,l);if("function"==typeof q)return void a.push(q);E=q}const y=E.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(null==y||y.length<4)return l.push(function Ie(E){return new w.wOt(3015,!1)}()),a;const M=y[1],N=y[2],L=y[3];a.push(Ri(M,L));"<"==N[0]&&!(M==Tr&&L==Tr)&&a.push(Ri(L,M))}(y,l,a)):l.push(E),l}const sr=new Set(["true","1"]),Dr=new Set(["false","0"]);function Ri(E,a){const l=sr.has(E)||Dr.has(E),y=sr.has(a)||Dr.has(a);return(M,N)=>{let L=E==Tr||E==M,U=a==Tr||a==N;return!L&&l&&"boolean"==typeof M&&(L=M?sr.has(E):Dr.has(E)),!U&&y&&"boolean"==typeof N&&(U=N?sr.has(a):Dr.has(a)),L&&U}}const ar=new RegExp("s*:selfs*,?","g");function ii(E,a,l,y){return new Fi(E).build(a,l,y)}class Fi{constructor(a){this._driver=a}build(a,l,y){const M=new vi(l);return this._resetContextStyleTimingState(M),dt(this,Se(a),M)}_resetContextStyleTimingState(a){a.currentQuerySelector="",a.collectedStyles=new Map,a.collectedStyles.set("",new Map),a.currentTime=0}visitTrigger(a,l){let y=l.queryCount=0,M=l.depCount=0;const N=[],L=[];return"@"==a.name.charAt(0)&&l.errors.push(function _n(){return new w.wOt(3006,!1)}()),a.definitions.forEach(U=>{if(this._resetContextStyleTimingState(l),0==U.type){const q=U,ue=q.name;ue.toString().split(/\s*,\s*/).forEach(Pe=>{q.name=Pe,N.push(this.visitState(q,l))}),q.name=ue}else if(1==U.type){const q=this.visitTransition(U,l);y+=q.queryCount,M+=q.depCount,L.push(q)}else l.errors.push(function $n(){return new w.wOt(3007,!1)}())}),{type:7,name:a.name,states:N,transitions:L,queryCount:y,depCount:M,options:null}}visitState(a,l){const y=this.visitStyle(a.styles,l),M=a.options&&a.options.params||null;if(y.containsDynamicStyles){const N=new Set,L=M||{};y.styles.forEach(U=>{U instanceof Map&&U.forEach(q=>{Wt(q).forEach(ue=>{L.hasOwnProperty(ue)||N.add(ue)})})}),N.size&&(rn(N.values()),l.errors.push(function xe(E,a){return new w.wOt(3008,!1)}()))}return{type:0,name:a.name,style:y,options:M?{params:M}:null}}visitTransition(a,l){l.queryCount=0,l.depCount=0;const y=dt(this,Se(a.animation),l);return{type:1,matchers:vr(a.expr,l.errors),animation:y,queryCount:l.queryCount,depCount:l.depCount,options:Ar(a.options)}}visitSequence(a,l){return{type:2,steps:a.steps.map(y=>dt(this,y,l)),options:Ar(a.options)}}visitGroup(a,l){const y=l.currentTime;let M=0;const N=a.steps.map(L=>{l.currentTime=y;const U=dt(this,L,l);return M=Math.max(M,l.currentTime),U});return l.currentTime=M,{type:3,steps:N,options:Ar(a.options)}}visitAnimate(a,l){const y=function ao(E,a){if(E.hasOwnProperty("duration"))return E;if("number"==typeof E)return Qi(bt(E,a).duration,0,"");const l=E;if(l.split(/\s+/).some(N=>"{"==N.charAt(0)&&"{"==N.charAt(1))){const N=Qi(0,0,"");return N.dynamic=!0,N.strValue=l,N}const M=bt(l,a);return Qi(M.duration,M.delay,M.easing)}(a.timings,l.errors);l.currentAnimateTimings=y;let M,N=a.styles?a.styles:pe({});if(5==N.type)M=this.visitKeyframes(N,l);else{let L=a.styles,U=!1;if(!L){U=!0;const ue={};y.easing&&(ue.easing=y.easing),L=pe(ue)}l.currentTime+=y.duration+y.delay;const q=this.visitStyle(L,l);q.isEmptyStep=U,M=q}return l.currentAnimateTimings=null,{type:4,timings:y,style:M,options:null}}visitStyle(a,l){const y=this._makeStyleAst(a,l);return this._validateStyleAst(y,l),y}_makeStyleAst(a,l){const y=[],M=Array.isArray(a.styles)?a.styles:[a.styles];for(let U of M)"string"==typeof U?U===X?y.push(U):l.errors.push(new w.wOt(3002,!1)):y.push(zn(U));let N=!1,L=null;return y.forEach(U=>{if(U instanceof Map&&(U.has("easing")&&(L=U.get("easing"),U.delete("easing")),!N))for(let q of U.values())if(q.toString().indexOf("{{")>=0){N=!0;break}}),{type:6,styles:y,easing:L,offset:a.offset,containsDynamicStyles:N,options:null}}_validateStyleAst(a,l){const y=l.currentAnimateTimings;let M=l.currentTime,N=l.currentTime;y&&N>0&&(N-=y.duration+y.delay),a.styles.forEach(L=>{"string"!=typeof L&&L.forEach((U,q)=>{const ue=l.collectedStyles.get(l.currentQuerySelector),Pe=ue.get(q);let Ue=!0;Pe&&(N!=M&&N>=Pe.startTime&&M<=Pe.endTime&&(l.errors.push(function ft(E,a,l,y,M){return new w.wOt(3010,!1)}()),Ue=!1),N=Pe.startTime),Ue&&ue.set(q,{startTime:N,endTime:M}),l.options&&function Be(E,a,l){const y=a.params||{},M=Wt(E);M.length&&M.forEach(N=>{y.hasOwnProperty(N)||l.push(function Ae(E){return new w.wOt(3001,!1)}())})}(U,l.options,l.errors)})})}visitKeyframes(a,l){const y={type:5,styles:[],options:null};if(!l.currentAnimateTimings)return l.errors.push(function Fn(){return new w.wOt(3011,!1)}()),y;let N=0;const L=[];let U=!1,q=!1,ue=0;const Pe=a.steps.map(Mn=>{const Un=this._makeStyleAst(Mn,l);let rr=null!=Un.offset?Un.offset:function Zn(E){if("string"==typeof E)return null;let a=null;if(Array.isArray(E))E.forEach(l=>{if(l instanceof Map&&l.has("offset")){const y=l;a=parseFloat(y.get("offset")),y.delete("offset")}});else if(E instanceof Map&&E.has("offset")){const l=E;a=parseFloat(l.get("offset")),l.delete("offset")}return a}(Un.styles),In=0;return null!=rr&&(N++,In=Un.offset=rr),q=q||In<0||In>1,U=U||In<ue,ue=In,L.push(In),Un});q&&l.errors.push(function Fr(){return new w.wOt(3012,!1)}()),U&&l.errors.push(function Q(){return new w.wOt(3200,!1)}());const Ue=a.steps.length;let It=0;N>0&&N<Ue?l.errors.push(function Y(){return new w.wOt(3202,!1)}()):0==N&&(It=1/(Ue-1));const yt=Ue-1,st=l.currentTime,_t=l.currentAnimateTimings,mn=_t.duration;return Pe.forEach((Mn,Un)=>{const rr=It>0?Un==yt?1:It*Un:L[Un],In=rr*mn;l.currentTime=st+_t.delay+In,_t.duration=In,this._validateStyleAst(Mn,l),Mn.offset=rr,y.styles.push(Mn)}),y}visitReference(a,l){return{type:8,animation:dt(this,Se(a.animation),l),options:Ar(a.options)}}visitAnimateChild(a,l){return l.depCount++,{type:9,options:Ar(a.options)}}visitAnimateRef(a,l){return{type:10,animation:this.visitReference(a.animation,l),options:Ar(a.options)}}visitQuery(a,l){const y=l.currentQuerySelector,M=a.options||{};l.queryCount++,l.currentQuery=a;const[N,L]=function Vr(E){const a=!!E.split(/\s*,\s*/).find(l=>":self"==l);return a&&(E=E.replace(ar,"")),E=E.replace(/@\*/g,V).replace(/@\w+/g,l=>V+"-"+l.slice(1)).replace(/:animating/g,me),[E,a]}(a.selector);l.currentQuerySelector=y.length?y+" "+N:N,Nt(l.collectedStyles,l.currentQuerySelector,new Map);const U=dt(this,Se(a.animation),l);return l.currentQuery=null,l.currentQuerySelector=y,{type:11,selector:N,limit:M.limit||0,optional:!!M.optional,includeSelf:L,animation:U,originalSelector:a.selector,options:Ar(a.options)}}visitStagger(a,l){l.currentQuery||l.errors.push(function J(){return new w.wOt(3013,!1)}());const y="full"===a.timings?{duration:0,delay:0,easing:"full"}:bt(a.timings,l.errors,!0);return{type:12,animation:dt(this,Se(a.animation),l),timings:y,options:null}}}class vi{constructor(a){this.errors=a,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles=new Map,this.options=null,this.unsupportedCSSPropertiesFound=new Set}}function Ar(E){return E?(E=Yt(E)).params&&(E.params=function Wn(E){return E?Yt(E):null}(E.params)):E={},E}function Qi(E,a,l){return{duration:E,delay:a,easing:l}}function ki(E,a,l,y,M,N,L=null,U=!1){return{type:1,element:E,keyframes:a,preStyleProps:l,postStyleProps:y,duration:M,delay:N,totalTime:M+N,easing:L,subTimeline:U}}class oi{constructor(){this._map=new Map}get(a){return this._map.get(a)||[]}append(a,l){let y=this._map.get(a);y||this._map.set(a,y=[]),y.push(...l)}has(a){return this._map.has(a)}clear(){this._map.clear()}}const Zi=new RegExp(":enter","g"),Bo=new RegExp(":leave","g");function si(E,a,l,y,M,N=new Map,L=new Map,U,q,ue=[]){return(new uo).buildKeyframes(E,a,l,y,M,N,L,U,q,ue)}class uo{buildKeyframes(a,l,y,M,N,L,U,q,ue,Pe=[]){ue=ue||new oi;const Ue=new Di(a,l,ue,M,N,Pe,[]);Ue.options=q;const It=q.delay?Fe(q.delay):0;Ue.currentTimeline.delayNextStep(It),Ue.currentTimeline.setStyles([L],null,Ue.errors,q),dt(this,y,Ue);const yt=Ue.timelines.filter(st=>st.containsAnimation());if(yt.length&&U.size){let st;for(let _t=yt.length-1;_t>=0;_t--){const mn=yt[_t];if(mn.element===l){st=mn;break}}st&&!st.allowOnlyTimelineStyles()&&st.setStyles([U],null,Ue.errors,q)}return yt.length?yt.map(st=>st.buildKeyframes()):[ki(l,[],[],[],0,It,"",!1)]}visitTrigger(a,l){}visitState(a,l){}visitTransition(a,l){}visitAnimateChild(a,l){const y=l.subInstructions.get(l.element);if(y){const M=l.createSubContext(a.options),N=l.currentTimeline.currentTime,L=this._visitSubInstructions(y,M,M.options);N!=L&&l.transformIntoNewTimeline(L)}l.previousNode=a}visitAnimateRef(a,l){const y=l.createSubContext(a.options);y.transformIntoNewTimeline(),this._applyAnimationRefDelays([a.options,a.animation.options],l,y),this.visitReference(a.animation,y),l.transformIntoNewTimeline(y.currentTimeline.currentTime),l.previousNode=a}_applyAnimationRefDelays(a,l,y){for(const M of a){const N=M?.delay;if(N){const L="number"==typeof N?N:Fe(ke(N,M?.params??{},l.errors));y.delayNextStep(L)}}}_visitSubInstructions(a,l,y){let N=l.currentTimeline.currentTime;const L=null!=y.duration?Fe(y.duration):null,U=null!=y.delay?Fe(y.delay):null;return 0!==L&&a.forEach(q=>{const ue=l.appendInstructionToTimeline(q,L,U);N=Math.max(N,ue.duration+ue.delay)}),N}visitReference(a,l){l.updateOptions(a.options,!0),dt(this,a.animation,l),l.previousNode=a}visitSequence(a,l){const y=l.subContextCount;let M=l;const N=a.options;if(N&&(N.params||N.delay)&&(M=l.createSubContext(N),M.transformIntoNewTimeline(),null!=N.delay)){6==M.previousNode.type&&(M.currentTimeline.snapshotCurrentStyles(),M.previousNode=jr);const L=Fe(N.delay);M.delayNextStep(L)}a.steps.length&&(a.steps.forEach(L=>dt(this,L,M)),M.currentTimeline.applyStylesToKeyframe(),M.subContextCount>y&&M.transformIntoNewTimeline()),l.previousNode=a}visitGroup(a,l){const y=[];let M=l.currentTimeline.currentTime;const N=a.options&&a.options.delay?Fe(a.options.delay):0;a.steps.forEach(L=>{const U=l.createSubContext(a.options);N&&U.delayNextStep(N),dt(this,L,U),M=Math.max(M,U.currentTimeline.currentTime),y.push(U.currentTimeline)}),y.forEach(L=>l.currentTimeline.mergeTimelineCollectedStyles(L)),l.transformIntoNewTimeline(M),l.previousNode=a}_visitTiming(a,l){if(a.dynamic){const y=a.strValue;return bt(l.params?ke(y,l.params,l.errors):y,l.errors)}return{duration:a.duration,delay:a.delay,easing:a.easing}}visitAnimate(a,l){const y=l.currentAnimateTimings=this._visitTiming(a.timings,l),M=l.currentTimeline;y.delay&&(l.incrementTime(y.delay),M.snapshotCurrentStyles());const N=a.style;5==N.type?this.visitKeyframes(N,l):(l.incrementTime(y.duration),this.visitStyle(N,l),M.applyStylesToKeyframe()),l.currentAnimateTimings=null,l.previousNode=a}visitStyle(a,l){const y=l.currentTimeline,M=l.currentAnimateTimings;!M&&y.hasCurrentStyleProperties()&&y.forwardFrame();const N=M&&M.easing||a.easing;a.isEmptyStep?y.applyEmptyStep(N):y.setStyles(a.styles,N,l.errors,l.options),l.previousNode=a}visitKeyframes(a,l){const y=l.currentAnimateTimings,M=l.currentTimeline.duration,N=y.duration,U=l.createSubContext().currentTimeline;U.easing=y.easing,a.styles.forEach(q=>{U.forwardTime((q.offset||0)*N),U.setStyles(q.styles,q.easing,l.errors,l.options),U.applyStylesToKeyframe()}),l.currentTimeline.mergeTimelineCollectedStyles(U),l.transformIntoNewTimeline(M+N),l.previousNode=a}visitQuery(a,l){const y=l.currentTimeline.currentTime,M=a.options||{},N=M.delay?Fe(M.delay):0;N&&(6===l.previousNode.type||0==y&&l.currentTimeline.hasCurrentStyleProperties())&&(l.currentTimeline.snapshotCurrentStyles(),l.previousNode=jr);let L=y;const U=l.invokeQuery(a.selector,a.originalSelector,a.limit,a.includeSelf,!!M.optional,l.errors);l.currentQueryTotal=U.length;let q=null;U.forEach((ue,Pe)=>{l.currentQueryIndex=Pe;const Ue=l.createSubContext(a.options,ue);N&&Ue.delayNextStep(N),ue===l.element&&(q=Ue.currentTimeline),dt(this,a.animation,Ue),Ue.currentTimeline.applyStylesToKeyframe(),L=Math.max(L,Ue.currentTimeline.currentTime)}),l.currentQueryIndex=0,l.currentQueryTotal=0,l.transformIntoNewTimeline(L),q&&(l.currentTimeline.mergeTimelineCollectedStyles(q),l.currentTimeline.snapshotCurrentStyles()),l.previousNode=a}visitStagger(a,l){const y=l.parentContext,M=l.currentTimeline,N=a.timings,L=Math.abs(N.duration),U=L*(l.currentQueryTotal-1);let q=L*l.currentQueryIndex;switch(N.duration<0?"reverse":N.easing){case"reverse":q=U-q;break;case"full":q=y.currentStaggerTime}const Pe=l.currentTimeline;q&&Pe.delayNextStep(q);const Ue=Pe.currentTime;dt(this,a.animation,l),l.previousNode=a,y.currentStaggerTime=M.currentTime-Ue+(M.startTime-y.currentTimeline.startTime)}}const jr={};class Di{constructor(a,l,y,M,N,L,U,q){this._driver=a,this.element=l,this.subInstructions=y,this._enterClassName=M,this._leaveClassName=N,this.errors=L,this.timelines=U,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=jr,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=q||new ai(this._driver,l,0),U.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(a,l){if(!a)return;const y=a;let M=this.options;null!=y.duration&&(M.duration=Fe(y.duration)),null!=y.delay&&(M.delay=Fe(y.delay));const N=y.params;if(N){let L=M.params;L||(L=this.options.params={}),Object.keys(N).forEach(U=>{(!l||!L.hasOwnProperty(U))&&(L[U]=ke(N[U],L,this.errors))})}}_copyOptions(){const a={};if(this.options){const l=this.options.params;if(l){const y=a.params={};Object.keys(l).forEach(M=>{y[M]=l[M]})}}return a}createSubContext(a=null,l,y){const M=l||this.element,N=new Di(this._driver,M,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(M,y||0));return N.previousNode=this.previousNode,N.currentAnimateTimings=this.currentAnimateTimings,N.options=this._copyOptions(),N.updateOptions(a),N.currentQueryIndex=this.currentQueryIndex,N.currentQueryTotal=this.currentQueryTotal,N.parentContext=this,this.subContextCount++,N}transformIntoNewTimeline(a){return this.previousNode=jr,this.currentTimeline=this.currentTimeline.fork(this.element,a),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(a,l,y){const M={duration:l??a.duration,delay:this.currentTimeline.currentTime+(y??0)+a.delay,easing:""},N=new Ji(this._driver,a.element,a.keyframes,a.preStyleProps,a.postStyleProps,M,a.stretchStartingKeyframe);return this.timelines.push(N),M}incrementTime(a){this.currentTimeline.forwardTime(this.currentTimeline.duration+a)}delayNextStep(a){a>0&&this.currentTimeline.delayNextStep(a)}invokeQuery(a,l,y,M,N,L){let U=[];if(M&&U.push(this.element),a.length>0){a=(a=a.replace(Zi,"."+this._enterClassName)).replace(Bo,"."+this._leaveClassName);let ue=this._driver.query(this.element,a,1!=y);0!==y&&(ue=y<0?ue.slice(ue.length+y,ue.length):ue.slice(0,y)),U.push(...ue)}return!N&&0==U.length&&L.push(function se(E){return new w.wOt(3014,!1)}()),U}}class ai{constructor(a,l,y,M){this._driver=a,this.element=l,this.startTime=y,this._elementTimelineStylesLookup=M,this.duration=0,this.easing=null,this._previousKeyframe=new Map,this._currentKeyframe=new Map,this._keyframes=new Map,this._styleSummary=new Map,this._localTimelineStyles=new Map,this._pendingStyles=new Map,this._backFill=new Map,this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(l),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(l,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(a){const l=1===this._keyframes.size&&this._pendingStyles.size;this.duration||l?(this.forwardTime(this.currentTime+a),l&&this.snapshotCurrentStyles()):this.startTime+=a}fork(a,l){return this.applyStylesToKeyframe(),new ai(this._driver,a,l||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=1,this._loadKeyframe()}forwardTime(a){this.applyStylesToKeyframe(),this.duration=a,this._loadKeyframe()}_updateStyle(a,l){this._localTimelineStyles.set(a,l),this._globalTimelineStyles.set(a,l),this._styleSummary.set(a,{time:this.currentTime,value:l})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(a){a&&this._previousKeyframe.set("easing",a);for(let[l,y]of this._globalTimelineStyles)this._backFill.set(l,y||X),this._currentKeyframe.set(l,X);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(a,l,y,M){l&&this._previousKeyframe.set("easing",l);const N=M&&M.params||{},L=function Ci(E,a){const l=new Map;let y;return E.forEach(M=>{if("*"===M){y=y||a.keys();for(let N of y)l.set(N,X)}else j(M,l)}),l}(a,this._globalTimelineStyles);for(let[U,q]of L){const ue=ke(q,N,y);this._pendingStyles.set(U,ue),this._localTimelineStyles.has(U)||this._backFill.set(U,this._globalTimelineStyles.get(U)??X),this._updateStyle(U,ue)}}applyStylesToKeyframe(){0!=this._pendingStyles.size&&(this._pendingStyles.forEach((a,l)=>{this._currentKeyframe.set(l,a)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((a,l)=>{this._currentKeyframe.has(l)||this._currentKeyframe.set(l,a)}))}snapshotCurrentStyles(){for(let[a,l]of this._localTimelineStyles)this._pendingStyles.set(a,l),this._updateStyle(a,l)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){const a=[];for(let l in this._currentKeyframe)a.push(l);return a}mergeTimelineCollectedStyles(a){a._styleSummary.forEach((l,y)=>{const M=this._styleSummary.get(y);(!M||l.time>M.time)&&this._updateStyle(y,l.value)})}buildKeyframes(){this.applyStylesToKeyframe();const a=new Set,l=new Set,y=1===this._keyframes.size&&0===this.duration;let M=[];this._keyframes.forEach((U,q)=>{const ue=j(U,new Map,this._backFill);ue.forEach((Pe,Ue)=>{"!"===Pe?a.add(Ue):Pe===X&&l.add(Ue)}),y||ue.set("offset",q/this.duration),M.push(ue)});const N=a.size?rn(a.values()):[],L=l.size?rn(l.values()):[];if(y){const U=M[0],q=new Map(U);U.set("offset",0),q.set("offset",1),M=[U,q]}return ki(this.element,M,N,L,this.duration,this.startTime,this.easing,!1)}}class Ji extends ai{constructor(a,l,y,M,N,L,U=!1){super(a,l,L.delay),this.keyframes=y,this.preStyleProps=M,this.postStyleProps=N,this._stretchStartingKeyframe=U,this.timings={duration:L.duration,delay:L.delay,easing:L.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let a=this.keyframes,{delay:l,duration:y,easing:M}=this.timings;if(this._stretchStartingKeyframe&&l){const N=[],L=y+l,U=l/L,q=j(a[0]);q.set("offset",0),N.push(q);const ue=j(a[0]);ue.set("offset",Xr(U)),N.push(ue);const Pe=a.length-1;for(let Ue=1;Ue<=Pe;Ue++){let It=j(a[Ue]);const yt=It.get("offset");It.set("offset",Xr((l+yt*y)/L)),N.push(It)}y=L,l=0,M="",a=N}return ki(this.element,a,this.preStyleProps,this.postStyleProps,y,l,M,!0)}}function Xr(E,a=3){const l=Math.pow(10,a-1);return Math.round(E*l)/l}class Mt{}const sn=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]);class Or extends Mt{normalizePropertyName(a,l){return Jt(a)}normalizeStyleValue(a,l,y,M){let N="";const L=y.toString().trim();if(sn.has(l)&&0!==y&&"0"!==y)if("number"==typeof y)N="px";else{const U=y.match(/^[+-]?[\d\.]+([a-z]*)$/);U&&0==U[1].length&&M.push(function Ft(E,a){return new w.wOt(3005,!1)}())}return L+N}}function bn(E,a,l,y,M,N,L,U,q,ue,Pe,Ue,It){return{type:0,element:E,triggerName:a,isRemovalTransition:M,fromState:l,fromStyles:N,toState:y,toStyles:L,timelines:U,queriedElements:q,preStyleProps:ue,postStyleProps:Pe,totalTime:Ue,errors:It}}const Li={};class Nr{constructor(a,l,y){this._triggerName=a,this.ast=l,this._stateStyles=y}match(a,l,y,M){return function Mo(E,a,l,y,M){return E.some(N=>N(a,l,y,M))}(this.ast.matchers,a,l,y,M)}buildStyles(a,l,y){let M=this._stateStyles.get("*");return void 0!==a&&(M=this._stateStyles.get(a?.toString())||M),M?M.buildStyles(l,y):new Map}build(a,l,y,M,N,L,U,q,ue,Pe){const Ue=[],It=this.ast.options&&this.ast.options.params||Li,st=this.buildStyles(y,U&&U.params||Li,Ue),_t=q&&q.params||Li,mn=this.buildStyles(M,_t,Ue),Mn=new Set,Un=new Map,rr=new Map,In="void"===M,Ti={params:_i(_t,It),delay:this.ast.options?.delay},ci=Pe?[]:si(a,l,this.ast.animation,N,L,st,mn,Ti,ue,Ue);let yn=0;if(ci.forEach(fi=>{yn=Math.max(fi.duration+fi.delay,yn)}),Ue.length)return bn(l,this._triggerName,y,M,In,st,mn,[],[],Un,rr,yn,Ue);ci.forEach(fi=>{const Et=fi.element,Wo=Nt(Un,Et,new Set);fi.preStyleProps.forEach(Cn=>Wo.add(Cn));const Ai=Nt(rr,Et,new Set);fi.postStyleProps.forEach(Cn=>Ai.add(Cn)),Et!==l&&Mn.add(Et)});const di=rn(Mn.values());return bn(l,this._triggerName,y,M,In,st,mn,ci,di,Un,rr,yn)}}function _i(E,a){const l=Yt(a);for(const y in E)E.hasOwnProperty(y)&&null!=E[y]&&(l[y]=E[y]);return l}class ln{constructor(a,l,y){this.styles=a,this.defaultParams=l,this.normalizer=y}buildStyles(a,l){const y=new Map,M=Yt(this.defaultParams);return Object.keys(a).forEach(N=>{const L=a[N];null!==L&&(M[N]=L)}),this.styles.styles.forEach(N=>{"string"!=typeof N&&N.forEach((L,U)=>{L&&(L=ke(L,M,l));const q=this.normalizer.normalizePropertyName(U,l);L=this.normalizer.normalizeStyleValue(U,q,L,l),y.set(U,L)})}),y}}class mt{constructor(a,l,y){this.name=a,this.ast=l,this._normalizer=y,this.transitionFactories=[],this.states=new Map,l.states.forEach(M=>{this.states.set(M.name,new ln(M.style,M.options&&M.options.params||{},y))}),Kn(this.states,"true","1"),Kn(this.states,"false","0"),l.transitions.forEach(M=>{this.transitionFactories.push(new Nr(a,M,this.states))}),this.fallbackTransition=function nn(E,a,l){return new Nr(E,{type:1,animation:{type:2,steps:[],options:null},matchers:[(L,U)=>!0],options:null,queryCount:0,depCount:0},a)}(a,this.states)}get containsQueries(){return this.ast.queryCount>0}matchTransition(a,l,y,M){return this.transitionFactories.find(L=>L.match(a,l,y,M))||null}matchStyles(a,l,y){return this.fallbackTransition.buildStyles(a,l,y)}}function Kn(E,a,l){E.has(a)?E.has(l)||E.set(l,E.get(a)):E.has(l)&&E.set(a,E.get(l))}const li=new oi;class Ln{constructor(a,l,y){this.bodyNode=a,this._driver=l,this._normalizer=y,this._animations=new Map,this._playersById=new Map,this.players=[]}register(a,l){const y=[],N=ii(this._driver,l,y,[]);if(y.length)throw function kt(E){return new w.wOt(3503,!1)}();this._animations.set(a,N)}_buildPlayer(a,l,y){const M=a.element,N=Ct(this._normalizer,a.keyframes,l,y);return this._driver.animate(M,N,a.duration,a.delay,a.easing,[],!0)}create(a,l,y={}){const M=[],N=this._animations.get(a);let L;const U=new Map;if(N?(L=si(this._driver,l,N,Mr,ce,new Map,new Map,y,li,M),L.forEach(Pe=>{const Ue=Nt(U,Pe.element,new Map);Pe.postStyleProps.forEach(It=>Ue.set(It,null))})):(M.push(function qt(){return new w.wOt(3300,!1)}()),L=[]),M.length)throw function Jn(E){return new w.wOt(3504,!1)}();U.forEach((Pe,Ue)=>{Pe.forEach((It,yt)=>{Pe.set(yt,this._driver.computeStyle(Ue,yt,X))})});const ue=ht(L.map(Pe=>{const Ue=U.get(Pe.element);return this._buildPlayer(Pe,new Map,Ue)}));return this._playersById.set(a,ue),ue.onDestroy(()=>this.destroy(a)),this.players.push(ue),ue}destroy(a){const l=this._getPlayer(a);l.destroy(),this._playersById.delete(a);const y=this.players.indexOf(l);y>=0&&this.players.splice(y,1)}_getPlayer(a){const l=this._playersById.get(a);if(!l)throw function gt(E){return new w.wOt(3301,!1)}();return l}listen(a,l,y,M){const N=kn(l,"","","");return En(this._getPlayer(a),y,N,M),()=>{}}command(a,l,y,M){if("register"==y)return void this.register(a,M[0]);if("create"==y)return void this.create(a,l,M[0]||{});const N=this._getPlayer(a);switch(y){case"play":N.play();break;case"pause":N.pause();break;case"reset":N.reset();break;case"restart":N.restart();break;case"finish":N.finish();break;case"init":N.init();break;case"setPosition":N.setPosition(parseFloat(M[0]));break;case"destroy":this.destroy(a)}}}const tr="ng-animate-queued",Cr="ng-animate-disabled",Ei=[],Ur={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},an={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},Vn="__ng_removed";class nr{get params(){return this.options.params}constructor(a,l=""){this.namespaceId=l;const y=a&&a.hasOwnProperty("value");if(this.value=function wi(E){return E??null}(y?a.value:a),y){const N=Yt(a);delete N.value,this.options=N}else this.options={};this.options.params||(this.options.params={})}absorbOptions(a){const l=a.params;if(l){const y=this.options.params;Object.keys(l).forEach(M=>{null==y[M]&&(y[M]=l[M])})}}}const Sn="void",Zr=new nr(Sn);class So{constructor(a,l,y){this.id=a,this.hostElement=l,this._engine=y,this.players=[],this._triggers=new Map,this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+a,Xn(l,this._hostClassName)}listen(a,l,y,M){if(!this._triggers.has(l))throw function fn(E,a){return new w.wOt(3302,!1)}();if(null==y||0==y.length)throw function Gt(E){return new w.wOt(3303,!1)}();if(!function $o(E){return"start"==E||"done"==E}(y))throw function Qe(E,a){return new w.wOt(3400,!1)}();const N=Nt(this._elementListeners,a,[]),L={name:l,phase:y,callback:M};N.push(L);const U=Nt(this._engine.statesByElement,a,new Map);return U.has(l)||(Xn(a,K),Xn(a,K+"-"+l),U.set(l,Zr)),()=>{this._engine.afterFlush(()=>{const q=N.indexOf(L);q>=0&&N.splice(q,1),this._triggers.has(l)||U.delete(l)})}}register(a,l){return!this._triggers.has(a)&&(this._triggers.set(a,l),!0)}_getTrigger(a){const l=this._triggers.get(a);if(!l)throw function tn(E){return new w.wOt(3401,!1)}();return l}trigger(a,l,y,M=!0){const N=this._getTrigger(l),L=new Pr(this.id,l,a);let U=this._engine.statesByElement.get(a);U||(Xn(a,K),Xn(a,K+"-"+l),this._engine.statesByElement.set(a,U=new Map));let q=U.get(l);const ue=new nr(y,this.id);if(!(y&&y.hasOwnProperty("value"))&&q&&ue.absorbOptions(q.options),U.set(l,ue),q||(q=Zr),ue.value!==Sn&&q.value===ue.value){if(!function Pn(E,a){const l=Object.keys(E),y=Object.keys(a);if(l.length!=y.length)return!1;for(let M=0;M<l.length;M++){const N=l[M];if(!a.hasOwnProperty(N)||E[N]!==a[N])return!1}return!0}(q.params,ue.params)){const _t=[],mn=N.matchStyles(q.value,q.params,_t),Mn=N.matchStyles(ue.value,ue.params,_t);_t.length?this._engine.reportError(_t):this._engine.afterFlush(()=>{fe(a,mn),$(a,Mn)})}return}const It=Nt(this._engine.playersByElement,a,[]);It.forEach(_t=>{_t.namespaceId==this.id&&_t.triggerName==l&&_t.queued&&_t.destroy()});let yt=N.matchTransition(q.value,ue.value,a,ue.params),st=!1;if(!yt){if(!M)return;yt=N.fallbackTransition,st=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:a,triggerName:l,transition:yt,fromState:q,toState:ue,player:L,isFallbackTransition:st}),st||(Xn(a,tr),L.onStart(()=>{Nn(a,tr)})),L.onDone(()=>{let _t=this.players.indexOf(L);_t>=0&&this.players.splice(_t,1);const mn=this._engine.playersByElement.get(a);if(mn){let Mn=mn.indexOf(L);Mn>=0&&mn.splice(Mn,1)}}),this.players.push(L),It.push(L),L}deregister(a){this._triggers.delete(a),this._engine.statesByElement.forEach(l=>l.delete(a)),this._elementListeners.forEach((l,y)=>{this._elementListeners.set(y,l.filter(M=>M.name!=a))})}clearElementCache(a){this._engine.statesByElement.delete(a),this._elementListeners.delete(a);const l=this._engine.playersByElement.get(a);l&&(l.forEach(y=>y.destroy()),this._engine.playersByElement.delete(a))}_signalRemovalForInnerTriggers(a,l){const y=this._engine.driver.query(a,V,!0);y.forEach(M=>{if(M[Vn])return;const N=this._engine.fetchNamespacesByElement(M);N.size?N.forEach(L=>L.triggerLeaveAnimation(M,l,!1,!0)):this.clearElementCache(M)}),this._engine.afterFlushAnimationsDone(()=>y.forEach(M=>this.clearElementCache(M)))}triggerLeaveAnimation(a,l,y,M){const N=this._engine.statesByElement.get(a),L=new Map;if(N){const U=[];if(N.forEach((q,ue)=>{if(L.set(ue,q.value),this._triggers.has(ue)){const Pe=this.trigger(a,ue,Sn,M);Pe&&U.push(Pe)}}),U.length)return this._engine.markElementAsRemoved(this.id,a,!0,l,L),y&&ht(U).onDone(()=>this._engine.processLeaveNode(a)),!0}return!1}prepareLeaveAnimationListeners(a){const l=this._elementListeners.get(a),y=this._engine.statesByElement.get(a);if(l&&y){const M=new Set;l.forEach(N=>{const L=N.name;if(M.has(L))return;M.add(L);const q=this._triggers.get(L).fallbackTransition,ue=y.get(L)||Zr,Pe=new nr(Sn),Ue=new Pr(this.id,L,a);this._engine.totalQueuedPlayers++,this._queue.push({element:a,triggerName:L,transition:q,fromState:ue,toState:Pe,player:Ue,isFallbackTransition:!0})})}}removeNode(a,l){const y=this._engine;if(a.childElementCount&&this._signalRemovalForInnerTriggers(a,l),this.triggerLeaveAnimation(a,l,!0))return;let M=!1;if(y.totalAnimations){const N=y.players.length?y.playersByQueriedElement.get(a):[];if(N&&N.length)M=!0;else{let L=a;for(;L=L.parentNode;)if(y.statesByElement.get(L)){M=!0;break}}}if(this.prepareLeaveAnimationListeners(a),M)y.markElementAsRemoved(this.id,a,!1,l);else{const N=a[Vn];(!N||N===Ur)&&(y.afterFlush(()=>this.clearElementCache(a)),y.destroyInnerAnimations(a),y._onRemovalComplete(a,l))}}insertNode(a,l){Xn(a,this._hostClassName)}drainQueuedTransitions(a){const l=[];return this._queue.forEach(y=>{const M=y.player;if(M.destroyed)return;const N=y.element,L=this._elementListeners.get(N);L&&L.forEach(U=>{if(U.name==y.triggerName){const q=kn(N,y.triggerName,y.fromState.value,y.toState.value);q._data=a,En(y.player,U.phase,q,U.callback)}}),M.markedForDestroy?this._engine.afterFlush(()=>{M.destroy()}):l.push(y)}),this._queue=[],l.sort((y,M)=>{const N=y.transition.ast.depCount,L=M.transition.ast.depCount;return 0==N||0==L?N-L:this._engine.driver.containsElement(y.element,M.element)?1:-1})}destroy(a){this.players.forEach(l=>l.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,a)}}class Br{_onRemovalComplete(a,l){this.onRemovalComplete(a,l)}constructor(a,l,y){this.bodyNode=a,this.driver=l,this._normalizer=y,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=(M,N)=>{}}get queuedPlayers(){const a=[];return this._namespaceList.forEach(l=>{l.players.forEach(y=>{y.queued&&a.push(y)})}),a}createNamespace(a,l){const y=new So(a,l,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,l)?this._balanceNamespaceList(y,l):(this.newHostElements.set(l,y),this.collectEnterElement(l)),this._namespaceLookup[a]=y}_balanceNamespaceList(a,l){const y=this._namespaceList,M=this.namespacesByHostElement;if(y.length-1>=0){let L=!1,U=this.driver.getParentElement(l);for(;U;){const q=M.get(U);if(q){const ue=y.indexOf(q);y.splice(ue+1,0,a),L=!0;break}U=this.driver.getParentElement(U)}L||y.unshift(a)}else y.push(a);return M.set(l,a),a}register(a,l){let y=this._namespaceLookup[a];return y||(y=this.createNamespace(a,l)),y}registerTrigger(a,l,y){let M=this._namespaceLookup[a];M&&M.register(l,y)&&this.totalAnimations++}destroy(a,l){a&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{const y=this._fetchNamespace(a);this.namespacesByHostElement.delete(y.hostElement);const M=this._namespaceList.indexOf(y);M>=0&&this._namespaceList.splice(M,1),y.destroy(l),delete this._namespaceLookup[a]}))}_fetchNamespace(a){return this._namespaceLookup[a]}fetchNamespacesByElement(a){const l=new Set,y=this.statesByElement.get(a);if(y)for(let M of y.values())if(M.namespaceId){const N=this._fetchNamespace(M.namespaceId);N&&l.add(N)}return l}trigger(a,l,y,M){if(St(l)){const N=this._fetchNamespace(a);if(N)return N.trigger(l,y,M),!0}return!1}insertNode(a,l,y,M){if(!St(l))return;const N=l[Vn];if(N&&N.setForRemoval){N.setForRemoval=!1,N.setForMove=!0;const L=this.collectedLeaveElements.indexOf(l);L>=0&&this.collectedLeaveElements.splice(L,1)}if(a){const L=this._fetchNamespace(a);L&&L.insertNode(l,y)}M&&this.collectEnterElement(l)}collectEnterElement(a){this.collectedEnterElements.push(a)}markElementAsDisabled(a,l){l?this.disabledNodes.has(a)||(this.disabledNodes.add(a),Xn(a,Cr)):this.disabledNodes.has(a)&&(this.disabledNodes.delete(a),Nn(a,Cr))}removeNode(a,l,y){if(St(l)){const M=a?this._fetchNamespace(a):null;M?M.removeNode(l,y):this.markElementAsRemoved(a,l,!1,y);const N=this.namespacesByHostElement.get(l);N&&N.id!==a&&N.removeNode(l,y)}else this._onRemovalComplete(l,y)}markElementAsRemoved(a,l,y,M,N){this.collectedLeaveElements.push(l),l[Vn]={namespaceId:a,setForRemoval:M,hasAnimation:y,removedBeforeQueried:!1,previousTriggersValues:N}}listen(a,l,y,M,N){return St(l)?this._fetchNamespace(a).listen(l,y,M,N):()=>{}}_buildInstruction(a,l,y,M,N){return a.transition.build(this.driver,a.element,a.fromState.value,a.toState.value,y,M,a.fromState.options,a.toState.options,l,N)}destroyInnerAnimations(a){let l=this.driver.query(a,V,!0);l.forEach(y=>this.destroyActiveAnimationsForElement(y)),0!=this.playersByQueriedElement.size&&(l=this.driver.query(a,me,!0),l.forEach(y=>this.finishActiveQueriedAnimationOnElement(y)))}destroyActiveAnimationsForElement(a){const l=this.playersByElement.get(a);l&&l.forEach(y=>{y.queued?y.markedForDestroy=!0:y.destroy()})}finishActiveQueriedAnimationOnElement(a){const l=this.playersByQueriedElement.get(a);l&&l.forEach(y=>y.finish())}whenRenderingDone(){return new Promise(a=>{if(this.players.length)return ht(this.players).onDone(()=>a());a()})}processLeaveNode(a){const l=a[Vn];if(l&&l.setForRemoval){if(a[Vn]=Ur,l.namespaceId){this.destroyInnerAnimations(a);const y=this._fetchNamespace(l.namespaceId);y&&y.clearElementCache(a)}this._onRemovalComplete(a,l.setForRemoval)}a.classList?.contains(Cr)&&this.markElementAsDisabled(a,!1),this.driver.query(a,".ng-animate-disabled",!0).forEach(y=>{this.markElementAsDisabled(y,!1)})}flush(a=-1){let l=[];if(this.newHostElements.size&&(this.newHostElements.forEach((y,M)=>this._balanceNamespaceList(y,M)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let y=0;y<this.collectedEnterElements.length;y++)Xn(this.collectedEnterElements[y],"ng-star-inserted");if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){const y=[];try{l=this._flushAnimations(y,a)}finally{for(let M=0;M<y.length;M++)y[M]()}}else for(let y=0;y<this.collectedLeaveElements.length;y++)this.processLeaveNode(this.collectedLeaveElements[y]);if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(y=>y()),this._flushFns=[],this._whenQuietFns.length){const y=this._whenQuietFns;this._whenQuietFns=[],l.length?ht(l).onDone(()=>{y.forEach(M=>M())}):y.forEach(M=>M())}}reportError(a){throw function it(E){return new w.wOt(3402,!1)}()}_flushAnimations(a,l){const y=new oi,M=[],N=new Map,L=[],U=new Map,q=new Map,ue=new Map,Pe=new Set;this.disabledNodes.forEach(Xe=>{Pe.add(Xe);const Je=this.driver.query(Xe,".ng-animate-queued",!0);for(let Dt=0;Dt<Je.length;Dt++)Pe.add(Je[Dt])});const Ue=this.bodyNode,It=Array.from(this.statesByElement.keys()),yt=gn(It,this.collectedEnterElements),st=new Map;let _t=0;yt.forEach((Xe,Je)=>{const Dt=Mr+_t++;st.set(Je,Dt),Xe.forEach($t=>Xn($t,Dt))});const mn=[],Mn=new Set,Un=new Set;for(let Xe=0;Xe<this.collectedLeaveElements.length;Xe++){const Je=this.collectedLeaveElements[Xe],Dt=Je[Vn];Dt&&Dt.setForRemoval&&(mn.push(Je),Mn.add(Je),Dt.hasAnimation?this.driver.query(Je,".ng-star-inserted",!0).forEach($t=>Mn.add($t)):Un.add(Je))}const rr=new Map,In=gn(It,Array.from(Mn));In.forEach((Xe,Je)=>{const Dt=ce+_t++;rr.set(Je,Dt),Xe.forEach($t=>Xn($t,Dt))}),a.push(()=>{yt.forEach((Xe,Je)=>{const Dt=st.get(Je);Xe.forEach($t=>Nn($t,Dt))}),In.forEach((Xe,Je)=>{const Dt=rr.get(Je);Xe.forEach($t=>Nn($t,Dt))}),mn.forEach(Xe=>{this.processLeaveNode(Xe)})});const Ti=[],ci=[];for(let Xe=this._namespaceList.length-1;Xe>=0;Xe--)this._namespaceList[Xe].drainQueuedTransitions(l).forEach(Dt=>{const $t=Dt.player,xn=Dt.element;if(Ti.push($t),this.collectedEnterElements.length){const dr=xn[Vn];if(dr&&dr.setForMove){if(dr.previousTriggersValues&&dr.previousTriggersValues.has(Dt.triggerName)){const ut=dr.previousTriggersValues.get(Dt.triggerName),Er=this.statesByElement.get(Dt.element);if(Er&&Er.has(Dt.triggerName)){const Ko=Er.get(Dt.triggerName);Ko.value=ut,Er.set(Dt.triggerName,Ko)}}return void $t.destroy()}}const qn=!Ue||!this.driver.containsElement(Ue,xn),Rr=rr.get(xn),Oi=st.get(xn),Kt=this._buildInstruction(Dt,y,Oi,Rr,qn);if(Kt.errors&&Kt.errors.length)return void ci.push(Kt);if(qn)return $t.onStart(()=>fe(xn,Kt.fromStyles)),$t.onDestroy(()=>$(xn,Kt.toStyles)),void M.push($t);if(Dt.isFallbackTransition)return $t.onStart(()=>fe(xn,Kt.fromStyles)),$t.onDestroy(()=>$(xn,Kt.toStyles)),void M.push($t);const us=[];Kt.timelines.forEach(dr=>{dr.stretchStartingKeyframe=!0,this.disabledNodes.has(dr.element)||us.push(dr)}),Kt.timelines=us,y.append(xn,Kt.timelines),L.push({instruction:Kt,player:$t,element:xn}),Kt.queriedElements.forEach(dr=>Nt(U,dr,[]).push($t)),Kt.preStyleProps.forEach((dr,ut)=>{if(dr.size){let Er=q.get(ut);Er||q.set(ut,Er=new Set),dr.forEach((Ko,xo)=>Er.add(xo))}}),Kt.postStyleProps.forEach((dr,ut)=>{let Er=ue.get(ut);Er||ue.set(ut,Er=new Set),dr.forEach((Ko,xo)=>Er.add(xo))})});if(ci.length){const Xe=[];ci.forEach(Je=>{Xe.push(function Ut(E,a){return new w.wOt(3505,!1)}())}),Ti.forEach(Je=>Je.destroy()),this.reportError(Xe)}const yn=new Map,di=new Map;L.forEach(Xe=>{const Je=Xe.element;y.has(Je)&&(di.set(Je,Je),this._beforeAnimationBuild(Xe.player.namespaceId,Xe.instruction,yn))}),M.forEach(Xe=>{const Je=Xe.element;this._getPreviousPlayers(Je,!1,Xe.namespaceId,Xe.triggerName,null).forEach($t=>{Nt(yn,Je,[]).push($t),$t.destroy()})});const fi=mn.filter(Xe=>Dn(Xe,q,ue)),Et=new Map;eo(Et,this.driver,Un,ue,X).forEach(Xe=>{Dn(Xe,q,ue)&&fi.push(Xe)});const Ai=new Map;yt.forEach((Xe,Je)=>{eo(Ai,this.driver,new Set(Xe),q,"!")}),fi.forEach(Xe=>{const Je=Et.get(Xe),Dt=Ai.get(Xe);Et.set(Xe,new Map([...Je?.entries()??[],...Dt?.entries()??[]]))});const Cn=[],as=[],ls={};L.forEach(Xe=>{const{element:Je,player:Dt,instruction:$t}=Xe;if(y.has(Je)){if(Pe.has(Je))return Dt.onDestroy(()=>$(Je,$t.toStyles)),Dt.disabled=!0,Dt.overrideTotalTime($t.totalTime),void M.push(Dt);let xn=ls;if(di.size>1){let Rr=Je;const Oi=[];for(;Rr=Rr.parentNode;){const Kt=di.get(Rr);if(Kt){xn=Kt;break}Oi.push(Rr)}Oi.forEach(Kt=>di.set(Kt,xn))}const qn=this._buildAnimation(Dt.namespaceId,$t,yn,N,Ai,Et);if(Dt.setRealPlayer(qn),xn===ls)Cn.push(Dt);else{const Rr=this.playersByElement.get(xn);Rr&&Rr.length&&(Dt.parentPlayer=ht(Rr)),M.push(Dt)}}else fe(Je,$t.fromStyles),Dt.onDestroy(()=>$(Je,$t.toStyles)),as.push(Dt),Pe.has(Je)&&M.push(Dt)}),as.forEach(Xe=>{const Je=N.get(Xe.element);if(Je&&Je.length){const Dt=ht(Je);Xe.setRealPlayer(Dt)}}),M.forEach(Xe=>{Xe.parentPlayer?Xe.syncPlayerEvents(Xe.parentPlayer):Xe.destroy()});for(let Xe=0;Xe<mn.length;Xe++){const Je=mn[Xe],Dt=Je[Vn];if(Nn(Je,ce),Dt&&Dt.hasAnimation)continue;let $t=[];if(U.size){let qn=U.get(Je);qn&&qn.length&&$t.push(...qn);let Rr=this.driver.query(Je,me,!0);for(let Oi=0;Oi<Rr.length;Oi++){let Kt=U.get(Rr[Oi]);Kt&&Kt.length&&$t.push(...Kt)}}const xn=$t.filter(qn=>!qn.destroyed);xn.length?Ui(this,Je,xn):this.processLeaveNode(Je)}return mn.length=0,Cn.forEach(Xe=>{this.players.push(Xe),Xe.onDone(()=>{Xe.destroy();const Je=this.players.indexOf(Xe);this.players.splice(Je,1)}),Xe.play()}),Cn}afterFlush(a){this._flushFns.push(a)}afterFlushAnimationsDone(a){this._whenQuietFns.push(a)}_getPreviousPlayers(a,l,y,M,N){let L=[];if(l){const U=this.playersByQueriedElement.get(a);U&&(L=U)}else{const U=this.playersByElement.get(a);if(U){const q=!N||N==Sn;U.forEach(ue=>{ue.queued||!q&&ue.triggerName!=M||L.push(ue)})}}return(y||M)&&(L=L.filter(U=>!(y&&y!=U.namespaceId||M&&M!=U.triggerName))),L}_beforeAnimationBuild(a,l,y){const N=l.element,L=l.isRemovalTransition?void 0:a,U=l.isRemovalTransition?void 0:l.triggerName;for(const q of l.timelines){const ue=q.element,Pe=ue!==N,Ue=Nt(y,ue,[]);this._getPreviousPlayers(ue,Pe,L,U,l.toState).forEach(yt=>{const st=yt.getRealPlayer();st.beforeDestroy&&st.beforeDestroy(),yt.destroy(),Ue.push(yt)})}fe(N,l.fromStyles)}_buildAnimation(a,l,y,M,N,L){const U=l.triggerName,q=l.element,ue=[],Pe=new Set,Ue=new Set,It=l.timelines.map(st=>{const _t=st.element;Pe.add(_t);const mn=_t[Vn];if(mn&&mn.removedBeforeQueried)return new jt(st.duration,st.delay);const Mn=_t!==q,Un=function jn(E){const a=[];return Ho(E,a),a}((y.get(_t)||Ei).map(yn=>yn.getRealPlayer())).filter(yn=>!!yn.element&&yn.element===_t),rr=N.get(_t),In=L.get(_t),Ti=Ct(this._normalizer,st.keyframes,rr,In),ci=this._buildPlayer(st,Ti,Un);if(st.subTimeline&&M&&Ue.add(_t),Mn){const yn=new Pr(a,U,_t);yn.setRealPlayer(ci),ue.push(yn)}return ci});ue.forEach(st=>{Nt(this.playersByQueriedElement,st.element,[]).push(st),st.onDone(()=>function Vi(E,a,l){let y=E.get(a);if(y){if(y.length){const M=y.indexOf(l);y.splice(M,1)}0==y.length&&E.delete(a)}return y}(this.playersByQueriedElement,st.element,st))}),Pe.forEach(st=>Xn(st,z));const yt=ht(It);return yt.onDestroy(()=>{Pe.forEach(st=>Nn(st,z)),$(q,l.toStyles)}),Ue.forEach(st=>{Nt(M,st,[]).push(yt)}),yt}_buildPlayer(a,l,y){return l.length>0?this.driver.animate(a.element,l,a.duration,a.delay,a.easing,y):new jt(a.duration,a.delay)}}class Pr{constructor(a,l,y){this.namespaceId=a,this.triggerName=l,this.element=y,this._player=new jt,this._containsRealPlayer=!1,this._queuedCallbacks=new Map,this.destroyed=!1,this.parentPlayer=null,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}setRealPlayer(a){this._containsRealPlayer||(this._player=a,this._queuedCallbacks.forEach((l,y)=>{l.forEach(M=>En(a,y,void 0,M))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(a.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(a){this.totalTime=a}syncPlayerEvents(a){const l=this._player;l.triggerCallback&&a.onStart(()=>l.triggerCallback("start")),a.onDone(()=>this.finish()),a.onDestroy(()=>this.destroy())}_queueEvent(a,l){Nt(this._queuedCallbacks,a,[]).push(l)}onDone(a){this.queued&&this._queueEvent("done",a),this._player.onDone(a)}onStart(a){this.queued&&this._queueEvent("start",a),this._player.onStart(a)}onDestroy(a){this.queued&&this._queueEvent("destroy",a),this._player.onDestroy(a)}init(){this._player.init()}hasStarted(){return!this.queued&&this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(a){this.queued||this._player.setPosition(a)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(a){const l=this._player;l.triggerCallback&&l.triggerCallback(a)}}function St(E){return E&&1===E.nodeType}function ji(E,a){const l=E.style.display;return E.style.display=a??"none",l}function eo(E,a,l,y,M){const N=[];l.forEach(q=>N.push(ji(q)));const L=[];y.forEach((q,ue)=>{const Pe=new Map;q.forEach(Ue=>{const It=a.computeStyle(ue,Ue,M);Pe.set(Ue,It),(!It||0==It.length)&&(ue[Vn]=an,L.push(ue))}),E.set(ue,Pe)});let U=0;return l.forEach(q=>ji(q,N[U++])),L}function gn(E,a){const l=new Map;if(E.forEach(U=>l.set(U,[])),0==a.length)return l;const M=new Set(a),N=new Map;function L(U){if(!U)return 1;let q=N.get(U);if(q)return q;const ue=U.parentNode;return q=l.has(ue)?ue:M.has(ue)?1:L(ue),N.set(U,q),q}return a.forEach(U=>{const q=L(U);1!==q&&l.get(q).push(U)}),l}function Xn(E,a){E.classList?.add(a)}function Nn(E,a){E.classList?.remove(a)}function Ui(E,a,l){ht(l).onDone(()=>E.processLeaveNode(a))}function Ho(E,a){for(let l=0;l<E.length;l++){const y=E[l];y instanceof Qt?Ho(y.players,a):a.push(y)}}function Dn(E,a,l){const y=l.get(E);if(!y)return!1;let M=a.get(E);return M?y.forEach(N=>M.add(N)):a.set(E,y),l.delete(E),!0}class Bi{constructor(a,l,y){this.bodyNode=a,this._driver=l,this._normalizer=y,this._triggerCache={},this.onRemovalComplete=(M,N)=>{},this._transitionEngine=new Br(a,l,y),this._timelineEngine=new Ln(a,l,y),this._transitionEngine.onRemovalComplete=(M,N)=>this.onRemovalComplete(M,N)}registerTrigger(a,l,y,M,N){const L=a+"-"+M;let U=this._triggerCache[L];if(!U){const q=[],Pe=ii(this._driver,N,q,[]);if(q.length)throw function zt(E,a){return new w.wOt(3404,!1)}();U=function Le(E,a,l){return new mt(E,a,l)}(M,Pe,this._normalizer),this._triggerCache[L]=U}this._transitionEngine.registerTrigger(l,M,U)}register(a,l){this._transitionEngine.register(a,l)}destroy(a,l){this._transitionEngine.destroy(a,l)}onInsert(a,l,y,M){this._transitionEngine.insertNode(a,l,y,M)}onRemove(a,l,y){this._transitionEngine.removeNode(a,l,y)}disableAnimations(a,l){this._transitionEngine.markElementAsDisabled(a,l)}process(a,l,y,M){if("@"==y.charAt(0)){const[N,L]=hn(y);this._timelineEngine.command(N,l,L,M)}else this._transitionEngine.trigger(a,l,y,M)}listen(a,l,y,M,N){if("@"==y.charAt(0)){const[L,U]=hn(y);return this._timelineEngine.listen(L,l,U,N)}return this._transitionEngine.listen(a,l,y,M,N)}flush(a=-1){this._transitionEngine.flush(a)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(a){this._transitionEngine.afterFlushAnimationsDone(a)}}let Bt=(()=>{class E{static{this.initialStylesByElement=new WeakMap}constructor(l,y,M){this._element=l,this._startStyles=y,this._endStyles=M,this._state=0;let N=E.initialStylesByElement.get(l);N||E.initialStylesByElement.set(l,N=new Map),this._initialStyles=N}start(){this._state<1&&(this._startStyles&&$(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&($(this._element,this._initialStyles),this._endStyles&&($(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(E.initialStylesByElement.delete(this._element),this._startStyles&&(fe(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(fe(this._element,this._endStyles),this._endStyles=null),$(this._element,this._initialStyles),this._state=3)}}return E})();function Rn(E){let a=null;return E.forEach((l,y)=>{(function co(E){return"display"===E||"position"===E})(y)&&(a=a||new Map,a.set(y,l))}),a}class b{constructor(a,l,y,M){this.element=a,this.keyframes=l,this.options=y,this._specialStyles=M,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this._originalOnDoneFns=[],this._originalOnStartFns=[],this.time=0,this.parentPlayer=null,this.currentSnapshot=new Map,this._duration=y.duration,this._delay=y.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(a=>a()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;const a=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,a,this.options),this._finalKeyframe=a.length?a[a.length-1]:new Map;const l=()=>this._onFinish();this.domPlayer.addEventListener("finish",l),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",l)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(a){const l=[];return a.forEach(y=>{l.push(Object.fromEntries(y))}),l}_triggerWebAnimation(a,l,y){return a.animate(this._convertKeyframesToObject(l),y)}onStart(a){this._originalOnStartFns.push(a),this._onStartFns.push(a)}onDone(a){this._originalOnDoneFns.push(a),this._onDoneFns.push(a)}onDestroy(a){this._onDestroyFns.push(a)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(a=>a()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(a=>a()),this._onDestroyFns=[])}setPosition(a){void 0===this.domPlayer&&this.init(),this.domPlayer.currentTime=a*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){const a=new Map;this.hasStarted()&&this._finalKeyframe.forEach((y,M)=>{"offset"!==M&&a.set(M,this._finished?y:Rt(this.element,M))}),this.currentSnapshot=a}triggerCallback(a){const l="start"===a?this._onStartFns:this._onDoneFns;l.forEach(y=>y()),l.length=0}}class S{validateStyleProperty(a){return!0}validateAnimatableStyleProperty(a){return!0}matchesElement(a,l){return!1}containsElement(a,l){return wn(a,l)}getParentElement(a){return Zt(a)}query(a,l,y){return Ze(a,l,y)}computeStyle(a,l,y){return window.getComputedStyle(a)[l]}animate(a,l,y,M,N,L=[]){const q={duration:y,delay:M,fill:0==M?"both":"forwards"};N&&(q.easing=N);const ue=new Map,Pe=L.filter(yt=>yt instanceof b);(function Kr(E,a){return 0===E||0===a})(y,M)&&Pe.forEach(yt=>{yt.currentSnapshot.forEach((st,_t)=>ue.set(_t,st))});let Ue=function Sr(E){return E.length?E[0]instanceof Map?E:E.map(a=>zn(a)):[]}(l).map(yt=>j(yt));Ue=function yr(E,a,l){if(l.size&&a.length){let y=a[0],M=[];if(l.forEach((N,L)=>{y.has(L)||M.push(L),y.set(L,N)}),M.length)for(let N=1;N<a.length;N++){let L=a[N];M.forEach(U=>L.set(U,Rt(E,U)))}}return a}(a,Ue,ue);const It=function bi(E,a){let l=null,y=null;return Array.isArray(a)&&a.length?(l=Rn(a[0]),a.length>1&&(y=Rn(a[a.length-1]))):a instanceof Map&&(l=Rn(a)),l||y?new Bt(E,l,y):null}(a,Ue);return new b(a,Ue,q,It)}}var v=R(177);let P=(()=>{class E extends de{constructor(l,y){super(),this._nextAnimationId=0,this._renderer=l.createRenderer(y.body,{id:"0",encapsulation:w.gXe.None,styles:[],data:{animation:[]}})}build(l){const y=this._nextAnimationId.toString();this._nextAnimationId++;const M=Array.isArray(l)?le(l):l;return vt(this._renderer,null,y,"register",[M]),new B(y,this._renderer)}static{this.\u0275fac=function(y){return new(y||E)(w.KVO(w._9s),w.KVO(v.qQ))}}static{this.\u0275prov=w.jDH({token:E,factory:E.\u0275fac})}}return E})();class B extends ae{constructor(a,l){super(),this._id=a,this._renderer=l}create(a,l){return new Ke(this._id,a,l||{},this._renderer)}}class Ke{constructor(a,l,y,M){this.id=a,this.element=l,this._renderer=M,this.parentPlayer=null,this._started=!1,this.totalTime=0,this._command("create",y)}_listen(a,l){return this._renderer.listen(this.element,`@@${this.id}:${a}`,l)}_command(a,...l){return vt(this._renderer,this.element,this.id,a,l)}onDone(a){this._listen("done",a)}onStart(a){this._listen("start",a)}onDestroy(a){this._listen("destroy",a)}init(){this._command("init")}hasStarted(){return this._started}play(){this._command("play"),this._started=!0}pause(){this._command("pause")}restart(){this._command("restart")}finish(){this._command("finish")}destroy(){this._command("destroy")}reset(){this._command("reset"),this._started=!1}setPosition(a){this._command("setPosition",a)}getPosition(){return this._renderer.engine.players[+this.id]?.getPosition()??0}}function vt(E,a,l,y,M){return E.setProperty(a,`@@${l}:${y}`,M)}const ui="@.disabled";let Mi=(()=>{class E{constructor(l,y,M){this.delegate=l,this.engine=y,this._zone=M,this._currentId=0,this._microtaskId=1,this._animationCallbacksBuffer=[],this._rendererCache=new Map,this._cdRecurDepth=0,y.onRemovalComplete=(N,L)=>{const U=L?.parentNode(N);U&&L.removeChild(U,N)}}createRenderer(l,y){const N=this.delegate.createRenderer(l,y);if(!(l&&y&&y.data&&y.data.animation)){let Pe=this._rendererCache.get(N);return Pe||(Pe=new $i("",N,this.engine,()=>this._rendererCache.delete(N)),this._rendererCache.set(N,Pe)),Pe}const L=y.id,U=y.id+"-"+this._currentId;this._currentId++,this.engine.register(U,l);const q=Pe=>{Array.isArray(Pe)?Pe.forEach(q):this.engine.registerTrigger(L,U,l,Pe.name,Pe)};return y.data.animation.forEach(q),new to(this,U,N,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(l,y,M){l>=0&&l<this._microtaskId?this._zone.run(()=>y(M)):(0==this._animationCallbacksBuffer.length&&queueMicrotask(()=>{this._zone.run(()=>{this._animationCallbacksBuffer.forEach(N=>{const[L,U]=N;L(U)}),this._animationCallbacksBuffer=[]})}),this._animationCallbacksBuffer.push([y,M]))}end(){this._cdRecurDepth--,0==this._cdRecurDepth&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}static{this.\u0275fac=function(y){return new(y||E)(w.KVO(w._9s),w.KVO(Bi),w.KVO(w.SKi))}}static{this.\u0275prov=w.jDH({token:E,factory:E.\u0275fac})}}return E})();class $i{constructor(a,l,y,M){this.namespaceId=a,this.delegate=l,this.engine=y,this._onDestroy=M}get data(){return this.delegate.data}destroyNode(a){this.delegate.destroyNode?.(a)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(a,l){return this.delegate.createElement(a,l)}createComment(a){return this.delegate.createComment(a)}createText(a){return this.delegate.createText(a)}appendChild(a,l){this.delegate.appendChild(a,l),this.engine.onInsert(this.namespaceId,l,a,!1)}insertBefore(a,l,y,M=!0){this.delegate.insertBefore(a,l,y),this.engine.onInsert(this.namespaceId,l,a,M)}removeChild(a,l,y){this.engine.onRemove(this.namespaceId,l,this.delegate)}selectRootElement(a,l){return this.delegate.selectRootElement(a,l)}parentNode(a){return this.delegate.parentNode(a)}nextSibling(a){return this.delegate.nextSibling(a)}setAttribute(a,l,y,M){this.delegate.setAttribute(a,l,y,M)}removeAttribute(a,l,y){this.delegate.removeAttribute(a,l,y)}addClass(a,l){this.delegate.addClass(a,l)}removeClass(a,l){this.delegate.removeClass(a,l)}setStyle(a,l,y,M){this.delegate.setStyle(a,l,y,M)}removeStyle(a,l,y){this.delegate.removeStyle(a,l,y)}setProperty(a,l,y){"@"==l.charAt(0)&&l==ui?this.disableAnimations(a,!!y):this.delegate.setProperty(a,l,y)}setValue(a,l){this.delegate.setValue(a,l)}listen(a,l,y){return this.delegate.listen(a,l,y)}disableAnimations(a,l){this.engine.disableAnimations(a,l)}}class to extends $i{constructor(a,l,y,M,N){super(l,y,M,N),this.factory=a,this.namespaceId=l}setProperty(a,l,y){"@"==l.charAt(0)?"."==l.charAt(1)&&l==ui?this.disableAnimations(a,y=void 0===y||!!y):this.engine.process(this.namespaceId,a,l.slice(1),y):this.delegate.setProperty(a,l,y)}listen(a,l,y){if("@"==l.charAt(0)){const M=function Si(E){switch(E){case"body":return document.body;case"document":return document;case"window":return window;default:return E}}(a);let N=l.slice(1),L="";return"@"!=N.charAt(0)&&([N,L]=function Jr(E){const a=E.indexOf(".");return[E.substring(0,a),E.slice(a+1)]}(N)),this.engine.listen(this.namespaceId,M,N,L,U=>{this.factory.scheduleListenerCallback(U._data||-1,y,U)})}return this.delegate.listen(a,l,y)}}const fo=[{provide:de,useClass:P},{provide:Mt,useFactory:function fa(){return new Or}},{provide:Bi,useClass:(()=>{class E extends Bi{constructor(l,y,M,N){super(l.body,y,M)}ngOnDestroy(){this.flush()}static{this.\u0275fac=function(y){return new(y||E)(w.KVO(v.qQ),w.KVO(ot),w.KVO(Mt),w.KVO(w.o8S))}}static{this.\u0275prov=w.jDH({token:E,factory:E.\u0275fac})}}return E})()},{provide:w._9s,useFactory:function ts(E,a,l){return new Mi(E,a,l)},deps:[d.B7,Bi,w.SKi]}],Io=[{provide:ot,useFactory:()=>new S},{provide:w.bc$,useValue:"BrowserAnimations"},...fo],_s=[{provide:ot,useClass:yi},{provide:w.bc$,useValue:"NoopAnimations"},...fo];let Es=(()=>{class E{static withConfig(l){return{ngModule:E,providers:l.disableAnimations?_s:Io}}static{this.\u0275fac=function(y){return new(y||E)}}static{this.\u0275mod=w.$C({type:E})}static{this.\u0275inj=w.G2t({providers:Io,imports:[d.Bb]})}}return E})();var bs=R(1626),To=R(4341),cn=R(6448),cr=R(4978);const _r=[{path:"",redirectTo:"/auth/login",pathMatch:"full"},{path:"auth",loadChildren:()=>R.e(395).then(R.bind(R,8395)).then(E=>E.AuthModule)},{path:"patient",canActivate:[cr.q],data:{roles:["PATIENT"]},loadChildren:()=>Promise.all([R.e(211),R.e(919),R.e(860),R.e(76),R.e(440)]).then(R.bind(R,9440)).then(E=>E.PatientModule)},{path:"doctor",canActivate:[cr.q],data:{roles:["DOCTOR"]},loadChildren:()=>Promise.all([R.e(211),R.e(919),R.e(860),R.e(561)]).then(R.bind(R,9561)).then(E=>E.DoctorModule)},{path:"profile",canActivate:[cr.q],loadChildren:()=>R.e(809).then(R.bind(R,5809)).then(E=>E.ProfileModule)},{path:"appointments",canActivate:[cr.q],loadChildren:()=>Promise.all([R.e(211),R.e(919),R.e(76),R.e(402)]).then(R.bind(R,5402)).then(E=>E.AppointmentsModule)},{path:"chat",canActivate:[cr.q],loadChildren:()=>Promise.all([R.e(211),R.e(693)]).then(R.bind(R,2693)).then(E=>E.ChatModule)},{path:"**",redirectTo:"/auth/login"}];let ns=(()=>{class E{static{this.\u0275fac=function(y){return new(y||E)}}static{this.\u0275mod=w.$C({type:E})}static{this.\u0275inj=w.G2t({imports:[cn.iI.forRoot(_r),cn.iI]})}}return E})();var zo=R(5964),Ao=R(8010),ho=R(5567);function Oo(E,a){if(1&E&&(w.j41(0,"span",16),w.EFF(1),w.k0s()),2&E){const l=w.XpG();w.R7$(1),w.SpI(" ",l.unreadCount>99?"99+":l.unreadCount," ")}}function No(E,a){if(1&E){const l=w.RV6();w.j41(0,"button",17),w.bIt("click",function(){w.eBV(l);const M=w.XpG();return w.Njj(M.markAllAsRead())}),w.EFF(1," Mark all read "),w.k0s()}}function Po(E,a){if(1&E){const l=w.RV6();w.j41(0,"button",18),w.bIt("click",function(){w.eBV(l);const M=w.XpG();return w.Njj(M.clearAll())}),w.EFF(1," Clear all "),w.k0s()}}function Go(E,a){1&E&&(w.j41(0,"div",19)(1,"div",20)(2,"span",21),w.EFF(3,"Loading..."),w.k0s()()())}function rs(E,a){1&E&&(w.j41(0,"div",22)(1,"div",23),w.nrm(2,"i",24),w.j41(3,"p",25),w.EFF(4,"No notifications"),w.k0s()()())}function is(E,a){if(1&E&&w.nrm(0,"img",41),2&E){const l=w.XpG(2).$implicit;w.Y8G("src",l.fromUser.avatar,w.B4B)("alt",l.fromUser.name)}}function os(E,a){if(1&E&&(w.j41(0,"div",38),w.DNE(1,is,1,2,"img",39),w.j41(2,"span",40),w.EFF(3),w.k0s()()),2&E){const l=w.XpG().$implicit;w.R7$(1),w.Y8G("ngIf",l.fromUser.avatar),w.R7$(2),w.JRh(l.fromUser.name)}}function Hi(E,a){1&E&&w.nrm(0,"div",42)}function po(E,a){if(1&E){const l=w.RV6();w.j41(0,"div",26),w.bIt("click",function(){const N=w.eBV(l).$implicit,L=w.XpG();return w.Njj(L.handleNotificationClick(N))}),w.j41(1,"div",27)(2,"div",28),w.nrm(3,"i"),w.k0s(),w.j41(4,"div",29)(5,"div",30),w.EFF(6),w.k0s(),w.j41(7,"div",31),w.EFF(8),w.k0s(),w.DNE(9,os,4,2,"div",32),w.j41(10,"div",33),w.EFF(11),w.k0s()(),w.j41(12,"div",34)(13,"button",35),w.bIt("click",function(M){const L=w.eBV(l).$implicit,U=w.XpG();return w.Njj(U.removeNotification(L,M))}),w.nrm(14,"i",36),w.k0s()()(),w.DNE(15,Hi,1,0,"div",37),w.k0s()}if(2&E){const l=a.$implicit,y=w.XpG();w.HbH(y.getNotificationClass(l)),w.R7$(3),w.HbH(y.getNotificationIcon(l.type)),w.R7$(3),w.JRh(l.title),w.R7$(2),w.JRh(l.message),w.R7$(1),w.Y8G("ngIf",l.fromUser),w.R7$(2),w.JRh(y.formatTime(l.timestamp)),w.R7$(4),w.Y8G("ngIf",!l.read)}}function ro(E,a){1&E&&(w.j41(0,"div",43)(1,"a",44),w.EFF(2," View All Notifications "),w.k0s()())}let ss=(()=>{class E{constructor(l,y){this.notificationService=l,this.router=y,this.notifications=[],this.unreadCount=0,this.showDropdown=!1,this.loading=!1,this.subscriptions=[]}ngOnInit(){const l=this.notificationService.getNotifications().subscribe(M=>{this.notifications=M.slice(0,10)});this.subscriptions.push(l);const y=this.notificationService.getUnreadCount().subscribe(M=>this.unreadCount=M);this.subscriptions.push(y),this.notificationService.requestNotificationPermission()}ngOnDestroy(){this.subscriptions.forEach(l=>l.unsubscribe())}toggleDropdown(){this.showDropdown=!this.showDropdown}closeDropdown(){this.showDropdown=!1}markAsRead(l){l.read||this.notificationService.markAsRead(l.id)}markAllAsRead(){this.notificationService.markAllAsRead()}handleNotificationClick(l){this.markAsRead(l),l.actionUrl&&this.router.navigate([l.actionUrl]),this.closeDropdown()}removeNotification(l,y){y.stopPropagation(),this.notificationService.removeNotification(l.id)}clearAll(){this.notificationService.clearAll()}getNotificationIcon(l){switch(l){case"message":return"fas fa-comment";case"appointment":return"fas fa-calendar";case"urgent":return"fas fa-exclamation-triangle";case"system":return"fas fa-cog";default:return"fas fa-bell"}}getNotificationClass(l){const y=["notification-item"];return l.read||y.push("unread"),y.push(`priority-${l.priority}`),y.join(" ")}formatTime(l){const M=(new Date).getTime()-l.getTime(),N=Math.floor(M/6e4),L=Math.floor(N/60),U=Math.floor(L/24);return N<1?"Just now":N<60?`${N}m ago`:L<24?`${L}h ago`:U<7?`${U}d ago`:l.toLocaleDateString()}trackByNotificationId(l,y){return y.id}static{this.\u0275fac=function(y){return new(y||E)(w.rXU(ho.J),w.rXU(cn.Ix))}}static{this.\u0275cmp=w.VBU({type:E,selectors:[["app-notification-bell"]],decls:17,vars:12,consts:[[1,"notification-bell",3,"clickOutside"],["type","button",1,"btn","btn-link","notification-trigger",3,"click"],[1,"fas","fa-bell"],["class","badge bg-danger notification-badge",4,"ngIf"],[1,"notification-dropdown",3,"click"],[1,"dropdown-header"],[1,"d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"dropdown-actions"],["type","button","class","btn btn-sm btn-link text-primary",3,"click",4,"ngIf"],["type","button","class","btn btn-sm btn-link text-danger",3,"click",4,"ngIf"],[1,"notifications-list"],["class","text-center py-3",4,"ngIf"],["class","no-notifications",4,"ngIf"],[3,"class","click",4,"ngFor","ngForOf","ngForTrackBy"],["class","dropdown-footer",4,"ngIf"],[1,"badge","bg-danger","notification-badge"],["type","button",1,"btn","btn-sm","btn-link","text-primary",3,"click"],["type","button",1,"btn","btn-sm","btn-link","text-danger",3,"click"],[1,"text-center","py-3"],["role","status",1,"spinner-border","spinner-border-sm"],[1,"visually-hidden"],[1,"no-notifications"],[1,"text-center","py-4"],[1,"fas","fa-bell-slash","fa-2x","text-muted","mb-2"],[1,"text-muted","mb-0"],[3,"click"],[1,"notification-content"],[1,"notification-icon"],[1,"notification-body"],[1,"notification-title"],[1,"notification-message"],["class","notification-from",4,"ngIf"],[1,"notification-time"],[1,"notification-actions"],["type","button","title","Remove notification",1,"btn","btn-sm","btn-link","text-muted",3,"click"],[1,"fas","fa-times"],["class","unread-indicator",4,"ngIf"],[1,"notification-from"],["class","from-avatar",3,"src","alt",4,"ngIf"],[1,"from-name"],[1,"from-avatar",3,"src","alt"],[1,"unread-indicator"],[1,"dropdown-footer"],["routerLink","/notifications",1,"btn","btn-sm","btn-outline-primary","w-100"]],template:function(y,M){1&y&&(w.j41(0,"div",0),w.bIt("clickOutside",function(){return M.closeDropdown()}),w.j41(1,"button",1),w.bIt("click",function(){return M.toggleDropdown()}),w.nrm(2,"i",2),w.DNE(3,Oo,2,1,"span",3),w.k0s(),w.j41(4,"div",4),w.bIt("click",function(L){return L.stopPropagation()}),w.j41(5,"div",5)(6,"div",6)(7,"h6",7),w.EFF(8,"Notifications"),w.k0s(),w.j41(9,"div",8),w.DNE(10,No,2,0,"button",9),w.DNE(11,Po,2,0,"button",10),w.k0s()()(),w.j41(12,"div",11),w.DNE(13,Go,4,0,"div",12),w.DNE(14,rs,5,0,"div",13),w.DNE(15,po,16,9,"div",14),w.k0s(),w.DNE(16,ro,3,0,"div",15),w.k0s()()),2&y&&(w.R7$(1),w.AVh("has-notifications",M.unreadCount>0),w.R7$(2),w.Y8G("ngIf",M.unreadCount>0),w.R7$(1),w.AVh("show",M.showDropdown),w.R7$(6),w.Y8G("ngIf",M.unreadCount>0),w.R7$(1),w.Y8G("ngIf",M.notifications.length>0),w.R7$(2),w.Y8G("ngIf",M.loading),w.R7$(1),w.Y8G("ngIf",!M.loading&&0===M.notifications.length),w.R7$(1),w.Y8G("ngForOf",M.notifications)("ngForTrackBy",M.trackByNotificationId),w.R7$(1),w.Y8G("ngIf",M.notifications.length>0))},dependencies:[v.Sq,v.bT,cn.Wk],styles:[".notification-bell[_ngcontent-%COMP%]{position:relative;display:inline-block}.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]{position:relative;padding:.5rem;color:#6c757d;border:none;background:none;font-size:1.25rem;transition:color .2s ease}.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]:hover{color:#495057}.notification-bell[_ngcontent-%COMP%]   .notification-trigger.has-notifications[_ngcontent-%COMP%]{color:#007bff;animation:_ngcontent-%COMP%_bellShake 2s infinite}.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]   .notification-badge[_ngcontent-%COMP%]{position:absolute;top:0;right:0;font-size:.75rem;min-width:18px;height:18px;border-radius:9px;display:flex;align-items:center;justify-content:center;transform:translate(25%,-25%)}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]{position:absolute;top:100%;right:0;width:380px;max-height:500px;background:white;border:1px solid #dee2e6;border-radius:8px;box-shadow:0 4px 12px #00000026;z-index:1050;opacity:0;visibility:hidden;transform:translateY(-10px);transition:all .2s ease}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown.show[_ngcontent-%COMP%]{opacity:1;visibility:visible;transform:translateY(0)}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid #dee2e6;background:#f8f9fa;border-radius:8px 8px 0 0}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#495057;font-weight:600}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.875rem;text-decoration:none}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{text-decoration:underline}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]{max-height:350px;overflow-y:auto}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]{position:relative;padding:1rem;border-bottom:1px solid #f1f3f4;cursor:pointer;transition:background-color .2s ease}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.unread[_ngcontent-%COMP%]{background-color:#f0f8ff;border-left:3px solid #007bff}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.unread[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%]{font-weight:600}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-urgent[_ngcontent-%COMP%]{border-left-color:#dc3545}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-urgent[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#dc3545;animation:_ngcontent-%COMP%_pulse 2s infinite}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-high[_ngcontent-%COMP%]{border-left-color:#fd7e14}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:.75rem}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]{flex-shrink:0;width:32px;height:32px;border-radius:50%;background:#e9ecef;display:flex;align-items:center;justify-content:center}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]{flex:1;min-width:0}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%]{font-size:.875rem;color:#212529;margin-bottom:.25rem;line-height:1.4}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-message[_ngcontent-%COMP%]{font-size:.8125rem;color:#6c757d;line-height:1.4;margin-bottom:.5rem;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.25rem}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]   .from-avatar[_ngcontent-%COMP%]{width:20px;height:20px;border-radius:50%;object-fit:cover}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]   .from-name[_ngcontent-%COMP%]{font-size:.75rem;color:#495057;font-weight:500}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-time[_ngcontent-%COMP%]{font-size:.75rem;color:#adb5bd}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]{flex-shrink:0}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.25rem;border:none;background:none}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{background:#e9ecef;border-radius:4px}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .unread-indicator[_ngcontent-%COMP%]{position:absolute;top:50%;right:.5rem;width:8px;height:8px;background:#007bff;border-radius:50%;transform:translateY(-50%)}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .no-notifications[_ngcontent-%COMP%]{padding:2rem 1rem}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]{padding:.75rem 1rem;border-top:1px solid #dee2e6;background:#f8f9fa;border-radius:0 0 8px 8px}@keyframes _ngcontent-%COMP%_bellShake{0%,50%,to{transform:rotate(0)}10%,30%{transform:rotate(-10deg)}20%,40%{transform:rotate(10deg)}}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}@media (max-width: 768px){.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]{width:320px;right:-50px}}@media (max-width: 576px){.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]{width:280px;right:-100px}}"]})}}return E})();function Ro(E,a){1&E&&(w.qex(0),w.j41(1,"li",11)(2,"a",34),w.nrm(3,"i",35),w.EFF(4,"Appointments "),w.k0s()(),w.j41(5,"li",11)(6,"a",36),w.nrm(7,"i",37),w.EFF(8,"Find Doctors "),w.k0s()(),w.j41(9,"li",11)(10,"a",38),w.nrm(11,"i",39),w.EFF(12,"Health Assistant "),w.k0s()(),w.bVm())}function ha(E,a){1&E&&(w.qex(0),w.j41(1,"li",11)(2,"a",40),w.nrm(3,"i",41),w.EFF(4,"Patients "),w.k0s()(),w.j41(5,"li",11)(6,"a",34),w.nrm(7,"i",35),w.EFF(8,"Schedule "),w.k0s()(),w.bVm())}function h(E,a){if(1&E){const l=w.RV6();w.j41(0,"nav",3)(1,"div",4)(2,"a",5),w.bIt("click",function(){w.eBV(l);const M=w.XpG();return w.Njj(M.navigateToDashboard())}),w.nrm(3,"i",6),w.EFF(4,"HealthConnect "),w.k0s(),w.j41(5,"button",7),w.nrm(6,"span",8),w.k0s(),w.j41(7,"div",9)(8,"ul",10)(9,"li",11)(10,"a",12),w.bIt("click",function(){w.eBV(l);const M=w.XpG();return w.Njj(M.navigateToDashboard())}),w.nrm(11,"i",13),w.EFF(12,"Dashboard "),w.k0s()(),w.DNE(13,Ro,13,0,"ng-container",14),w.DNE(14,ha,9,0,"ng-container",14),w.j41(15,"li",11)(16,"a",15),w.nrm(17,"i",16),w.EFF(18,"Messages "),w.k0s()()(),w.j41(19,"ul",17)(20,"li",11),w.nrm(21,"app-notification-bell"),w.k0s(),w.j41(22,"li",18)(23,"a",19)(24,"div",20),w.nrm(25,"i",21),w.k0s(),w.j41(26,"span",22),w.EFF(27),w.k0s()(),w.j41(28,"ul",23)(29,"li")(30,"h6",24),w.EFF(31),w.nrm(32,"br"),w.j41(33,"small",25),w.EFF(34),w.nI1(35,"titlecase"),w.k0s()()(),w.j41(36,"li"),w.nrm(37,"hr",26),w.k0s(),w.j41(38,"li")(39,"a",27),w.bIt("click",function(){w.eBV(l);const M=w.XpG();return w.Njj(M.navigateToProfile())}),w.nrm(40,"i",28),w.EFF(41,"Profile Settings "),w.k0s()(),w.j41(42,"li")(43,"a",29),w.nrm(44,"i",30),w.EFF(45,"Notifications "),w.k0s()(),w.j41(46,"li")(47,"a",29),w.nrm(48,"i",31),w.EFF(49,"Help & Support "),w.k0s()(),w.j41(50,"li"),w.nrm(51,"hr",26),w.k0s(),w.j41(52,"li")(53,"a",32),w.bIt("click",function(){w.eBV(l);const M=w.XpG();return w.Njj(M.logout())}),w.nrm(54,"i",33),w.EFF(55,"Sign Out "),w.k0s()()()()()()()()}if(2&E){const l=w.XpG();w.R7$(13),w.Y8G("ngIf","PATIENT"===l.currentUser.role),w.R7$(1),w.Y8G("ngIf","DOCTOR"===l.currentUser.role),w.R7$(13),w.JRh(l.currentUser.fullName),w.R7$(4),w.SpI(" ",l.currentUser.fullName," "),w.R7$(3),w.JRh(w.bMT(35,5,l.currentUser.role))}}function T(E,a){1&E&&(w.j41(0,"footer",42)(1,"div",43)(2,"small",25),w.EFF(3," \xa9 2024 HealthConnect. All rights reserved. | "),w.j41(4,"a",44),w.EFF(5,"Privacy Policy"),w.k0s(),w.EFF(6," | "),w.j41(7,"a",44),w.EFF(8,"Terms of Service"),w.k0s()()()())}let D=(()=>{class E{constructor(l,y){this.authService=l,this.router=y,this.title="HealthConnect",this.currentUser=null,this.showNavigation=!1}ngOnInit(){this.authService.currentUser$.subscribe(l=>{this.currentUser=l,this.updateNavigationVisibility()}),this.router.events.pipe((0,zo.p)(l=>l instanceof cn.wF)).subscribe(l=>{this.updateNavigationVisibility()})}updateNavigationVisibility(){const l=this.router.url.includes("/auth"),y=this.authService.isAuthenticated();this.showNavigation=!l&&y&&!!this.currentUser}logout(){this.authService.logout()}navigateToProfile(){this.router.navigate(["/profile"])}navigateToDashboard(){"DOCTOR"===this.currentUser?.role?this.router.navigate(["/doctor/dashboard"]):"PATIENT"===this.currentUser?.role&&this.router.navigate(["/patient/dashboard"])}static{this.\u0275fac=function(y){return new(y||E)(w.rXU(Ao.u),w.rXU(cn.Ix))}}static{this.\u0275cmp=w.VBU({type:E,selectors:[["app-root"]],decls:4,vars:4,consts:[["class","navbar navbar-expand-lg navbar-dark bg-primary",4,"ngIf"],[1,"main-content"],["class","bg-light text-center py-3 mt-auto",4,"ngIf"],[1,"navbar","navbar-expand-lg","navbar-dark","bg-primary"],[1,"container-fluid"],[1,"navbar-brand","fw-bold",2,"cursor","pointer",3,"click"],[1,"bi","bi-heart-pulse","me-2"],["type","button","data-bs-toggle","collapse","data-bs-target","#navbarNav",1,"navbar-toggler"],[1,"navbar-toggler-icon"],["id","navbarNav",1,"collapse","navbar-collapse"],[1,"navbar-nav","me-auto"],[1,"nav-item"],[1,"nav-link",2,"cursor","pointer",3,"click"],[1,"bi","bi-house","me-1"],[4,"ngIf"],["routerLink","/chat","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-chat-dots","me-1"],[1,"navbar-nav"],[1,"nav-item","dropdown"],["href","#","id","navbarDropdown","role","button","data-bs-toggle","dropdown",1,"nav-link","dropdown-toggle","d-flex","align-items-center"],[1,"rounded-circle","bg-light","text-primary","d-flex","align-items-center","justify-content-center","me-2",2,"width","32px","height","32px"],[1,"bi","bi-person"],[1,"d-none","d-md-inline"],[1,"dropdown-menu","dropdown-menu-end"],[1,"dropdown-header"],[1,"text-muted"],[1,"dropdown-divider"],[1,"dropdown-item",2,"cursor","pointer",3,"click"],[1,"bi","bi-person-gear","me-2"],["href","#",1,"dropdown-item",2,"cursor","pointer"],[1,"bi","bi-bell","me-2"],[1,"bi","bi-question-circle","me-2"],[1,"dropdown-item","text-danger",2,"cursor","pointer",3,"click"],[1,"bi","bi-box-arrow-right","me-2"],["routerLink","/appointments","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-calendar","me-1"],["routerLink","/doctors","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-search","me-1"],["routerLink","/health-bot","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-robot","me-1"],["routerLink","/patients","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-people","me-1"],[1,"bg-light","text-center","py-3","mt-auto"],[1,"container"],["href","#",1,"text-decoration-none"]],template:function(y,M){1&y&&(w.DNE(0,h,56,7,"nav",0),w.j41(1,"main",1),w.nrm(2,"router-outlet"),w.k0s(),w.DNE(3,T,9,0,"footer",2)),2&y&&(w.Y8G("ngIf",M.showNavigation&&M.currentUser),w.R7$(1),w.AVh("with-navbar",M.showNavigation&&M.currentUser),w.R7$(2),w.Y8G("ngIf",M.showNavigation))},dependencies:[v.bT,cn.n3,cn.Wk,cn.wQ,ss,v.PV],styles:[".navbar[_ngcontent-%COMP%]{box-shadow:0 2px 4px #0000001a;z-index:1030}.navbar-brand[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700}.nav-link[_ngcontent-%COMP%]{font-weight:500;transition:all .3s ease}.nav-link[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;border-radius:.375rem}.nav-link.active[_ngcontent-%COMP%]{background-color:#fff3;border-radius:.375rem}.dropdown-menu[_ngcontent-%COMP%]{border:none;box-shadow:0 4px 6px #0000001a;border-radius:.5rem;min-width:200px}.dropdown-item[_ngcontent-%COMP%]{padding:.5rem 1rem;transition:all .3s ease}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.dropdown-header[_ngcontent-%COMP%]{font-weight:600;color:#495057}.main-content[_ngcontent-%COMP%]{min-height:calc(100vh - 60px);display:flex;flex-direction:column}.main-content.with-navbar[_ngcontent-%COMP%]{min-height:calc(100vh - 116px)}footer[_ngcontent-%COMP%]{margin-top:auto;border-top:1px solid #e9ecef}@media (max-width: 991px){.navbar-nav[_ngcontent-%COMP%]{padding-top:1rem}.navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{padding:.5rem 1rem}.dropdown-menu[_ngcontent-%COMP%]{position:static!important;transform:none!important;border:none;box-shadow:none;background-color:#ffffff1a;margin-top:.5rem}.dropdown-item[_ngcontent-%COMP%]{color:#fffc}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;color:#fff}.dropdown-header[_ngcontent-%COMP%]{color:#ffffffe6}.dropdown-divider[_ngcontent-%COMP%]{border-color:#fff3}}"]})}}return E})();var I=R(3443);let k=(()=>{class E{constructor(l){this.authService=l}intercept(l,y){const M=this.authService.getToken();if(M){const N=l.clone({headers:l.headers.set("Authorization",`Bearer ${M}`)});return y.handle(N)}return y.handle(l)}static{this.\u0275fac=function(y){return new(y||E)(w.KVO(Ao.u))}}static{this.\u0275prov=w.jDH({token:E,factory:E.\u0275fac})}}return E})(),re=(()=>{class E{constructor(l){if(l)throw new Error("CoreModule is already loaded. Import it in the AppModule only")}static{this.\u0275fac=function(y){return new(y||E)(w.KVO(E,12))}}static{this.\u0275mod=w.$C({type:E})}static{this.\u0275inj=w.G2t({providers:[Ao.u,I.D,cr.q,{provide:bs.a7,useClass:k,multi:!0}],imports:[v.MD]})}}return E})();var ve=R(3887);let qe=(()=>{class E{static{this.\u0275fac=function(y){return new(y||E)}}static{this.\u0275mod=w.$C({type:E,bootstrap:[D]})}static{this.\u0275inj=w.G2t({imports:[d.Bb,Es,bs.q1,To.X1,To.YN,ns,re,ve.G]})}}return E})();window.global=window,d.sG().bootstrapModule(qe).catch(E=>console.error(E))},4412:(tt,ye,R)=>{R.d(ye,{t:()=>w});var d=R(1413);class w extends d.B{constructor(ae){super(),this._value=ae}get value(){return this.getValue()}_subscribe(ae){const X=super._subscribe(ae);return!X.closed&&ae.next(this._value),X}getValue(){const{hasError:ae,thrownError:X,_value:oe}=this;if(ae)throw X;return this._throwIfClosed(),oe}next(ae){super.next(this._value=ae)}}},1985:(tt,ye,R)=>{R.d(ye,{c:()=>ge});var d=R(7707),w=R(8359),de=R(3494),ae=R(1203),X=R(1026),oe=R(8071),ie=R(9786);let ge=(()=>{class ne{constructor(Ne){Ne&&(this._subscribe=Ne)}lift(Ne){const Ge=new ne;return Ge.source=this,Ge.operator=Ne,Ge}subscribe(Ne,Ge,Lt){const Tt=function Z(ne){return ne&&ne instanceof d.vU||function pe(ne){return ne&&(0,oe.T)(ne.next)&&(0,oe.T)(ne.error)&&(0,oe.T)(ne.complete)}(ne)&&(0,w.Uv)(ne)}(Ne)?Ne:new d.Ms(Ne,Ge,Lt);return(0,ie.Y)(()=>{const{operator:Vt,source:jt}=this;Tt.add(Vt?Vt.call(Tt,jt):jt?this._subscribe(Tt):this._trySubscribe(Tt))}),Tt}_trySubscribe(Ne){try{return this._subscribe(Ne)}catch(Ge){Ne.error(Ge)}}forEach(Ne,Ge){return new(Ge=le(Ge))((Lt,Tt)=>{const Vt=new d.Ms({next:jt=>{try{Ne(jt)}catch(Qt){Tt(Qt),Vt.unsubscribe()}},error:Tt,complete:Lt});this.subscribe(Vt)})}_subscribe(Ne){var Ge;return null===(Ge=this.source)||void 0===Ge?void 0:Ge.subscribe(Ne)}[de.s](){return this}pipe(...Ne){return(0,ae.m)(Ne)(this)}toPromise(Ne){return new(Ne=le(Ne))((Ge,Lt)=>{let Tt;this.subscribe(Vt=>Tt=Vt,Vt=>Lt(Vt),()=>Ge(Tt))})}}return ne.create=be=>new ne(be),ne})();function le(ne){var be;return null!==(be=ne??X.$.Promise)&&void 0!==be?be:Promise}},1413:(tt,ye,R)=>{R.d(ye,{B:()=>ie});var d=R(1985),w=R(8359);const ae=(0,R(1853).L)(le=>function(){le(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var X=R(7908),oe=R(9786);let ie=(()=>{class le extends d.c{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(Z){const ne=new ge(this,this);return ne.operator=Z,ne}_throwIfClosed(){if(this.closed)throw new ae}next(Z){(0,oe.Y)(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(const ne of this.currentObservers)ne.next(Z)}})}error(Z){(0,oe.Y)(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=Z;const{observers:ne}=this;for(;ne.length;)ne.shift().error(Z)}})}complete(){(0,oe.Y)(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;const{observers:Z}=this;for(;Z.length;)Z.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var Z;return(null===(Z=this.observers)||void 0===Z?void 0:Z.length)>0}_trySubscribe(Z){return this._throwIfClosed(),super._trySubscribe(Z)}_subscribe(Z){return this._throwIfClosed(),this._checkFinalizedStatuses(Z),this._innerSubscribe(Z)}_innerSubscribe(Z){const{hasError:ne,isStopped:be,observers:Ne}=this;return ne||be?w.Kn:(this.currentObservers=null,Ne.push(Z),new w.yU(()=>{this.currentObservers=null,(0,X.o)(Ne,Z)}))}_checkFinalizedStatuses(Z){const{hasError:ne,thrownError:be,isStopped:Ne}=this;ne?Z.error(be):Ne&&Z.complete()}asObservable(){const Z=new d.c;return Z.source=this,Z}}return le.create=(pe,Z)=>new ge(pe,Z),le})();class ge extends ie{constructor(pe,Z){super(),this.destination=pe,this.source=Z}next(pe){var Z,ne;null===(ne=null===(Z=this.destination)||void 0===Z?void 0:Z.next)||void 0===ne||ne.call(Z,pe)}error(pe){var Z,ne;null===(ne=null===(Z=this.destination)||void 0===Z?void 0:Z.error)||void 0===ne||ne.call(Z,pe)}complete(){var pe,Z;null===(Z=null===(pe=this.destination)||void 0===pe?void 0:pe.complete)||void 0===Z||Z.call(pe)}_subscribe(pe){var Z,ne;return null!==(ne=null===(Z=this.source)||void 0===Z?void 0:Z.subscribe(pe))&&void 0!==ne?ne:w.Kn}}},7707:(tt,ye,R)=>{R.d(ye,{Ms:()=>Lt,vU:()=>ne});var d=R(8071),w=R(8359),de=R(1026),ae=R(5334),X=R(5343);const oe=le("C",void 0,void 0);function le(De,Te,Ve){return{kind:De,value:Te,error:Ve}}var pe=R(9270),Z=R(9786);class ne extends w.yU{constructor(Te){super(),this.isStopped=!1,Te?(this.destination=Te,(0,w.Uv)(Te)&&Te.add(this)):this.destination=Qt}static create(Te,Ve,nt){return new Lt(Te,Ve,nt)}next(Te){this.isStopped?jt(function ge(De){return le("N",De,void 0)}(Te),this):this._next(Te)}error(Te){this.isStopped?jt(function ie(De){return le("E",void 0,De)}(Te),this):(this.isStopped=!0,this._error(Te))}complete(){this.isStopped?jt(oe,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(Te){this.destination.next(Te)}_error(Te){try{this.destination.error(Te)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}}const be=Function.prototype.bind;function Ne(De,Te){return be.call(De,Te)}class Ge{constructor(Te){this.partialObserver=Te}next(Te){const{partialObserver:Ve}=this;if(Ve.next)try{Ve.next(Te)}catch(nt){Tt(nt)}}error(Te){const{partialObserver:Ve}=this;if(Ve.error)try{Ve.error(Te)}catch(nt){Tt(nt)}else Tt(Te)}complete(){const{partialObserver:Te}=this;if(Te.complete)try{Te.complete()}catch(Ve){Tt(Ve)}}}class Lt extends ne{constructor(Te,Ve,nt){let Re;if(super(),(0,d.T)(Te)||!Te)Re={next:Te??void 0,error:Ve??void 0,complete:nt??void 0};else{let Ae;this&&de.$.useDeprecatedNextContext?(Ae=Object.create(Te),Ae.unsubscribe=()=>this.unsubscribe(),Re={next:Te.next&&Ne(Te.next,Ae),error:Te.error&&Ne(Te.error,Ae),complete:Te.complete&&Ne(Te.complete,Ae)}):Re=Te}this.destination=new Ge(Re)}}function Tt(De){de.$.useDeprecatedSynchronousErrorHandling?(0,Z.l)(De):(0,ae.m)(De)}function jt(De,Te){const{onStoppedNotification:Ve}=de.$;Ve&&pe.f.setTimeout(()=>Ve(De,Te))}const Qt={closed:!0,next:X.l,error:function Vt(De){throw De},complete:X.l}},8359:(tt,ye,R)=>{R.d(ye,{Kn:()=>oe,yU:()=>X,Uv:()=>ie});var d=R(8071);const de=(0,R(1853).L)(le=>function(Z){le(this),this.message=Z?`${Z.length} errors occurred during unsubscription:\n${Z.map((ne,be)=>`${be+1}) ${ne.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=Z});var ae=R(7908);class X{constructor(pe){this.initialTeardown=pe,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let pe;if(!this.closed){this.closed=!0;const{_parentage:Z}=this;if(Z)if(this._parentage=null,Array.isArray(Z))for(const Ne of Z)Ne.remove(this);else Z.remove(this);const{initialTeardown:ne}=this;if((0,d.T)(ne))try{ne()}catch(Ne){pe=Ne instanceof de?Ne.errors:[Ne]}const{_finalizers:be}=this;if(be){this._finalizers=null;for(const Ne of be)try{ge(Ne)}catch(Ge){pe=pe??[],Ge instanceof de?pe=[...pe,...Ge.errors]:pe.push(Ge)}}if(pe)throw new de(pe)}}add(pe){var Z;if(pe&&pe!==this)if(this.closed)ge(pe);else{if(pe instanceof X){if(pe.closed||pe._hasParent(this))return;pe._addParent(this)}(this._finalizers=null!==(Z=this._finalizers)&&void 0!==Z?Z:[]).push(pe)}}_hasParent(pe){const{_parentage:Z}=this;return Z===pe||Array.isArray(Z)&&Z.includes(pe)}_addParent(pe){const{_parentage:Z}=this;this._parentage=Array.isArray(Z)?(Z.push(pe),Z):Z?[Z,pe]:pe}_removeParent(pe){const{_parentage:Z}=this;Z===pe?this._parentage=null:Array.isArray(Z)&&(0,ae.o)(Z,pe)}remove(pe){const{_finalizers:Z}=this;Z&&(0,ae.o)(Z,pe),pe instanceof X&&pe._removeParent(this)}}X.EMPTY=(()=>{const le=new X;return le.closed=!0,le})();const oe=X.EMPTY;function ie(le){return le instanceof X||le&&"closed"in le&&(0,d.T)(le.remove)&&(0,d.T)(le.add)&&(0,d.T)(le.unsubscribe)}function ge(le){(0,d.T)(le)?le():le.unsubscribe()}},1026:(tt,ye,R)=>{R.d(ye,{$:()=>d});const d={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1}},983:(tt,ye,R)=>{R.d(ye,{w:()=>w});const w=new(R(1985).c)(X=>X.complete())},6648:(tt,ye,R)=>{R.d(ye,{H:()=>nt});var d=R(8750),w=R(5225),de=R(9974),ae=R(4360);function X(Re,Ae=0){return(0,de.N)((Ee,rt)=>{Ee.subscribe((0,ae._)(rt,Ft=>(0,w.N)(rt,Re,()=>rt.next(Ft),Ae),()=>(0,w.N)(rt,Re,()=>rt.complete(),Ae),Ft=>(0,w.N)(rt,Re,()=>rt.error(Ft),Ae)))})}function oe(Re,Ae=0){return(0,de.N)((Ee,rt)=>{rt.add(Re.schedule(()=>Ee.subscribe(rt),Ae))})}var le=R(1985),Z=R(4761),ne=R(8071);function Ne(Re,Ae){if(!Re)throw new Error("Iterable cannot be null");return new le.c(Ee=>{(0,w.N)(Ee,Ae,()=>{const rt=Re[Symbol.asyncIterator]();(0,w.N)(Ee,Ae,()=>{rt.next().then(Ft=>{Ft.done?Ee.complete():Ee.next(Ft.value)})},0,!0)})})}var Ge=R(5055),Lt=R(9858),Tt=R(7441),Vt=R(5397),jt=R(7953),Qt=R(591),De=R(5196);function nt(Re,Ae){return Ae?function Ve(Re,Ae){if(null!=Re){if((0,Ge.l)(Re))return function ie(Re,Ae){return(0,d.Tg)(Re).pipe(oe(Ae),X(Ae))}(Re,Ae);if((0,Tt.X)(Re))return function pe(Re,Ae){return new le.c(Ee=>{let rt=0;return Ae.schedule(function(){rt===Re.length?Ee.complete():(Ee.next(Re[rt++]),Ee.closed||this.schedule())})})}(Re,Ae);if((0,Lt.y)(Re))return function ge(Re,Ae){return(0,d.Tg)(Re).pipe(oe(Ae),X(Ae))}(Re,Ae);if((0,jt.T)(Re))return Ne(Re,Ae);if((0,Vt.x)(Re))return function be(Re,Ae){return new le.c(Ee=>{let rt;return(0,w.N)(Ee,Ae,()=>{rt=Re[Z.l](),(0,w.N)(Ee,Ae,()=>{let Ft,_n;try{({value:Ft,done:_n}=rt.next())}catch($n){return void Ee.error($n)}_n?Ee.complete():Ee.next(Ft)},0,!0)}),()=>(0,ne.T)(rt?.return)&&rt.return()})}(Re,Ae);if((0,De.U)(Re))return function Te(Re,Ae){return Ne((0,De.C)(Re),Ae)}(Re,Ae)}throw(0,Qt.L)(Re)}(Re,Ae):(0,d.Tg)(Re)}},8750:(tt,ye,R)=>{R.d(ye,{Tg:()=>be});var d=R(1635),w=R(7441),de=R(9858),ae=R(1985),X=R(5055),oe=R(7953),ie=R(591),ge=R(5397),le=R(5196),pe=R(8071),Z=R(5334),ne=R(3494);function be(De){if(De instanceof ae.c)return De;if(null!=De){if((0,X.l)(De))return function Ne(De){return new ae.c(Te=>{const Ve=De[ne.s]();if((0,pe.T)(Ve.subscribe))return Ve.subscribe(Te);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}(De);if((0,w.X)(De))return function Ge(De){return new ae.c(Te=>{for(let Ve=0;Ve<De.length&&!Te.closed;Ve++)Te.next(De[Ve]);Te.complete()})}(De);if((0,de.y)(De))return function Lt(De){return new ae.c(Te=>{De.then(Ve=>{Te.closed||(Te.next(Ve),Te.complete())},Ve=>Te.error(Ve)).then(null,Z.m)})}(De);if((0,oe.T)(De))return Vt(De);if((0,ge.x)(De))return function Tt(De){return new ae.c(Te=>{for(const Ve of De)if(Te.next(Ve),Te.closed)return;Te.complete()})}(De);if((0,le.U)(De))return function jt(De){return Vt((0,le.C)(De))}(De)}throw(0,ie.L)(De)}function Vt(De){return new ae.c(Te=>{(function Qt(De,Te){var Ve,nt,Re,Ae;return(0,d.sH)(this,void 0,void 0,function*(){try{for(Ve=(0,d.xN)(De);!(nt=yield Ve.next()).done;)if(Te.next(nt.value),Te.closed)return}catch(Ee){Re={error:Ee}}finally{try{nt&&!nt.done&&(Ae=Ve.return)&&(yield Ae.call(Ve))}finally{if(Re)throw Re.error}}Te.complete()})})(De,Te).catch(Ve=>Te.error(Ve))})}},7673:(tt,ye,R)=>{R.d(ye,{of:()=>de});var d=R(9326),w=R(6648);function de(...ae){const X=(0,d.lI)(ae);return(0,w.H)(ae,X)}},8810:(tt,ye,R)=>{R.d(ye,{$:()=>de});var d=R(1985),w=R(8071);function de(ae,X){const oe=(0,w.T)(ae)?ae:()=>ae,ie=ge=>ge.error(oe());return new d.c(X?ge=>X.schedule(ie,0,ge):ie)}},4360:(tt,ye,R)=>{R.d(ye,{_:()=>w});var d=R(7707);function w(ae,X,oe,ie,ge){return new de(ae,X,oe,ie,ge)}class de extends d.vU{constructor(X,oe,ie,ge,le,pe){super(X),this.onFinalize=le,this.shouldUnsubscribe=pe,this._next=oe?function(Z){try{oe(Z)}catch(ne){X.error(ne)}}:super._next,this._error=ge?function(Z){try{ge(Z)}catch(ne){X.error(ne)}finally{this.unsubscribe()}}:super._error,this._complete=ie?function(){try{ie()}catch(Z){X.error(Z)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var X;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){const{closed:oe}=this;super.unsubscribe(),!oe&&(null===(X=this.onFinalize)||void 0===X||X.call(this))}}}},9437:(tt,ye,R)=>{R.d(ye,{W:()=>ae});var d=R(8750),w=R(4360),de=R(9974);function ae(X){return(0,de.N)((oe,ie)=>{let pe,ge=null,le=!1;ge=oe.subscribe((0,w._)(ie,void 0,void 0,Z=>{pe=(0,d.Tg)(X(Z,ae(X)(oe))),ge?(ge.unsubscribe(),ge=null,pe.subscribe(ie)):le=!0})),le&&(ge.unsubscribe(),ge=null,pe.subscribe(ie))})}},274:(tt,ye,R)=>{R.d(ye,{H:()=>de});var d=R(1397),w=R(8071);function de(ae,X){return(0,w.T)(X)?(0,d.Z)(ae,X,1):(0,d.Z)(ae,1)}},5964:(tt,ye,R)=>{R.d(ye,{p:()=>de});var d=R(9974),w=R(4360);function de(ae,X){return(0,d.N)((oe,ie)=>{let ge=0;oe.subscribe((0,w._)(ie,le=>ae.call(X,le,ge++)&&ie.next(le)))})}},980:(tt,ye,R)=>{R.d(ye,{j:()=>w});var d=R(9974);function w(de){return(0,d.N)((ae,X)=>{try{ae.subscribe(X)}finally{X.add(de)}})}},6354:(tt,ye,R)=>{R.d(ye,{T:()=>de});var d=R(9974),w=R(4360);function de(ae,X){return(0,d.N)((oe,ie)=>{let ge=0;oe.subscribe((0,w._)(ie,le=>{ie.next(ae.call(X,le,ge++))}))})}},6365:(tt,ye,R)=>{R.d(ye,{U:()=>de});var d=R(1397),w=R(3669);function de(ae=1/0){return(0,d.Z)(w.D,ae)}},1397:(tt,ye,R)=>{R.d(ye,{Z:()=>ge});var d=R(6354),w=R(8750),de=R(9974),ae=R(5225),X=R(4360),ie=R(8071);function ge(le,pe,Z=1/0){return(0,ie.T)(pe)?ge((ne,be)=>(0,d.T)((Ne,Ge)=>pe(ne,Ne,be,Ge))((0,w.Tg)(le(ne,be))),Z):("number"==typeof pe&&(Z=pe),(0,de.N)((ne,be)=>function oe(le,pe,Z,ne,be,Ne,Ge,Lt){const Tt=[];let Vt=0,jt=0,Qt=!1;const De=()=>{Qt&&!Tt.length&&!Vt&&pe.complete()},Te=nt=>Vt<ne?Ve(nt):Tt.push(nt),Ve=nt=>{Ne&&pe.next(nt),Vt++;let Re=!1;(0,w.Tg)(Z(nt,jt++)).subscribe((0,X._)(pe,Ae=>{be?.(Ae),Ne?Te(Ae):pe.next(Ae)},()=>{Re=!0},void 0,()=>{if(Re)try{for(Vt--;Tt.length&&Vt<ne;){const Ae=Tt.shift();Ge?(0,ae.N)(pe,Ge,()=>Ve(Ae)):Ve(Ae)}De()}catch(Ae){pe.error(Ae)}}))};return le.subscribe((0,X._)(pe,Te,()=>{Qt=!0,De()})),()=>{Lt?.()}}(ne,be,le,Z)))}},5558:(tt,ye,R)=>{R.d(ye,{n:()=>ae});var d=R(8750),w=R(9974),de=R(4360);function ae(X,oe){return(0,w.N)((ie,ge)=>{let le=null,pe=0,Z=!1;const ne=()=>Z&&!le&&ge.complete();ie.subscribe((0,de._)(ge,be=>{le?.unsubscribe();let Ne=0;const Ge=pe++;(0,d.Tg)(X(be,Ge)).subscribe(le=(0,de._)(ge,Lt=>ge.next(oe?oe(be,Lt,Ge,Ne++):Lt),()=>{le=null,ne()}))},()=>{Z=!0,ne()}))})}},8141:(tt,ye,R)=>{R.d(ye,{M:()=>X});var d=R(8071),w=R(9974),de=R(4360),ae=R(3669);function X(oe,ie,ge){const le=(0,d.T)(oe)||ie||ge?{next:oe,error:ie,complete:ge}:oe;return le?(0,w.N)((pe,Z)=>{var ne;null===(ne=le.subscribe)||void 0===ne||ne.call(le);let be=!0;pe.subscribe((0,de._)(Z,Ne=>{var Ge;null===(Ge=le.next)||void 0===Ge||Ge.call(le,Ne),Z.next(Ne)},()=>{var Ne;be=!1,null===(Ne=le.complete)||void 0===Ne||Ne.call(le),Z.complete()},Ne=>{var Ge;be=!1,null===(Ge=le.error)||void 0===Ge||Ge.call(le,Ne),Z.error(Ne)},()=>{var Ne,Ge;be&&(null===(Ne=le.unsubscribe)||void 0===Ne||Ne.call(le)),null===(Ge=le.finalize)||void 0===Ge||Ge.call(le)}))}):ae.D}},9270:(tt,ye,R)=>{R.d(ye,{f:()=>d});const d={setTimeout(w,de,...ae){const{delegate:X}=d;return X?.setTimeout?X.setTimeout(w,de,...ae):setTimeout(w,de,...ae)},clearTimeout(w){const{delegate:de}=d;return(de?.clearTimeout||clearTimeout)(w)},delegate:void 0}},4761:(tt,ye,R)=>{R.d(ye,{l:()=>w});const w=function d(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}()},3494:(tt,ye,R)=>{R.d(ye,{s:()=>d});const d="function"==typeof Symbol&&Symbol.observable||"@@observable"},9326:(tt,ye,R)=>{R.d(ye,{R0:()=>oe,lI:()=>X,ms:()=>ae});var d=R(8071),w=R(9470);function de(ie){return ie[ie.length-1]}function ae(ie){return(0,d.T)(de(ie))?ie.pop():void 0}function X(ie){return(0,w.m)(de(ie))?ie.pop():void 0}function oe(ie,ge){return"number"==typeof de(ie)?ie.pop():ge}},3073:(tt,ye,R)=>{R.d(ye,{D:()=>X});const{isArray:d}=Array,{getPrototypeOf:w,prototype:de,keys:ae}=Object;function X(ie){if(1===ie.length){const ge=ie[0];if(d(ge))return{args:ge,keys:null};if(function oe(ie){return ie&&"object"==typeof ie&&w(ie)===de}(ge)){const le=ae(ge);return{args:le.map(pe=>ge[pe]),keys:le}}}return{args:ie,keys:null}}},7908:(tt,ye,R)=>{function d(w,de){if(w){const ae=w.indexOf(de);0<=ae&&w.splice(ae,1)}}R.d(ye,{o:()=>d})},1853:(tt,ye,R)=>{function d(w){const ae=w(X=>{Error.call(X),X.stack=(new Error).stack});return ae.prototype=Object.create(Error.prototype),ae.prototype.constructor=ae,ae}R.d(ye,{L:()=>d})},8496:(tt,ye,R)=>{function d(w,de){return w.reduce((ae,X,oe)=>(ae[X]=de[oe],ae),{})}R.d(ye,{e:()=>d})},9786:(tt,ye,R)=>{R.d(ye,{Y:()=>de,l:()=>ae});var d=R(1026);let w=null;function de(X){if(d.$.useDeprecatedSynchronousErrorHandling){const oe=!w;if(oe&&(w={errorThrown:!1,error:null}),X(),oe){const{errorThrown:ie,error:ge}=w;if(w=null,ie)throw ge}}else X()}function ae(X){d.$.useDeprecatedSynchronousErrorHandling&&w&&(w.errorThrown=!0,w.error=X)}},5225:(tt,ye,R)=>{function d(w,de,ae,X=0,oe=!1){const ie=de.schedule(function(){ae(),oe?w.add(this.schedule(null,X)):this.unsubscribe()},X);if(w.add(ie),!oe)return ie}R.d(ye,{N:()=>d})},3669:(tt,ye,R)=>{function d(w){return w}R.d(ye,{D:()=>d})},7441:(tt,ye,R)=>{R.d(ye,{X:()=>d});const d=w=>w&&"number"==typeof w.length&&"function"!=typeof w},7953:(tt,ye,R)=>{R.d(ye,{T:()=>w});var d=R(8071);function w(de){return Symbol.asyncIterator&&(0,d.T)(de?.[Symbol.asyncIterator])}},8071:(tt,ye,R)=>{function d(w){return"function"==typeof w}R.d(ye,{T:()=>d})},5055:(tt,ye,R)=>{R.d(ye,{l:()=>de});var d=R(3494),w=R(8071);function de(ae){return(0,w.T)(ae[d.s])}},5397:(tt,ye,R)=>{R.d(ye,{x:()=>de});var d=R(4761),w=R(8071);function de(ae){return(0,w.T)(ae?.[d.l])}},9858:(tt,ye,R)=>{R.d(ye,{y:()=>w});var d=R(8071);function w(de){return(0,d.T)(de?.then)}},5196:(tt,ye,R)=>{R.d(ye,{C:()=>de,U:()=>ae});var d=R(1635),w=R(8071);function de(X){return(0,d.AQ)(this,arguments,function*(){const ie=X.getReader();try{for(;;){const{value:ge,done:le}=yield(0,d.N3)(ie.read());if(le)return yield(0,d.N3)(void 0);yield yield(0,d.N3)(ge)}}finally{ie.releaseLock()}})}function ae(X){return(0,w.T)(X?.getReader)}},9470:(tt,ye,R)=>{R.d(ye,{m:()=>w});var d=R(8071);function w(de){return de&&(0,d.T)(de.schedule)}},9974:(tt,ye,R)=>{R.d(ye,{N:()=>de,S:()=>w});var d=R(8071);function w(ae){return(0,d.T)(ae?.lift)}function de(ae){return X=>{if(w(X))return X.lift(function(oe){try{return ae(oe,this)}catch(ie){this.error(ie)}});throw new TypeError("Unable to lift unknown Observable type")}}},6450:(tt,ye,R)=>{R.d(ye,{I:()=>ae});var d=R(6354);const{isArray:w}=Array;function ae(X){return(0,d.T)(oe=>function de(X,oe){return w(oe)?X(...oe):X(oe)}(X,oe))}},5343:(tt,ye,R)=>{function d(){}R.d(ye,{l:()=>d})},1203:(tt,ye,R)=>{R.d(ye,{F:()=>w,m:()=>de});var d=R(3669);function w(...ae){return de(ae)}function de(ae){return 0===ae.length?d.D:1===ae.length?ae[0]:function(oe){return ae.reduce((ie,ge)=>ge(ie),oe)}}},5334:(tt,ye,R)=>{R.d(ye,{m:()=>de});var d=R(1026),w=R(9270);function de(ae){w.f.setTimeout(()=>{const{onUnhandledError:X}=d.$;if(!X)throw ae;X(ae)})}},591:(tt,ye,R)=>{function d(w){return new TypeError(`You provided ${null!==w&&"object"==typeof w?"an invalid object":`'${w}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}R.d(ye,{L:()=>d})},177:(tt,ye,R)=>{R.d(ye,{AJ:()=>Qr,MD:()=>Nr,N0:()=>ur,P9:()=>bn,PV:()=>qi,QT:()=>de,Sm:()=>Ge,Sq:()=>or,VF:()=>X,Vy:()=>mt,Xr:()=>Ln,YU:()=>Rt,ZD:()=>ae,_b:()=>Kr,aZ:()=>Tt,bT:()=>Tr,fw:()=>Lt,hb:()=>be,hj:()=>ge,qQ:()=>oe,vh:()=>si});var d=R(540);let w=null;function de(){return w}function ae(h){w||(w=h)}class X{}const oe=new d.nKC("DocumentToken");let ie=(()=>{class h{historyGo(D){throw new Error("Not implemented")}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275prov=d.jDH({token:h,factory:function(){return(0,d.WQX)(le)},providedIn:"platform"})}}return h})();const ge=new d.nKC("Location Initialized");let le=(()=>{class h extends ie{constructor(){super(),this._doc=(0,d.WQX)(oe),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return de().getBaseHref(this._doc)}onPopState(D){const I=de().getGlobalEventTarget(this._doc,"window");return I.addEventListener("popstate",D,!1),()=>I.removeEventListener("popstate",D)}onHashChange(D){const I=de().getGlobalEventTarget(this._doc,"window");return I.addEventListener("hashchange",D,!1),()=>I.removeEventListener("hashchange",D)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(D){this._location.pathname=D}pushState(D,I,k){this._history.pushState(D,I,k)}replaceState(D,I,k){this._history.replaceState(D,I,k)}forward(){this._history.forward()}back(){this._history.back()}historyGo(D=0){this._history.go(D)}getState(){return this._history.state}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275prov=d.jDH({token:h,factory:function(){return new h},providedIn:"platform"})}}return h})();function pe(h,T){if(0==h.length)return T;if(0==T.length)return h;let D=0;return h.endsWith("/")&&D++,T.startsWith("/")&&D++,2==D?h+T.substring(1):1==D?h+T:h+"/"+T}function Z(h){const T=h.match(/#|\?|$/),D=T&&T.index||h.length;return h.slice(0,D-("/"===h[D-1]?1:0))+h.slice(D)}function ne(h){return h&&"?"!==h[0]?"?"+h:h}let be=(()=>{class h{historyGo(D){throw new Error("Not implemented")}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275prov=d.jDH({token:h,factory:function(){return(0,d.WQX)(Ge)},providedIn:"root"})}}return h})();const Ne=new d.nKC("appBaseHref");let Ge=(()=>{class h extends be{constructor(D,I){super(),this._platformLocation=D,this._removeListenerFns=[],this._baseHref=I??this._platformLocation.getBaseHrefFromDOM()??(0,d.WQX)(oe).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(D){this._removeListenerFns.push(this._platformLocation.onPopState(D),this._platformLocation.onHashChange(D))}getBaseHref(){return this._baseHref}prepareExternalUrl(D){return pe(this._baseHref,D)}path(D=!1){const I=this._platformLocation.pathname+ne(this._platformLocation.search),k=this._platformLocation.hash;return k&&D?`${I}${k}`:I}pushState(D,I,k,re){const ve=this.prepareExternalUrl(k+ne(re));this._platformLocation.pushState(D,I,ve)}replaceState(D,I,k,re){const ve=this.prepareExternalUrl(k+ne(re));this._platformLocation.replaceState(D,I,ve)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(D=0){this._platformLocation.historyGo?.(D)}static{this.\u0275fac=function(I){return new(I||h)(d.KVO(ie),d.KVO(Ne,8))}}static{this.\u0275prov=d.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})(),Lt=(()=>{class h extends be{constructor(D,I){super(),this._platformLocation=D,this._baseHref="",this._removeListenerFns=[],null!=I&&(this._baseHref=I)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(D){this._removeListenerFns.push(this._platformLocation.onPopState(D),this._platformLocation.onHashChange(D))}getBaseHref(){return this._baseHref}path(D=!1){let I=this._platformLocation.hash;return null==I&&(I="#"),I.length>0?I.substring(1):I}prepareExternalUrl(D){const I=pe(this._baseHref,D);return I.length>0?"#"+I:I}pushState(D,I,k,re){let ve=this.prepareExternalUrl(k+ne(re));0==ve.length&&(ve=this._platformLocation.pathname),this._platformLocation.pushState(D,I,ve)}replaceState(D,I,k,re){let ve=this.prepareExternalUrl(k+ne(re));0==ve.length&&(ve=this._platformLocation.pathname),this._platformLocation.replaceState(D,I,ve)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(D=0){this._platformLocation.historyGo?.(D)}static{this.\u0275fac=function(I){return new(I||h)(d.KVO(ie),d.KVO(Ne,8))}}static{this.\u0275prov=d.jDH({token:h,factory:h.\u0275fac})}}return h})(),Tt=(()=>{class h{constructor(D){this._subject=new d.bkB,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=D;const I=this._locationStrategy.getBaseHref();this._basePath=function De(h){if(new RegExp("^(https?:)?//").test(h)){const[,D]=h.split(/\/\/[^\/]+/);return D}return h}(Z(Qt(I))),this._locationStrategy.onPopState(k=>{this._subject.emit({url:this.path(!0),pop:!0,state:k.state,type:k.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(D=!1){return this.normalize(this._locationStrategy.path(D))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(D,I=""){return this.path()==this.normalize(D+ne(I))}normalize(D){return h.stripTrailingSlash(function jt(h,T){if(!h||!T.startsWith(h))return T;const D=T.substring(h.length);return""===D||["/",";","?","#"].includes(D[0])?D:T}(this._basePath,Qt(D)))}prepareExternalUrl(D){return D&&"/"!==D[0]&&(D="/"+D),this._locationStrategy.prepareExternalUrl(D)}go(D,I="",k=null){this._locationStrategy.pushState(k,"",D,I),this._notifyUrlChangeListeners(this.prepareExternalUrl(D+ne(I)),k)}replaceState(D,I="",k=null){this._locationStrategy.replaceState(k,"",D,I),this._notifyUrlChangeListeners(this.prepareExternalUrl(D+ne(I)),k)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(D=0){this._locationStrategy.historyGo?.(D)}onUrlChange(D){return this._urlChangeListeners.push(D),this._urlChangeSubscription||(this._urlChangeSubscription=this.subscribe(I=>{this._notifyUrlChangeListeners(I.url,I.state)})),()=>{const I=this._urlChangeListeners.indexOf(D);this._urlChangeListeners.splice(I,1),0===this._urlChangeListeners.length&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(D="",I){this._urlChangeListeners.forEach(k=>k(D,I))}subscribe(D,I,k){return this._subject.subscribe({next:D,error:I,complete:k})}static{this.normalizeQueryParams=ne}static{this.joinWithSlash=pe}static{this.stripTrailingSlash=Z}static{this.\u0275fac=function(I){return new(I||h)(d.KVO(be))}}static{this.\u0275prov=d.jDH({token:h,factory:function(){return function Vt(){return new Tt((0,d.KVO)(be))}()},providedIn:"root"})}}return h})();function Qt(h){return h.replace(/\/index.html$/,"")}var Re=function(h){return h[h.Format=0]="Format",h[h.Standalone=1]="Standalone",h}(Re||{}),Ae=function(h){return h[h.Narrow=0]="Narrow",h[h.Abbreviated=1]="Abbreviated",h[h.Wide=2]="Wide",h[h.Short=3]="Short",h}(Ae||{}),Ee=function(h){return h[h.Short=0]="Short",h[h.Medium=1]="Medium",h[h.Long=2]="Long",h[h.Full=3]="Full",h}(Ee||{}),rt=function(h){return h[h.Decimal=0]="Decimal",h[h.Group=1]="Group",h[h.List=2]="List",h[h.PercentSign=3]="PercentSign",h[h.PlusSign=4]="PlusSign",h[h.MinusSign=5]="MinusSign",h[h.Exponential=6]="Exponential",h[h.SuperscriptingExponent=7]="SuperscriptingExponent",h[h.PerMille=8]="PerMille",h[h.Infinity=9]="Infinity",h[h.NaN=10]="NaN",h[h.TimeSeparator=11]="TimeSeparator",h[h.CurrencyDecimal=12]="CurrencyDecimal",h[h.CurrencyGroup=13]="CurrencyGroup",h}(rt||{});function Fr(h,T){return gt((0,d.H5H)(h)[d.KH2.DateFormat],T)}function Q(h,T){return gt((0,d.H5H)(h)[d.KH2.TimeFormat],T)}function Y(h,T){return gt((0,d.H5H)(h)[d.KH2.DateTimeFormat],T)}function J(h,T){const D=(0,d.H5H)(h),I=D[d.KH2.NumberSymbols][T];if(typeof I>"u"){if(T===rt.CurrencyDecimal)return D[d.KH2.NumberSymbols][rt.Decimal];if(T===rt.CurrencyGroup)return D[d.KH2.NumberSymbols][rt.Group]}return I}function at(h){if(!h[d.KH2.ExtraData])throw new Error(`Missing extra locale data for the locale "${h[d.KH2.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function gt(h,T){for(let D=T;D>-1;D--)if(typeof h[D]<"u")return h[D];throw new Error("Locale data API: locale data undefined")}function fn(h){const[T,D]=h.split(":");return{hours:+T,minutes:+D}}const it=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Yn={},Ut=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;var Tn=function(h){return h[h.Short=0]="Short",h[h.ShortGMT=1]="ShortGMT",h[h.Long=2]="Long",h[h.Extended=3]="Extended",h}(Tn||{}),ht=function(h){return h[h.FullYear=0]="FullYear",h[h.Month=1]="Month",h[h.Date=2]="Date",h[h.Hours=3]="Hours",h[h.Minutes=4]="Minutes",h[h.Seconds=5]="Seconds",h[h.FractionalSeconds=6]="FractionalSeconds",h[h.Day=7]="Day",h}(ht||{}),Ct=function(h){return h[h.DayPeriods=0]="DayPeriods",h[h.Days=1]="Days",h[h.Months=2]="Months",h[h.Eras=3]="Eras",h}(Ct||{});function En(h,T,D,I){let k=function ce(h){if(V(h))return h;if("number"==typeof h&&!isNaN(h))return new Date(h);if("string"==typeof h){if(h=h.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(h)){const[k,re=1,ve=1]=h.split("-").map(qe=>+qe);return Hn(k,re-1,ve)}const D=parseFloat(h);if(!isNaN(h-D))return new Date(D);let I;if(I=h.match(it))return function K(h){const T=new Date(0);let D=0,I=0;const k=h[8]?T.setUTCFullYear:T.setFullYear,re=h[8]?T.setUTCHours:T.setHours;h[9]&&(D=Number(h[9]+h[10]),I=Number(h[9]+h[11])),k.call(T,Number(h[1]),Number(h[2])-1,Number(h[3]));const ve=Number(h[4]||0)-D,qe=Number(h[5]||0)-I,E=Number(h[6]||0),a=Math.floor(1e3*parseFloat("0."+(h[7]||0)));return re.call(T,ve,qe,E,a),T}(I)}const T=new Date(h);if(!V(T))throw new Error(`Unable to convert "${h}" into a date`);return T}(h);T=kn(D,T)||T;let qe,ve=[];for(;T;){if(qe=Ut.exec(T),!qe){ve.push(T);break}{ve=ve.concat(qe.slice(1));const l=ve.pop();if(!l)break;T=l}}let E=k.getTimezoneOffset();I&&(E=On(I,E),k=function Mr(h,T,D){const I=D?-1:1,k=h.getTimezoneOffset();return function mr(h,T){return(h=new Date(h.getTime())).setMinutes(h.getMinutes()+T),h}(h,I*(On(T,k)-k))}(k,I,!0));let a="";return ve.forEach(l=>{const y=function br(h){if(ot[h])return ot[h];let T;switch(h){case"G":case"GG":case"GGG":T=ze(Ct.Eras,Ae.Abbreviated);break;case"GGGG":T=ze(Ct.Eras,Ae.Wide);break;case"GGGGG":T=ze(Ct.Eras,Ae.Narrow);break;case"y":T=Zt(ht.FullYear,1,0,!1,!0);break;case"yy":T=Zt(ht.FullYear,2,0,!0,!0);break;case"yyy":T=Zt(ht.FullYear,3,0,!1,!0);break;case"yyyy":T=Zt(ht.FullYear,4,0,!1,!0);break;case"Y":T=yi(1);break;case"YY":T=yi(2,!0);break;case"YYY":T=yi(3);break;case"YYYY":T=yi(4);break;case"M":case"L":T=Zt(ht.Month,1,1);break;case"MM":case"LL":T=Zt(ht.Month,2,1);break;case"MMM":T=ze(Ct.Months,Ae.Abbreviated);break;case"MMMM":T=ze(Ct.Months,Ae.Wide);break;case"MMMMM":T=ze(Ct.Months,Ae.Narrow);break;case"LLL":T=ze(Ct.Months,Ae.Abbreviated,Re.Standalone);break;case"LLLL":T=ze(Ct.Months,Ae.Wide,Re.Standalone);break;case"LLLLL":T=ze(Ct.Months,Ae.Narrow,Re.Standalone);break;case"w":T=Wr(1);break;case"ww":T=Wr(2);break;case"W":T=Wr(1,!0);break;case"d":T=Zt(ht.Date,1);break;case"dd":T=Zt(ht.Date,2);break;case"c":case"cc":T=Zt(ht.Day,1);break;case"ccc":T=ze(Ct.Days,Ae.Abbreviated,Re.Standalone);break;case"cccc":T=ze(Ct.Days,Ae.Wide,Re.Standalone);break;case"ccccc":T=ze(Ct.Days,Ae.Narrow,Re.Standalone);break;case"cccccc":T=ze(Ct.Days,Ae.Short,Re.Standalone);break;case"E":case"EE":case"EEE":T=ze(Ct.Days,Ae.Abbreviated);break;case"EEEE":T=ze(Ct.Days,Ae.Wide);break;case"EEEEE":T=ze(Ct.Days,Ae.Narrow);break;case"EEEEEE":T=ze(Ct.Days,Ae.Short);break;case"a":case"aa":case"aaa":T=ze(Ct.DayPeriods,Ae.Abbreviated);break;case"aaaa":T=ze(Ct.DayPeriods,Ae.Wide);break;case"aaaaa":T=ze(Ct.DayPeriods,Ae.Narrow);break;case"b":case"bb":case"bbb":T=ze(Ct.DayPeriods,Ae.Abbreviated,Re.Standalone,!0);break;case"bbbb":T=ze(Ct.DayPeriods,Ae.Wide,Re.Standalone,!0);break;case"bbbbb":T=ze(Ct.DayPeriods,Ae.Narrow,Re.Standalone,!0);break;case"B":case"BB":case"BBB":T=ze(Ct.DayPeriods,Ae.Abbreviated,Re.Format,!0);break;case"BBBB":T=ze(Ct.DayPeriods,Ae.Wide,Re.Format,!0);break;case"BBBBB":T=ze(Ct.DayPeriods,Ae.Narrow,Re.Format,!0);break;case"h":T=Zt(ht.Hours,1,-12);break;case"hh":T=Zt(ht.Hours,2,-12);break;case"H":T=Zt(ht.Hours,1);break;case"HH":T=Zt(ht.Hours,2);break;case"m":T=Zt(ht.Minutes,1);break;case"mm":T=Zt(ht.Minutes,2);break;case"s":T=Zt(ht.Seconds,1);break;case"ss":T=Zt(ht.Seconds,2);break;case"S":T=Zt(ht.FractionalSeconds,1);break;case"SS":T=Zt(ht.FractionalSeconds,2);break;case"SSS":T=Zt(ht.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":T=kr(Tn.Short);break;case"ZZZZZ":T=kr(Tn.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":T=kr(Tn.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":T=kr(Tn.Long);break;default:return null}return ot[h]=T,T}(l);a+=y?y(k,D,E):"''"===l?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),a}function Hn(h,T,D){const I=new Date(0);return I.setFullYear(h,T,D),I.setHours(0,0,0),I}function kn(h,T){const D=function _n(h){return(0,d.H5H)(h)[d.KH2.LocaleId]}(h);if(Yn[D]=Yn[D]||{},Yn[D][T])return Yn[D][T];let I="";switch(T){case"shortDate":I=Fr(h,Ee.Short);break;case"mediumDate":I=Fr(h,Ee.Medium);break;case"longDate":I=Fr(h,Ee.Long);break;case"fullDate":I=Fr(h,Ee.Full);break;case"shortTime":I=Q(h,Ee.Short);break;case"mediumTime":I=Q(h,Ee.Medium);break;case"longTime":I=Q(h,Ee.Long);break;case"fullTime":I=Q(h,Ee.Full);break;case"short":const k=kn(h,"shortTime"),re=kn(h,"shortDate");I=Nt(Y(h,Ee.Short),[k,re]);break;case"medium":const ve=kn(h,"mediumTime"),qe=kn(h,"mediumDate");I=Nt(Y(h,Ee.Medium),[ve,qe]);break;case"long":const E=kn(h,"longTime"),a=kn(h,"longDate");I=Nt(Y(h,Ee.Long),[E,a]);break;case"full":const l=kn(h,"fullTime"),y=kn(h,"fullDate");I=Nt(Y(h,Ee.Full),[l,y])}return I&&(Yn[D][T]=I),I}function Nt(h,T){return T&&(h=h.replace(/\{([^}]+)}/g,function(D,I){return null!=T&&I in T?T[I]:D})),h}function hn(h,T,D="-",I,k){let re="";(h<0||k&&h<=0)&&(k?h=1-h:(h=-h,re=D));let ve=String(h);for(;ve.length<T;)ve="0"+ve;return I&&(ve=ve.slice(ve.length-T)),re+ve}function Zt(h,T,D=0,I=!1,k=!1){return function(re,ve){let qe=function Xi(h,T){switch(h){case ht.FullYear:return T.getFullYear();case ht.Month:return T.getMonth();case ht.Date:return T.getDate();case ht.Hours:return T.getHours();case ht.Minutes:return T.getMinutes();case ht.Seconds:return T.getSeconds();case ht.FractionalSeconds:return T.getMilliseconds();case ht.Day:return T.getDay();default:throw new Error(`Unknown DateType value "${h}".`)}}(h,re);if((D>0||qe>-D)&&(qe+=D),h===ht.Hours)0===qe&&-12===D&&(qe=12);else if(h===ht.FractionalSeconds)return function ti(h,T){return hn(h,3).substring(0,T)}(qe,T);const E=J(ve,rt.MinusSign);return hn(qe,T,E,I,k)}}function ze(h,T,D=Re.Format,I=!1){return function(k,re){return function Gr(h,T,D,I,k,re){switch(D){case Ct.Months:return function en(h,T,D){const I=(0,d.H5H)(h),re=gt([I[d.KH2.MonthsFormat],I[d.KH2.MonthsStandalone]],T);return gt(re,D)}(T,k,I)[h.getMonth()];case Ct.Days:return function xe(h,T,D){const I=(0,d.H5H)(h),re=gt([I[d.KH2.DaysFormat],I[d.KH2.DaysStandalone]],T);return gt(re,D)}(T,k,I)[h.getDay()];case Ct.DayPeriods:const ve=h.getHours(),qe=h.getMinutes();if(re){const a=function kt(h){const T=(0,d.H5H)(h);return at(T),(T[d.KH2.ExtraData][2]||[]).map(I=>"string"==typeof I?fn(I):[fn(I[0]),fn(I[1])])}(T),l=function qt(h,T,D){const I=(0,d.H5H)(h);at(I);const re=gt([I[d.KH2.ExtraData][0],I[d.KH2.ExtraData][1]],T)||[];return gt(re,D)||[]}(T,k,I),y=a.findIndex(M=>{if(Array.isArray(M)){const[N,L]=M,U=ve>=N.hours&&qe>=N.minutes,q=ve<L.hours||ve===L.hours&&qe<L.minutes;if(N.hours<L.hours){if(U&&q)return!0}else if(U||q)return!0}else if(M.hours===ve&&M.minutes===qe)return!0;return!1});if(-1!==y)return l[y]}return function $n(h,T,D){const I=(0,d.H5H)(h),re=gt([I[d.KH2.DayPeriodsFormat],I[d.KH2.DayPeriodsStandalone]],T);return gt(re,D)}(T,k,I)[ve<12?0:1];case Ct.Eras:return function je(h,T){return gt((0,d.H5H)(h)[d.KH2.Eras],T)}(T,I)[h.getFullYear()<=0?0:1];default:throw new Error(`unexpected translation type ${D}`)}}(k,re,h,T,D,I)}}function kr(h){return function(T,D,I){const k=-1*I,re=J(D,rt.MinusSign),ve=k>0?Math.floor(k/60):Math.ceil(k/60);switch(h){case Tn.Short:return(k>=0?"+":"")+hn(ve,2,re)+hn(Math.abs(k%60),2,re);case Tn.ShortGMT:return"GMT"+(k>=0?"+":"")+hn(ve,1,re);case Tn.Long:return"GMT"+(k>=0?"+":"")+hn(ve,2,re)+":"+hn(Math.abs(k%60),2,re);case Tn.Extended:return 0===I?"Z":(k>=0?"+":"")+hn(ve,2,re)+":"+hn(Math.abs(k%60),2,re);default:throw new Error(`Unknown zone width "${h}"`)}}}const An=0,gr=4;function Ze(h){return Hn(h.getFullYear(),h.getMonth(),h.getDate()+(gr-h.getDay()))}function Wr(h,T=!1){return function(D,I){let k;if(T){const re=new Date(D.getFullYear(),D.getMonth(),1).getDay()-1,ve=D.getDate();k=1+Math.floor((ve+re)/7)}else{const re=Ze(D),ve=function wn(h){const T=Hn(h,An,1).getDay();return Hn(h,0,1+(T<=gr?gr:gr+7)-T)}(re.getFullYear()),qe=re.getTime()-ve.getTime();k=1+Math.round(qe/6048e5)}return hn(k,h,J(I,rt.MinusSign))}}function yi(h,T=!1){return function(D,I){return hn(Ze(D).getFullYear(),h,J(I,rt.MinusSign),T)}}const ot={};function On(h,T){h=h.replace(/:/g,"");const D=Date.parse("Jan 01, 1970 00:00:00 "+h)/6e4;return isNaN(D)?T:D}function V(h){return h instanceof Date&&!isNaN(h.valueOf())}function Kr(h,T){T=encodeURIComponent(T);for(const D of h.split(";")){const I=D.indexOf("="),[k,re]=-1==I?[D,""]:[D.slice(0,I),D.slice(I+1)];if(k.trim()===T)return decodeURIComponent(re)}return null}const yr=/\s+/,dt=[];let Rt=(()=>{class h{constructor(D,I,k,re){this._iterableDiffers=D,this._keyValueDiffers=I,this._ngEl=k,this._renderer=re,this.initialClasses=dt,this.stateMap=new Map}set klass(D){this.initialClasses=null!=D?D.trim().split(yr):dt}set ngClass(D){this.rawClass="string"==typeof D?D.trim().split(yr):D}ngDoCheck(){for(const I of this.initialClasses)this._updateState(I,!0);const D=this.rawClass;if(Array.isArray(D)||D instanceof Set)for(const I of D)this._updateState(I,!0);else if(null!=D)for(const I of Object.keys(D))this._updateState(I,!!D[I]);this._applyStateDiff()}_updateState(D,I){const k=this.stateMap.get(D);void 0!==k?(k.enabled!==I&&(k.changed=!0,k.enabled=I),k.touched=!0):this.stateMap.set(D,{enabled:I,changed:!0,touched:!0})}_applyStateDiff(){for(const D of this.stateMap){const I=D[0],k=D[1];k.changed?(this._toggleClass(I,k.enabled),k.changed=!1):k.touched||(k.enabled&&this._toggleClass(I,!1),this.stateMap.delete(I)),k.touched=!1}}_toggleClass(D,I){(D=D.trim()).length>0&&D.split(yr).forEach(k=>{I?this._renderer.addClass(this._ngEl.nativeElement,k):this._renderer.removeClass(this._ngEl.nativeElement,k)})}static{this.\u0275fac=function(I){return new(I||h)(d.rXU(d._q3),d.rXU(d.MKu),d.rXU(d.aKT),d.rXU(d.sFG))}}static{this.\u0275dir=d.FsC({type:h,selectors:[["","ngClass",""]],inputs:{klass:["class","klass"],ngClass:"ngClass"},standalone:!0})}}return h})();class pt{constructor(T,D,I,k){this.$implicit=T,this.ngForOf=D,this.index=I,this.count=k}get first(){return 0===this.index}get last(){return this.index===this.count-1}get even(){return this.index%2==0}get odd(){return!this.even}}let or=(()=>{class h{set ngForOf(D){this._ngForOf=D,this._ngForOfDirty=!0}set ngForTrackBy(D){this._trackByFn=D}get ngForTrackBy(){return this._trackByFn}constructor(D,I,k){this._viewContainer=D,this._template=I,this._differs=k,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(D){D&&(this._template=D)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;const D=this._ngForOf;!this._differ&&D&&(this._differ=this._differs.find(D).create(this.ngForTrackBy))}if(this._differ){const D=this._differ.diff(this._ngForOf);D&&this._applyChanges(D)}}_applyChanges(D){const I=this._viewContainer;D.forEachOperation((k,re,ve)=>{if(null==k.previousIndex)I.createEmbeddedView(this._template,new pt(k.item,this._ngForOf,-1,-1),null===ve?void 0:ve);else if(null==ve)I.remove(null===re?void 0:re);else if(null!==re){const qe=I.get(re);I.move(qe,ve),Ir(qe,k)}});for(let k=0,re=I.length;k<re;k++){const qe=I.get(k).context;qe.index=k,qe.count=re,qe.ngForOf=this._ngForOf}D.forEachIdentityChange(k=>{Ir(I.get(k.currentIndex),k)})}static ngTemplateContextGuard(D,I){return!0}static{this.\u0275fac=function(I){return new(I||h)(d.rXU(d.c1b),d.rXU(d.C4Q),d.rXU(d._q3))}}static{this.\u0275dir=d.FsC({type:h,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return h})();function Ir(h,T){h.context.$implicit=T.item}let Tr=(()=>{class h{constructor(D,I){this._viewContainer=D,this._context=new vr,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=I}set ngIf(D){this._context.$implicit=this._context.ngIf=D,this._updateView()}set ngIfThen(D){ri("ngIfThen",D),this._thenTemplateRef=D,this._thenViewRef=null,this._updateView()}set ngIfElse(D){ri("ngIfElse",D),this._elseTemplateRef=D,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(D,I){return!0}static{this.\u0275fac=function(I){return new(I||h)(d.rXU(d.c1b),d.rXU(d.C4Q))}}static{this.\u0275dir=d.FsC({type:h,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return h})();class vr{constructor(){this.$implicit=null,this.ngIf=null}}function ri(h,T){if(T&&!T.createEmbeddedView)throw new Error(`${h} must be a TemplateRef, but received '${(0,d.Tbb)(T)}'.`)}function Wn(h,T){return new d.wOt(2100,!1)}const oi=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g;let qi=(()=>{class h{transform(D){if(null==D)return null;if("string"!=typeof D)throw Wn();return D.replace(oi,I=>I[0].toUpperCase()+I.slice(1).toLowerCase())}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275pipe=d.EJ8({name:"titlecase",type:h,pure:!0,standalone:!0})}}return h})();const bo=new d.nKC("DATE_PIPE_DEFAULT_TIMEZONE"),Bo=new d.nKC("DATE_PIPE_DEFAULT_OPTIONS");let si=(()=>{class h{constructor(D,I,k){this.locale=D,this.defaultTimezone=I,this.defaultOptions=k}transform(D,I,k,re){if(null==D||""===D||D!=D)return null;try{return En(D,I??this.defaultOptions?.dateFormat??"mediumDate",re||this.locale,k??this.defaultOptions?.timezone??this.defaultTimezone??void 0)}catch(ve){throw Wn()}}static{this.\u0275fac=function(I){return new(I||h)(d.rXU(d.xe9,16),d.rXU(bo,24),d.rXU(Bo,24))}}static{this.\u0275pipe=d.EJ8({name:"date",type:h,pure:!0,standalone:!0})}}return h})(),bn=(()=>{class h{transform(D,I,k){if(null==D)return null;if(!this.supports(D))throw Wn();return D.slice(I,k)}supports(D){return"string"==typeof D||Array.isArray(D)}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275pipe=d.EJ8({name:"slice",type:h,pure:!1,standalone:!0})}}return h})(),Nr=(()=>{class h{static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275mod=d.$C({type:h})}static{this.\u0275inj=d.G2t({})}}return h})();const Qr="browser",Mo="server";function mt(h){return h===Mo}let Ln=(()=>{class h{static{this.\u0275prov=(0,d.jDH)({token:h,providedIn:"root",factory:()=>new tr((0,d.KVO)(oe),window)})}}return h})();class tr{constructor(T,D){this.document=T,this.window=D,this.offset=()=>[0,0]}setOffset(T){this.offset=Array.isArray(T)?()=>T:T}getScrollPosition(){return this.supportsScrolling()?[this.window.pageXOffset,this.window.pageYOffset]:[0,0]}scrollToPosition(T){this.supportsScrolling()&&this.window.scrollTo(T[0],T[1])}scrollToAnchor(T){if(!this.supportsScrolling())return;const D=function un(h,T){const D=h.getElementById(T)||h.getElementsByName(T)[0];if(D)return D;if("function"==typeof h.createTreeWalker&&h.body&&"function"==typeof h.body.attachShadow){const I=h.createTreeWalker(h.body,NodeFilter.SHOW_ELEMENT);let k=I.currentNode;for(;k;){const re=k.shadowRoot;if(re){const ve=re.getElementById(T)||re.querySelector(`[name="${T}"]`);if(ve)return ve}k=I.nextNode()}}return null}(this.document,T);D&&(this.scrollToElement(D),D.focus())}setHistoryScrollRestoration(T){this.supportsScrolling()&&(this.window.history.scrollRestoration=T)}scrollToElement(T){const D=T.getBoundingClientRect(),I=D.left+this.window.pageXOffset,k=D.top+this.window.pageYOffset,re=this.offset();this.window.scrollTo(I-re[0],k-re[1])}supportsScrolling(){try{return!!this.window&&!!this.window.scrollTo&&"pageXOffset"in this.window}catch{return!1}}}class ur{}},1626:(tt,ye,R)=>{R.d(ye,{Lr:()=>be,Nl:()=>De,Qq:()=>Fn,a7:()=>at,q1:()=>z});var d=R(540),w=R(7673),de=R(6648),ae=R(1985),X=R(274),oe=R(5964),ie=R(6354),ge=R(980),le=R(5558),pe=R(177);class Z{}class ne{}class be{constructor(j){this.normalizedNames=new Map,this.lazyUpdate=null,j?"string"==typeof j?this.lazyInit=()=>{this.headers=new Map,j.split("\n").forEach($=>{const fe=$.indexOf(":");if(fe>0){const Se=$.slice(0,fe),Be=Se.toLowerCase(),Oe=$.slice(fe+1).trim();this.maybeSetNormalizedName(Se,Be),this.headers.has(Be)?this.headers.get(Be).push(Oe):this.headers.set(Be,[Oe])}})}:typeof Headers<"u"&&j instanceof Headers?(this.headers=new Map,j.forEach(($,fe)=>{this.setHeaderEntries(fe,$)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(j).forEach(([$,fe])=>{this.setHeaderEntries($,fe)})}:this.headers=new Map}has(j){return this.init(),this.headers.has(j.toLowerCase())}get(j){this.init();const $=this.headers.get(j.toLowerCase());return $&&$.length>0?$[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(j){return this.init(),this.headers.get(j.toLowerCase())||null}append(j,$){return this.clone({name:j,value:$,op:"a"})}set(j,$){return this.clone({name:j,value:$,op:"s"})}delete(j,$){return this.clone({name:j,value:$,op:"d"})}maybeSetNormalizedName(j,$){this.normalizedNames.has($)||this.normalizedNames.set($,j)}init(){this.lazyInit&&(this.lazyInit instanceof be?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(j=>this.applyUpdate(j)),this.lazyUpdate=null))}copyFrom(j){j.init(),Array.from(j.headers.keys()).forEach($=>{this.headers.set($,j.headers.get($)),this.normalizedNames.set($,j.normalizedNames.get($))})}clone(j){const $=new be;return $.lazyInit=this.lazyInit&&this.lazyInit instanceof be?this.lazyInit:this,$.lazyUpdate=(this.lazyUpdate||[]).concat([j]),$}applyUpdate(j){const $=j.name.toLowerCase();switch(j.op){case"a":case"s":let fe=j.value;if("string"==typeof fe&&(fe=[fe]),0===fe.length)return;this.maybeSetNormalizedName(j.name,$);const Se=("a"===j.op?this.headers.get($):void 0)||[];Se.push(...fe),this.headers.set($,Se);break;case"d":const Be=j.value;if(Be){let Oe=this.headers.get($);if(!Oe)return;Oe=Oe.filter(Wt=>-1===Be.indexOf(Wt)),0===Oe.length?(this.headers.delete($),this.normalizedNames.delete($)):this.headers.set($,Oe)}else this.headers.delete($),this.normalizedNames.delete($)}}setHeaderEntries(j,$){const fe=(Array.isArray($)?$:[$]).map(Be=>Be.toString()),Se=j.toLowerCase();this.headers.set(Se,fe),this.maybeSetNormalizedName(j,Se)}forEach(j){this.init(),Array.from(this.normalizedNames.keys()).forEach($=>j(this.normalizedNames.get($),this.headers.get($)))}}class Ge{encodeKey(j){return jt(j)}encodeValue(j){return jt(j)}decodeKey(j){return decodeURIComponent(j)}decodeValue(j){return decodeURIComponent(j)}}const Tt=/%(\d[a-f0-9])/gi,Vt={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function jt(G){return encodeURIComponent(G).replace(Tt,(j,$)=>Vt[$]??j)}function Qt(G){return`${G}`}class De{constructor(j={}){if(this.updates=null,this.cloneFrom=null,this.encoder=j.encoder||new Ge,j.fromString){if(j.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=function Lt(G,j){const $=new Map;return G.length>0&&G.replace(/^\?/,"").split("&").forEach(Se=>{const Be=Se.indexOf("="),[Oe,Wt]=-1==Be?[j.decodeKey(Se),""]:[j.decodeKey(Se.slice(0,Be)),j.decodeValue(Se.slice(Be+1))],ke=$.get(Oe)||[];ke.push(Wt),$.set(Oe,ke)}),$}(j.fromString,this.encoder)}else j.fromObject?(this.map=new Map,Object.keys(j.fromObject).forEach($=>{const fe=j.fromObject[$],Se=Array.isArray(fe)?fe.map(Qt):[Qt(fe)];this.map.set($,Se)})):this.map=null}has(j){return this.init(),this.map.has(j)}get(j){this.init();const $=this.map.get(j);return $?$[0]:null}getAll(j){return this.init(),this.map.get(j)||null}keys(){return this.init(),Array.from(this.map.keys())}append(j,$){return this.clone({param:j,value:$,op:"a"})}appendAll(j){const $=[];return Object.keys(j).forEach(fe=>{const Se=j[fe];Array.isArray(Se)?Se.forEach(Be=>{$.push({param:fe,value:Be,op:"a"})}):$.push({param:fe,value:Se,op:"a"})}),this.clone($)}set(j,$){return this.clone({param:j,value:$,op:"s"})}delete(j,$){return this.clone({param:j,value:$,op:"d"})}toString(){return this.init(),this.keys().map(j=>{const $=this.encoder.encodeKey(j);return this.map.get(j).map(fe=>$+"="+this.encoder.encodeValue(fe)).join("&")}).filter(j=>""!==j).join("&")}clone(j){const $=new De({encoder:this.encoder});return $.cloneFrom=this.cloneFrom||this,$.updates=(this.updates||[]).concat(j),$}init(){null===this.map&&(this.map=new Map),null!==this.cloneFrom&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(j=>this.map.set(j,this.cloneFrom.map.get(j))),this.updates.forEach(j=>{switch(j.op){case"a":case"s":const $=("a"===j.op?this.map.get(j.param):void 0)||[];$.push(Qt(j.value)),this.map.set(j.param,$);break;case"d":if(void 0===j.value){this.map.delete(j.param);break}{let fe=this.map.get(j.param)||[];const Se=fe.indexOf(Qt(j.value));-1!==Se&&fe.splice(Se,1),fe.length>0?this.map.set(j.param,fe):this.map.delete(j.param)}}}),this.cloneFrom=this.updates=null)}}class Ve{constructor(){this.map=new Map}set(j,$){return this.map.set(j,$),this}get(j){return this.map.has(j)||this.map.set(j,j.defaultValue()),this.map.get(j)}delete(j){return this.map.delete(j),this}has(j){return this.map.has(j)}keys(){return this.map.keys()}}function Re(G){return typeof ArrayBuffer<"u"&&G instanceof ArrayBuffer}function Ae(G){return typeof Blob<"u"&&G instanceof Blob}function Ee(G){return typeof FormData<"u"&&G instanceof FormData}class Ft{constructor(j,$,fe,Se){let Be;if(this.url=$,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=j.toUpperCase(),function nt(G){switch(G){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}(this.method)||Se?(this.body=void 0!==fe?fe:null,Be=Se):Be=fe,Be&&(this.reportProgress=!!Be.reportProgress,this.withCredentials=!!Be.withCredentials,Be.responseType&&(this.responseType=Be.responseType),Be.headers&&(this.headers=Be.headers),Be.context&&(this.context=Be.context),Be.params&&(this.params=Be.params)),this.headers||(this.headers=new be),this.context||(this.context=new Ve),this.params){const Oe=this.params.toString();if(0===Oe.length)this.urlWithParams=$;else{const Wt=$.indexOf("?");this.urlWithParams=$+(-1===Wt?"?":Wt<$.length-1?"&":"")+Oe}}else this.params=new De,this.urlWithParams=$}serializeBody(){return null===this.body?null:Re(this.body)||Ae(this.body)||Ee(this.body)||function rt(G){return typeof URLSearchParams<"u"&&G instanceof URLSearchParams}(this.body)||"string"==typeof this.body?this.body:this.body instanceof De?this.body.toString():"object"==typeof this.body||"boolean"==typeof this.body||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return null===this.body||Ee(this.body)?null:Ae(this.body)?this.body.type||null:Re(this.body)?null:"string"==typeof this.body?"text/plain":this.body instanceof De?"application/x-www-form-urlencoded;charset=UTF-8":"object"==typeof this.body||"number"==typeof this.body||"boolean"==typeof this.body?"application/json":null}clone(j={}){const $=j.method||this.method,fe=j.url||this.url,Se=j.responseType||this.responseType,Be=void 0!==j.body?j.body:this.body,Oe=void 0!==j.withCredentials?j.withCredentials:this.withCredentials,Wt=void 0!==j.reportProgress?j.reportProgress:this.reportProgress;let ke=j.headers||this.headers,rn=j.params||this.params;const ir=j.context??this.context;return void 0!==j.setHeaders&&(ke=Object.keys(j.setHeaders).reduce((Jt,er)=>Jt.set(er,j.setHeaders[er]),ke)),j.setParams&&(rn=Object.keys(j.setParams).reduce((Jt,er)=>Jt.set(er,j.setParams[er]),rn)),new Ft($,fe,Be,{params:rn,headers:ke,context:ir,reportProgress:Wt,responseType:Se,withCredentials:Oe})}}var _n=function(G){return G[G.Sent=0]="Sent",G[G.UploadProgress=1]="UploadProgress",G[G.ResponseHeader=2]="ResponseHeader",G[G.DownloadProgress=3]="DownloadProgress",G[G.Response=4]="Response",G[G.User=5]="User",G}(_n||{});class $n{constructor(j,$=200,fe="OK"){this.headers=j.headers||new be,this.status=void 0!==j.status?j.status:$,this.statusText=j.statusText||fe,this.url=j.url||null,this.ok=this.status>=200&&this.status<300}}class xe extends $n{constructor(j={}){super(j),this.type=_n.ResponseHeader}clone(j={}){return new xe({headers:j.headers||this.headers,status:void 0!==j.status?j.status:this.status,statusText:j.statusText||this.statusText,url:j.url||this.url||void 0})}}class en extends $n{constructor(j={}){super(j),this.type=_n.Response,this.body=void 0!==j.body?j.body:null}clone(j={}){return new en({body:void 0!==j.body?j.body:this.body,headers:j.headers||this.headers,status:void 0!==j.status?j.status:this.status,statusText:j.statusText||this.statusText,url:j.url||this.url||void 0})}}class je extends $n{constructor(j){super(j,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.message=this.status>=200&&this.status<300?`Http failure during parsing for ${j.url||"(unknown url)"}`:`Http failure response for ${j.url||"(unknown url)"}: ${j.status} ${j.statusText}`,this.error=j.error||null}}function ft(G,j){return{body:j,headers:G.headers,context:G.context,observe:G.observe,params:G.params,reportProgress:G.reportProgress,responseType:G.responseType,withCredentials:G.withCredentials}}let Fn=(()=>{class G{constructor($){this.handler=$}request($,fe,Se={}){let Be;if($ instanceof Ft)Be=$;else{let ke,rn;ke=Se.headers instanceof be?Se.headers:new be(Se.headers),Se.params&&(rn=Se.params instanceof De?Se.params:new De({fromObject:Se.params})),Be=new Ft($,fe,void 0!==Se.body?Se.body:null,{headers:ke,context:Se.context,params:rn,reportProgress:Se.reportProgress,responseType:Se.responseType||"json",withCredentials:Se.withCredentials})}const Oe=(0,w.of)(Be).pipe((0,X.H)(ke=>this.handler.handle(ke)));if($ instanceof Ft||"events"===Se.observe)return Oe;const Wt=Oe.pipe((0,oe.p)(ke=>ke instanceof en));switch(Se.observe||"body"){case"body":switch(Be.responseType){case"arraybuffer":return Wt.pipe((0,ie.T)(ke=>{if(null!==ke.body&&!(ke.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return ke.body}));case"blob":return Wt.pipe((0,ie.T)(ke=>{if(null!==ke.body&&!(ke.body instanceof Blob))throw new Error("Response is not a Blob.");return ke.body}));case"text":return Wt.pipe((0,ie.T)(ke=>{if(null!==ke.body&&"string"!=typeof ke.body)throw new Error("Response is not a string.");return ke.body}));default:return Wt.pipe((0,ie.T)(ke=>ke.body))}case"response":return Wt;default:throw new Error(`Unreachable: unhandled observe type ${Se.observe}}`)}}delete($,fe={}){return this.request("DELETE",$,fe)}get($,fe={}){return this.request("GET",$,fe)}head($,fe={}){return this.request("HEAD",$,fe)}jsonp($,fe){return this.request("JSONP",$,{params:(new De).append(fe,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options($,fe={}){return this.request("OPTIONS",$,fe)}patch($,fe,Se={}){return this.request("PATCH",$,ft(Se,fe))}post($,fe,Se={}){return this.request("POST",$,ft(Se,fe))}put($,fe,Se={}){return this.request("PUT",$,ft(Se,fe))}static{this.\u0275fac=function(fe){return new(fe||G)(d.KVO(Z))}}static{this.\u0275prov=d.jDH({token:G,factory:G.\u0275fac})}}return G})();function We(G,j){return j(G)}function ct(G,j){return($,fe)=>j.intercept($,{handle:Se=>G(Se,fe)})}const at=new d.nKC(""),kt=new d.nKC(""),qt=new d.nKC("");function Jn(){let G=null;return(j,$)=>{null===G&&(G=((0,d.WQX)(at,{optional:!0})??[]).reduceRight(ct,We));const fe=(0,d.WQX)(d.$K3),Se=fe.add();return G(j,$).pipe((0,ge.j)(()=>fe.remove(Se)))}}let gt=(()=>{class G extends Z{constructor($,fe){super(),this.backend=$,this.injector=fe,this.chain=null,this.pendingTasks=(0,d.WQX)(d.$K3)}handle($){if(null===this.chain){const Se=Array.from(new Set([...this.injector.get(kt),...this.injector.get(qt,[])]));this.chain=Se.reduceRight((Be,Oe)=>function zt(G,j,$){return(fe,Se)=>$.runInContext(()=>j(fe,Be=>G(Be,Se)))}(Be,Oe,this.injector),We)}const fe=this.pendingTasks.add();return this.chain($,Se=>this.backend.handle(Se)).pipe((0,ge.j)(()=>this.pendingTasks.remove(fe)))}static{this.\u0275fac=function(fe){return new(fe||G)(d.KVO(ne),d.KVO(d.uvJ))}}static{this.\u0275prov=d.jDH({token:G,factory:G.\u0275fac})}}return G})();const Hn=/^\)\]\}',?\n/;let Nt=(()=>{class G{constructor($){this.xhrFactory=$}handle($){if("JSONP"===$.method)throw new d.wOt(-2800,!1);const fe=this.xhrFactory;return(fe.\u0275loadImpl?(0,de.H)(fe.\u0275loadImpl()):(0,w.of)(null)).pipe((0,le.n)(()=>new ae.c(Be=>{const Oe=fe.build();if(Oe.open($.method,$.urlWithParams),$.withCredentials&&(Oe.withCredentials=!0),$.headers.forEach((dt,Rt)=>Oe.setRequestHeader(dt,Rt.join(","))),$.headers.has("Accept")||Oe.setRequestHeader("Accept","application/json, text/plain, */*"),!$.headers.has("Content-Type")){const dt=$.detectContentTypeHeader();null!==dt&&Oe.setRequestHeader("Content-Type",dt)}if($.responseType){const dt=$.responseType.toLowerCase();Oe.responseType="json"!==dt?dt:"text"}const Wt=$.serializeBody();let ke=null;const rn=()=>{if(null!==ke)return ke;const dt=Oe.statusText||"OK",Rt=new be(Oe.getAllResponseHeaders()),vn=function kn(G){return"responseURL"in G&&G.responseURL?G.responseURL:/^X-Request-URL:/m.test(G.getAllResponseHeaders())?G.getResponseHeader("X-Request-URL"):null}(Oe)||$.url;return ke=new xe({headers:Rt,status:Oe.status,statusText:dt,url:vn}),ke},ir=()=>{let{headers:dt,status:Rt,statusText:vn,url:Gn}=rn(),pt=null;204!==Rt&&(pt=typeof Oe.response>"u"?Oe.responseText:Oe.response),0===Rt&&(Rt=pt?200:0);let or=Rt>=200&&Rt<300;if("json"===$.responseType&&"string"==typeof pt){const Ir=pt;pt=pt.replace(Hn,"");try{pt=""!==pt?JSON.parse(pt):null}catch(ni){pt=Ir,or&&(or=!1,pt={error:ni,text:pt})}}or?(Be.next(new en({body:pt,headers:dt,status:Rt,statusText:vn,url:Gn||void 0})),Be.complete()):Be.error(new je({error:pt,headers:dt,status:Rt,statusText:vn,url:Gn||void 0}))},Jt=dt=>{const{url:Rt}=rn(),vn=new je({error:dt,status:Oe.status||0,statusText:Oe.statusText||"Unknown Error",url:Rt||void 0});Be.error(vn)};let er=!1;const Kr=dt=>{er||(Be.next(rn()),er=!0);let Rt={type:_n.DownloadProgress,loaded:dt.loaded};dt.lengthComputable&&(Rt.total=dt.total),"text"===$.responseType&&Oe.responseText&&(Rt.partialText=Oe.responseText),Be.next(Rt)},yr=dt=>{let Rt={type:_n.UploadProgress,loaded:dt.loaded};dt.lengthComputable&&(Rt.total=dt.total),Be.next(Rt)};return Oe.addEventListener("load",ir),Oe.addEventListener("error",Jt),Oe.addEventListener("timeout",Jt),Oe.addEventListener("abort",Jt),$.reportProgress&&(Oe.addEventListener("progress",Kr),null!==Wt&&Oe.upload&&Oe.upload.addEventListener("progress",yr)),Oe.send(Wt),Be.next({type:_n.Sent}),()=>{Oe.removeEventListener("error",Jt),Oe.removeEventListener("abort",Jt),Oe.removeEventListener("load",ir),Oe.removeEventListener("timeout",Jt),$.reportProgress&&(Oe.removeEventListener("progress",Kr),null!==Wt&&Oe.upload&&Oe.upload.removeEventListener("progress",yr)),Oe.readyState!==Oe.DONE&&Oe.abort()}})))}static{this.\u0275fac=function(fe){return new(fe||G)(d.KVO(pe.N0))}}static{this.\u0275prov=d.jDH({token:G,factory:G.\u0275fac})}}return G})();const hn=new d.nKC("XSRF_ENABLED"),Zt=new d.nKC("XSRF_COOKIE_NAME",{providedIn:"root",factory:()=>"XSRF-TOKEN"}),ze=new d.nKC("XSRF_HEADER_NAME",{providedIn:"root",factory:()=>"X-XSRF-TOKEN"});class Gr{}let kr=(()=>{class G{constructor($,fe,Se){this.doc=$,this.platform=fe,this.cookieName=Se,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if("server"===this.platform)return null;const $=this.doc.cookie||"";return $!==this.lastCookieString&&(this.parseCount++,this.lastToken=(0,pe._b)($,this.cookieName),this.lastCookieString=$),this.lastToken}static{this.\u0275fac=function(fe){return new(fe||G)(d.KVO(pe.qQ),d.KVO(d.Agw),d.KVO(Zt))}}static{this.\u0275prov=d.jDH({token:G,factory:G.\u0275fac})}}return G})();function An(G,j){const $=G.url.toLowerCase();if(!(0,d.WQX)(hn)||"GET"===G.method||"HEAD"===G.method||$.startsWith("http://")||$.startsWith("https://"))return j(G);const fe=(0,d.WQX)(Gr).getToken(),Se=(0,d.WQX)(ze);return null!=fe&&!G.headers.has(Se)&&(G=G.clone({headers:G.headers.set(Se,fe)})),j(G)}var wn=function(G){return G[G.Interceptors=0]="Interceptors",G[G.LegacyInterceptors=1]="LegacyInterceptors",G[G.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",G[G.NoXsrfProtection=3]="NoXsrfProtection",G[G.JsonpSupport=4]="JsonpSupport",G[G.RequestsMadeViaParent=5]="RequestsMadeViaParent",G[G.Fetch=6]="Fetch",G}(wn||{});function Ze(G,j){return{\u0275kind:G,\u0275providers:j}}function Wr(...G){const j=[Fn,Nt,gt,{provide:Z,useExisting:gt},{provide:ne,useExisting:Nt},{provide:kt,useValue:An,multi:!0},{provide:hn,useValue:!0},{provide:Gr,useClass:kr}];for(const $ of G)j.push(...$.\u0275providers);return(0,d.EmA)(j)}const ot=new d.nKC("LEGACY_INTERCEPTOR_FN");let z=(()=>{class G{static{this.\u0275fac=function(fe){return new(fe||G)}}static{this.\u0275mod=d.$C({type:G})}static{this.\u0275inj=d.G2t({providers:[Wr(Ze(wn.LegacyInterceptors,[{provide:ot,useFactory:Jn},{provide:kt,useExisting:ot,multi:!0}]))]})}}return G})()},540:(tt,ye,R)=>{R.d(ye,{bc$:()=>pC,iLQ:()=>$d,sZ2:()=>wh,hnV:()=>Sy,Hbi:()=>AM,o8S:()=>ua,BIS:()=>gC,gRc:()=>pM,Ql9:()=>x0,Ocv:()=>N0,Z63:()=>Pa,aKT:()=>Fa,uvJ:()=>vo,zcH:()=>Jo,bkB:()=>Do,$GK:()=>ze,nKC:()=>ot,zZn:()=>io,_q3:()=>Xd,MKu:()=>Qd,xe9:()=>yu,Co$:()=>Em,Vns:()=>Ds,NEm:()=>sM,SKi:()=>wr,Xx1:()=>ml,Agw:()=>vc,PLl:()=>bh,sFG:()=>OC,_9s:()=>Oh,czy:()=>bl,kdw:()=>yl,C4Q:()=>Ya,NYb:()=>nM,giA:()=>xy,RxE:()=>Nh,c1b:()=>gu,gXe:()=>vn,L39:()=>UM,Ol2:()=>bm,w6W:()=>_b,oH4:()=>Vy,Rfq:()=>Ae,WQX:()=>ke,QuC:()=>Or,EmA:()=>dc,fpN:()=>TM,HJs:()=>$M,O8t:()=>q,H3F:()=>Iy,H8p:()=>pc,$K3:()=>Ty,KH2:()=>oa,wOt:()=>xe,WHO:()=>Py,e01:()=>Ry,H5H:()=>gd,mq5:()=>Kg,JZv:()=>Ze,LfX:()=>En,plB:()=>Pd,jNT:()=>id,zjR:()=>qp,TL$:()=>FD,Tbb:()=>Ve,Vt3:()=>Kc,GFd:()=>Op,OA$:()=>st,Jv_:()=>_m,aNF:()=>Mm,R7$:()=>tp,BMQ:()=>Qc,HbH:()=>vg,ZvI:()=>xg,STu:()=>Fg,AVh:()=>dd,wni:()=>oy,VBU:()=>si,FsC:()=>Ci,jDH:()=>Ut,G2t:()=>ht,$C:()=>ai,EJ8:()=>Yi,rXU:()=>Ws,nrm:()=>rd,bVm:()=>ou,qex:()=>iu,k0s:()=>ru,j41:()=>nu,RV6:()=>Qp,xGo:()=>yf,KVO:()=>Oe,kS0:()=>Lu,QTQ:()=>rp,bIt:()=>od,lsd:()=>sy,XpG:()=>tg,nI1:()=>$m,bMT:()=>Hm,i5U:()=>zm,brH:()=>Gm,Y8G:()=>td,lJ4:()=>Pm,eq3:()=>Rm,l_i:()=>xm,sMw:()=>Fm,ziG:()=>km,mGM:()=>ry,Njj:()=>rl,eBV:()=>nl,B4B:()=>uc,n$t:()=>gh,xc7:()=>cd,DNE:()=>Hp,EFF:()=>Mg,JRh:()=>hd,SpI:()=>lu,Lme:()=>pd,GBs:()=>iy});var d=R(1413),w=R(8359),de=R(1985),ae=R(6365),X=R(8750),oe=R(983),ie=R(9326),ge=R(6648),pe=R(4412),Z=R(7673),ne=R(7707),be=R(9974);function Ne(e={}){const{connector:t=(()=>new d.B),resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:i=!0}=e;return o=>{let s,c,g,_=0,A=!1,x=!1;const H=()=>{c?.unsubscribe(),c=void 0},W=()=>{H(),s=g=void 0,A=x=!1},he=()=>{const Me=s;W(),Me?.unsubscribe()};return(0,be.N)((Me,He)=>{_++,!x&&!A&&H();const et=g=g??t();He.add(()=>{_--,0===_&&!x&&!A&&(c=Ge(he,i))}),et.subscribe(He),!s&&_>0&&(s=new ne.Ms({next:Ce=>et.next(Ce),error:Ce=>{x=!0,H(),c=Ge(W,n,Ce),et.error(Ce)},complete:()=>{A=!0,H(),c=Ge(W,r),et.complete()}}),(0,X.Tg)(Me).subscribe(s))})(o)}}function Ge(e,t,...n){if(!0===t)return void e();if(!1===t)return;const r=new ne.Ms({next:()=>{r.unsubscribe(),e()}});return(0,X.Tg)(t(...n)).subscribe(r)}var Lt=R(5558),Tt=R(3669),Vt=R(4360);function Qt(e,t){return e===t}function De(e){for(let t in e)if(e[t]===De)return t;throw Error("Could not find renamed property on target object.")}function Te(e,t){for(const n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Ve(e){if("string"==typeof e)return e;if(Array.isArray(e))return"["+e.map(Ve).join(", ")+"]";if(null==e)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;const t=e.toString();if(null==t)return""+t;const n=t.indexOf("\n");return-1===n?t:t.substring(0,n)}function nt(e,t){return null==e||""===e?null===t?"":t:null==t||""===t?e:e+" "+t}const Re=De({__forward_ref__:De});function Ae(e){return e.__forward_ref__=Ae,e.toString=function(){return Ve(this())},e}function Ee(e){return rt(e)?e():e}function rt(e){return"function"==typeof e&&e.hasOwnProperty(Re)&&e.__forward_ref__===Ae}function Ft(e){return e&&!!e.\u0275providers}const $n="https://g.co/ng/security#xss";class xe extends Error{constructor(t,n){super(function en(e,t){return`NG0${Math.abs(e)}${t?": "+t:""}`}(t,n)),this.code=t}}function je(e){return"string"==typeof e?e:null==e?"":String(e)}function Y(e,t){throw new xe(-201,!1)}function Gt(e,t){null==e&&function Qe(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(null==r?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}(t,e,null,"!=")}function Ut(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function ht(e){return{providers:e.providers||[],imports:e.imports||[]}}function Ct(e){return Hn(e,hn)||Hn(e,Zt)}function En(e){return null!==Ct(e)}function Hn(e,t){return e.hasOwnProperty(t)?e[t]:null}function Nt(e){return e&&(e.hasOwnProperty(ti)||e.hasOwnProperty(Xi))?e[ti]:null}const hn=De({\u0275prov:De}),ti=De({\u0275inj:De}),Zt=De({ngInjectableDef:De}),Xi=De({ngInjectorDef:De});var ze=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(ze||{});let Gr;function An(e){const t=Gr;return Gr=e,t}function gr(e,t,n){const r=Ct(e);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:n&ze.Optional?null:void 0!==t?t:void Y(Ve(e))}const Ze=globalThis;class ot{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof n?this.__NG_ELEMENT_ID__=n:void 0!==n&&(this.\u0275prov=Ut({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}}const bt={},Pt="__NG_DI_FLAG__",Yt="ngTempTokenPath",Sr=/\n/gm,j="__source";let $;function Se(e){const t=$;return $=e,t}function Be(e,t=ze.Default){if(void 0===$)throw new xe(-203,!1);return null===$?gr(e,void 0,t):$.get(e,t&ze.Optional?null:void 0,t)}function Oe(e,t=ze.Default){return(function kr(){return Gr}()||Be)(Ee(e),t)}function ke(e,t=ze.Default){return Oe(e,rn(t))}function rn(e){return typeof e>"u"||"number"==typeof e?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function ir(e){const t=[];for(let n=0;n<e.length;n++){const r=Ee(e[n]);if(Array.isArray(r)){if(0===r.length)throw new xe(900,!1);let i,o=ze.Default;for(let s=0;s<r.length;s++){const c=r[s],g=er(c);"number"==typeof g?-1===g?i=c.token:o|=g:i=c}t.push(Oe(i,o))}else t.push(Oe(r))}return t}function Jt(e,t){return e[Pt]=t,e.prototype[Pt]=t,e}function er(e){return e[Pt]}function dt(e){return{toString:e}.toString()}var Rt=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Rt||{}),vn=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(vn||{});const Gn={},pt=[],or=De({\u0275cmp:De}),Ir=De({\u0275dir:De}),ni=De({\u0275pipe:De}),Tr=De({\u0275mod:De}),vr=De({\u0275fac:De}),ri=De({__NG_ELEMENT_ID__:De}),Lr=De({__NG_ENV_ID__:De});function sr(e,t,n){let r=e.length;for(;;){const i=e.indexOf(t,n);if(-1===i)return i;if(0===i||e.charCodeAt(i-1)<=32){const o=t.length;if(i+o===r||e.charCodeAt(i+o)<=32)return i}n=i+1}}function Dr(e,t,n){let r=0;for(;r<n.length;){const i=n[r];if("number"==typeof i){if(0!==i)break;r++;const o=n[r++],s=n[r++],c=n[r++];e.setAttribute(t,s,c,o)}else{const o=i,s=n[++r];xi(o)?e.setProperty(t,o,s):e.setAttribute(t,o,s),r++}}return r}function Ri(e){return 3===e||4===e||6===e}function xi(e){return 64===e.charCodeAt(0)}function ar(e,t){if(null!==t&&0!==t.length)if(null===e||0===e.length)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){const i=t[r];"number"==typeof i?n=i:0===n||ii(e,n,i,null,-1===n||2===n?t[++r]:null)}}return e}function ii(e,t,n,r,i){let o=0,s=e.length;if(-1===t)s=-1;else for(;o<e.length;){const c=e[o++];if("number"==typeof c){if(c===t){s=-1;break}if(c>t){s=o-1;break}}}for(;o<e.length;){const c=e[o];if("number"==typeof c)break;if(c===n){if(null===r)return void(null!==i&&(e[o+1]=i));if(r===e[o+1])return void(e[o+2]=i)}o++,null!==r&&o++,null!==i&&o++}-1!==s&&(e.splice(s,0,t),o=s+1),e.splice(o++,0,n),null!==r&&e.splice(o++,0,r),null!==i&&e.splice(o++,0,i)}const lr="ng-template";function Fi(e,t,n){let r=0,i=!0;for(;r<e.length;){let o=e[r++];if("string"==typeof o&&i){const s=e[r++];if(n&&"class"===o&&-1!==sr(s.toLowerCase(),t,0))return!0}else{if(1===o){for(;r<e.length&&"string"==typeof(o=e[r++]);)if(o.toLowerCase()===t)return!0;return!1}"number"==typeof o&&(i=!1)}}return!1}function Vr(e){return 4===e.type&&e.value!==lr}function Wn(e,t,n){return t===(4!==e.type||n?e.value:lr)}function vi(e,t,n){let r=4;const i=e.attrs||[],o=function ki(e){for(let t=0;t<e.length;t++)if(Ri(e[t]))return t;return e.length}(i);let s=!1;for(let c=0;c<t.length;c++){const g=t[c];if("number"!=typeof g){if(!s)if(4&r){if(r=2|1&r,""!==g&&!Wn(e,g,n)||""===g&&1===t.length){if(Zn(r))return!1;s=!0}}else{const _=8&r?g:t[++c];if(8&r&&null!==e.attrs){if(!Fi(e.attrs,_,n)){if(Zn(r))return!1;s=!0}continue}const x=ao(8&r?"class":g,i,Vr(e),n);if(-1===x){if(Zn(r))return!1;s=!0;continue}if(""!==_){let H;H=x>o?"":i[x+1].toLowerCase();const W=8&r?H:null;if(W&&-1!==sr(W,_,0)||2&r&&_!==H){if(Zn(r))return!1;s=!0}}}}else{if(!s&&!Zn(r)&&!Zn(g))return!1;if(s&&Zn(g))continue;s=!1,r=g|1&r}}return Zn(r)||s}function Zn(e){return 0==(1&e)}function ao(e,t,n,r){if(null===t)return-1;let i=0;if(r||!n){let o=!1;for(;i<t.length;){const s=t[i];if(s===e)return i;if(3===s||6===s)o=!0;else{if(1===s||2===s){let c=t[++i];for(;"string"==typeof c;)c=t[++i];continue}if(4===s)break;if(0===s){i+=4;continue}}i+=o?1:2}return-1}return function oi(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){const r=e[n];if("number"==typeof r)return-1;if(r===t)return n;n++}return-1}(t,e)}function Ar(e,t,n=!1){for(let r=0;r<t.length;r++)if(vi(e,t[r],n))return!0;return!1}function lo(e,t){return e?":not("+t.trim()+")":t}function Zi(e){let t=e[0],n=1,r=2,i="",o=!1;for(;n<e.length;){let s=e[n];if("string"==typeof s)if(2&r){const c=e[++n];i+="["+s+(c.length>0?'="'+c+'"':"")+"]"}else 8&r?i+="."+s:4&r&&(i+=" "+s);else""!==i&&!Zn(s)&&(t+=lo(o,i),i=""),r=s,o=o||!Zn(r);n++}return""!==i&&(t+=lo(o,i)),t}function si(e){return dt(()=>{const t=Li(e),n={...t,decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Rt.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||vn.Emulated,styles:e.styles||pt,_:null,schemas:e.schemas||null,tView:null,id:""};Nr(n);const r=e.dependencies;return n.directiveDefs=Qr(r,!1),n.pipeDefs=Qr(r,!0),n.id=function _i(e){let t=0;const n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(const i of n)t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}(n),n})}function jr(e){return Mt(e)||on(e)}function Di(e){return null!==e}function ai(e){return dt(()=>({type:e.type,bootstrap:e.bootstrap||pt,declarations:e.declarations||pt,imports:e.imports||pt,exports:e.exports||pt,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Xr(e,t){if(null==e)return Gn;const n={};for(const r in e)if(e.hasOwnProperty(r)){let i=e[r],o=i;Array.isArray(i)&&(o=i[1],i=i[0]),n[i]=r,t&&(t[i]=o)}return n}function Ci(e){return dt(()=>{const t=Li(e);return Nr(t),t})}function Yi(e){return{type:e.type,name:e.name,factory:null,pure:!1!==e.pure,standalone:!0===e.standalone,onDestroy:e.type.prototype.ngOnDestroy||null}}function Mt(e){return e[or]||null}function on(e){return e[Ir]||null}function sn(e){return e[ni]||null}function Or(e){const t=Mt(e)||on(e)||sn(e);return null!==t&&t.standalone}function bn(e,t){const n=e[Tr]||null;if(!n&&!0===t)throw new Error(`Type ${Ve(e)} does not have '\u0275mod' property.`);return n}function Li(e){const t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||Gn,exportAs:e.exportAs||null,standalone:!0===e.standalone,signals:!0===e.signals,selectors:e.selectors||pt,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Xr(e.inputs,t),outputs:Xr(e.outputs)}}function Nr(e){e.features?.forEach(t=>t(e))}function Qr(e,t){if(!e)return null;const n=t?sn:jr;return()=>("function"==typeof e?e():e).map(r=>n(r)).filter(Di)}const ln=0,Le=1,mt=2,nn=3,Kn=4,li=5,Ln=6,tr=7,un=8,Cr=9,ur=10,lt=11,qr=12,Ei=13,Ur=14,an=15,Vn=16,nr=17,Sn=18,Zr=19,So=20,Br=21,Pr=22,Vi=23,wi=24,St=25,ji=1,eo=2,gn=7,Nn=9,jn=11;function Pn(e){return Array.isArray(e)&&"object"==typeof e[ji]}function Dn(e){return Array.isArray(e)&&!0===e[ji]}function Bi(e){return 0!=(4&e.flags)}function bi(e){return e.componentOffset>-1}function Bt(e){return 1==(1&e.flags)}function Rn(e){return!!e.template}function co(e){return 0!=(512&e[mt])}function no(e,t){return e.hasOwnProperty(vr)?e[vr]:null}let cn=null,cr=!1;function _r(e){const t=cn;return cn=e,t}const ns={version:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{}};function Ao(e){if(!po(e)||e.dirty){if(!e.producerMustRecompute(e)&&!rs(e))return void(e.dirty=!1);e.producerRecomputeValue(e),e.dirty=!1}}function No(e){e.dirty=!0,function ho(e){if(void 0===e.liveConsumerNode)return;const t=cr;cr=!0;try{for(const n of e.liveConsumerNode)n.dirty||No(n)}finally{cr=t}}(e),e.consumerMarkedDirty?.(e)}function Po(e){return e&&(e.nextProducerIndex=0),_r(e)}function Go(e,t){if(_r(t),e&&void 0!==e.producerNode&&void 0!==e.producerIndexOfThis&&void 0!==e.producerLastReadVersion){if(po(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Hi(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function rs(e){ro(e);for(let t=0;t<e.producerNode.length;t++){const n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Ao(n),r!==n.version))return!0}return!1}function is(e){if(ro(e),po(e))for(let t=0;t<e.producerNode.length;t++)Hi(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Hi(e,t){if(function ss(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}(e),ro(e),1===e.liveConsumerNode.length)for(let r=0;r<e.producerNode.length;r++)Hi(e.producerNode[r],e.producerIndexOfThis[r]);const n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){const r=e.liveConsumerIndexOfThis[t],i=e.liveConsumerNode[t];ro(i),i.producerIndexOfThis[r]=t}}function po(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function ro(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}let k=null;function q(e){const t=_r(null);try{return e()}finally{_r(t)}}const Pe=()=>{},Ue=(()=>({...ns,consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!1,consumerMarkedDirty:e=>{e.schedule(e.ref)},hasRun:!1,cleanupFn:Pe}))();class yt{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}}function st(){return _t}function _t(e){return e.type.prototype.ngOnChanges&&(e.setInput=Mn),mn}function mn(){const e=rr(this),t=e?.current;if(t){const n=e.previous;if(n===Gn)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Mn(e,t,n,r){const i=this.declaredInputs[n],o=rr(e)||function In(e,t){return e[Un]=t}(e,{previous:Gn,current:null}),s=o.current||(o.current={}),c=o.previous,g=c[i];s[i]=new yt(g&&g.currentValue,t,c===Gn),e[r]=t}st.ngInherit=!0;const Un="__ngSimpleChanges__";function rr(e){return e[Un]||null}const yn=function(e,t,n){};function Et(e){for(;Array.isArray(e);)e=e[ln];return e}function Ai(e,t){return Et(t[e])}function Cn(e,t){return Et(t[e.index])}function ls(e,t){return e.data[t]}function Xe(e,t){return e[t]}function Je(e,t){const n=t[e];return Pn(n)?n:n[ln]}function qn(e,t){return null==t?null:e[t]}function Rr(e){e[nr]=0}function Oi(e){1024&e[mt]||(e[mt]|=1024,us(e,1))}function Kt(e){1024&e[mt]&&(e[mt]&=-1025,us(e,-1))}function us(e,t){let n=e[nn];if(null===n)return;n[li]+=t;let r=n;for(n=n[nn];null!==n&&(1===t&&1===r[li]||-1===t&&0===r[li]);)n[li]+=t,r=n,n=n[nn]}const ut={lFrame:Xt(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function Mu(){return ut.bindingsEnabled}function _e(){return ut.lFrame.lView}function Ht(){return ut.lFrame.tView}function nl(e){return ut.lFrame.contextLView=e,e[un]}function rl(e){return ut.lFrame.contextLView=null,e}function fr(){let e=il();for(;null!==e&&64===e.type;)e=e.parent;return e}function il(){return ut.lFrame.currentTNode}function zi(e,t){const n=ut.lFrame;n.currentTNode=e,n.isParent=t}function ga(){return ut.lFrame.isParent}function ma(){ut.lFrame.isParent=!1}function $r(){const e=ut.lFrame;let t=e.bindingRootIndex;return-1===t&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function ds(){return ut.lFrame.bindingIndex++}function mo(e){const t=ut.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function u(e,t){const n=ut.lFrame;n.bindingIndex=n.bindingRootIndex=e,C(t)}function C(e){ut.lFrame.currentDirectiveIndex=e}function F(){return ut.lFrame.currentQueryIndex}function te(e){ut.lFrame.currentQueryIndex=e}function ee(e){const t=e[Le];return 2===t.type?t.declTNode:1===t.type?e[Ln]:null}function $e(e,t,n){if(n&ze.SkipSelf){let i=t,o=e;for(;!(i=i.parent,null!==i||n&ze.Host||(i=ee(o),null===i||(o=o[Ur],10&i.type))););if(null===i)return!1;t=i,e=o}const r=ut.lFrame=dn();return r.currentTNode=t,r.lView=e,!0}function wt(e){const t=dn(),n=e[Le];ut.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function dn(){const e=ut.lFrame,t=null===e?null:e.child;return null===t?Xt(e):t}function Xt(e){const t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return null!==e&&(e.child=t),t}function Gi(){const e=ut.lFrame;return ut.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}const Xo=Gi;function ko(){const e=Gi();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function xr(){return ut.lFrame.selectedIndex}function fs(e){ut.lFrame.selectedIndex=e}function Bn(){const e=ut.lFrame;return ls(e.tView,e.selectedIndex)}let rf=!0;function sl(){return rf}function qo(e){rf=e}function al(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){const o=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:c,ngAfterViewInit:g,ngAfterViewChecked:_,ngOnDestroy:A}=o;s&&(e.contentHooks??=[]).push(-n,s),c&&((e.contentHooks??=[]).push(n,c),(e.contentCheckHooks??=[]).push(n,c)),g&&(e.viewHooks??=[]).push(-n,g),_&&((e.viewHooks??=[]).push(n,_),(e.viewCheckHooks??=[]).push(n,_)),null!=A&&(e.destroyHooks??=[]).push(n,A)}}function ll(e,t,n){sf(e,t,3,n)}function ul(e,t,n,r){(3&e[mt])===n&&sf(e,t,n,r)}function Ou(e,t){let n=e[mt];(3&n)===t&&(n&=8191,n+=1,e[mt]=n)}function sf(e,t,n,r){const o=r??-1,s=t.length-1;let c=0;for(let g=void 0!==r?65535&e[nr]:0;g<s;g++)if("number"==typeof t[g+1]){if(c=t[g],null!=r&&c>=r)break}else t[g]<0&&(e[nr]+=65536),(c<o||-1==o)&&(mv(e,n,t,g),e[nr]=(**********&e[nr])+g+2),g++}function af(e,t){yn(4,e,t);const n=_r(null);try{t.call(e)}finally{_r(n),yn(5,e,t)}}function mv(e,t,n,r){const i=n[r]<0,o=n[r+1],c=e[i?-n[r]:n[r]];i?e[mt]>>13<e[nr]>>16&&(3&e[mt])===t&&(e[mt]+=8192,af(c,o)):af(c,o)}const Ms=-1;class va{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}}function Pu(e){return e!==Ms}function Da(e){return 32767&e}function Ca(e,t){let n=function Cv(e){return e>>16}(e),r=t;for(;n>0;)r=r[Ur],n--;return r}let Ru=!0;function cl(e){const t=Ru;return Ru=e,t}const lf=255,uf=5;let _v=0;const yo={};function dl(e,t){const n=cf(e,t);if(-1!==n)return n;const r=t[Le];r.firstCreatePass&&(e.injectorIndex=t.length,xu(r.data,e),xu(t,null),xu(r.blueprint,null));const i=fl(e,t),o=e.injectorIndex;if(Pu(i)){const s=Da(i),c=Ca(i,t),g=c[Le].data;for(let _=0;_<8;_++)t[o+_]=c[s+_]|g[s+_]}return t[o+8]=i,o}function xu(e,t){e.push(0,0,0,0,0,0,0,0,t)}function cf(e,t){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null===t[e.injectorIndex+8]?-1:e.injectorIndex}function fl(e,t){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;let n=0,r=null,i=t;for(;null!==i;){if(r=vf(i),null===r)return Ms;if(n++,i=i[Ur],-1!==r.injectorIndex)return r.injectorIndex|n<<16}return Ms}function Fu(e,t,n){!function Ev(e,t,n){let r;"string"==typeof n?r=n.charCodeAt(0)||0:n.hasOwnProperty(ri)&&(r=n[ri]),null==r&&(r=n[ri]=_v++);const i=r&lf;t.data[e+(i>>uf)]|=1<<i}(e,t,n)}function df(e,t,n){if(n&ze.Optional||void 0!==e)return e;Y()}function ff(e,t,n,r){if(n&ze.Optional&&void 0===r&&(r=null),!(n&(ze.Self|ze.Host))){const i=e[Cr],o=An(void 0);try{return i?i.get(t,r,n&ze.Optional):gr(t,r,n&ze.Optional)}finally{An(o)}}return df(r,0,n)}function hf(e,t,n,r=ze.Default,i){if(null!==e){if(2048&t[mt]&&!(r&ze.Self)){const s=function Tv(e,t,n,r,i){let o=e,s=t;for(;null!==o&&null!==s&&2048&s[mt]&&!(512&s[mt]);){const c=pf(o,s,n,r|ze.Self,yo);if(c!==yo)return c;let g=o.parent;if(!g){const _=s[So];if(_){const A=_.get(n,yo,r);if(A!==yo)return A}g=vf(s),s=s[Ur]}o=g}return i}(e,t,n,r,yo);if(s!==yo)return s}const o=pf(e,t,n,r,yo);if(o!==yo)return o}return ff(t,n,r,i)}function pf(e,t,n,r,i){const o=function Mv(e){if("string"==typeof e)return e.charCodeAt(0)||0;const t=e.hasOwnProperty(ri)?e[ri]:void 0;return"number"==typeof t?t>=0?t&lf:Iv:t}(n);if("function"==typeof o){if(!$e(t,e,r))return r&ze.Host?df(i,0,r):ff(t,n,r,i);try{let s;if(s=o(r),null!=s||r&ze.Optional)return s;Y()}finally{Xo()}}else if("number"==typeof o){let s=null,c=cf(e,t),g=Ms,_=r&ze.Host?t[an][Ln]:null;for((-1===c||r&ze.SkipSelf)&&(g=-1===c?fl(e,t):t[c+8],g!==Ms&&mf(r,!1)?(s=t[Le],c=Da(g),t=Ca(g,t)):c=-1);-1!==c;){const A=t[Le];if(gf(o,c,A.data)){const x=bv(c,t,n,s,r,_);if(x!==yo)return x}g=t[c+8],g!==Ms&&mf(r,t[Le].data[c+8]===_)&&gf(o,c,t)?(s=A,c=Da(g),t=Ca(g,t)):c=-1}}return i}function bv(e,t,n,r,i,o){const s=t[Le],c=s.data[e+8],A=hl(c,s,n,null==r?bi(c)&&Ru:r!=s&&0!=(3&c.type),i&ze.Host&&o===c);return null!==A?hs(t,s,A,c):yo}function hl(e,t,n,r,i){const o=e.providerIndexes,s=t.data,c=1048575&o,g=e.directiveStart,A=o>>20,H=i?c+A:e.directiveEnd;for(let W=r?c:c+A;W<H;W++){const he=s[W];if(W<g&&n===he||W>=g&&he.type===n)return W}if(i){const W=s[g];if(W&&Rn(W)&&W.type===n)return g}return null}function hs(e,t,n,r){let i=e[n];const o=t.data;if(function yv(e){return e instanceof va}(i)){const s=i;s.resolving&&function Fn(e,t){const n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new xe(-200,`Circular dependency in DI detected for ${e}${n}`)}(function ft(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():je(e)}(o[n]));const c=cl(s.canSeeViewProviders);s.resolving=!0;const _=s.injectImpl?An(s.injectImpl):null;$e(e,r,ze.Default);try{i=e[n]=s.factory(void 0,o,e,r),t.firstCreatePass&&n>=r.directiveStart&&function gv(e,t,n){const{ngOnChanges:r,ngOnInit:i,ngDoCheck:o}=t.type.prototype;if(r){const s=_t(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}i&&(n.preOrderHooks??=[]).push(0-e,i),o&&((n.preOrderHooks??=[]).push(e,o),(n.preOrderCheckHooks??=[]).push(e,o))}(n,o[n],t)}finally{null!==_&&An(_),cl(c),s.resolving=!1,Xo()}}return i}function gf(e,t,n){return!!(n[t+(e>>uf)]&1<<e)}function mf(e,t){return!(e&ze.Self||e&ze.Host&&t)}class Yr{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return hf(this._tNode,this._lView,t,rn(r),n)}}function Iv(){return new Yr(fr(),_e())}function yf(e){return dt(()=>{const t=e.prototype.constructor,n=t[vr]||ku(t),r=Object.prototype;let i=Object.getPrototypeOf(e.prototype).constructor;for(;i&&i!==r;){const o=i[vr]||ku(i);if(o&&o!==n)return o;i=Object.getPrototypeOf(i)}return o=>new o})}function ku(e){return rt(e)?()=>{const t=ku(Ee(e));return t&&t()}:no(e)}function vf(e){const t=e[Le],n=t.type;return 2===n?t.declTNode:1===n?e[Ln]:null}function Lu(e){return function wv(e,t){if("class"===t)return e.classes;if("style"===t)return e.styles;const n=e.attrs;if(n){const r=n.length;let i=0;for(;i<r;){const o=n[i];if(Ri(o))break;if(0===o)i+=2;else if("number"==typeof o)for(i++;i<r&&"string"==typeof n[i];)i++;else{if(o===t)return n[i+1];i+=2}}}return null}(fr(),e)}const Is="__parameters__";function As(e,t,n){return dt(()=>{const r=function Vu(e){return function(...n){if(e){const r=e(...n);for(const i in r)this[i]=r[i]}}}(t);function i(...o){if(this instanceof i)return r.apply(this,o),this;const s=new i(...o);return c.annotation=s,c;function c(g,_,A){const x=g.hasOwnProperty(Is)?g[Is]:Object.defineProperty(g,Is,{value:[]})[Is];for(;x.length<=A;)x.push(null);return(x[A]=x[A]||[]).push(s),g}}return n&&(i.prototype=Object.create(n.prototype)),i.prototype.ngMetadataName=e,i.annotationCls=i,i})}function Ns(e,t){e.forEach(n=>Array.isArray(n)?Ns(n,t):t(n))}function Cf(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function pl(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Ni(e,t,n){let r=Ps(e,t);return r>=0?e[1|r]=n:(r=~r,function Fv(e,t,n,r){let i=e.length;if(i==t)e.push(n,r);else if(1===i)e.push(r,e[0]),e[0]=n;else{for(i--,e.push(e[i-1],e[i]);i>t;)e[i]=e[i-2],i--;e[t]=n,e[t+1]=r}}(e,r,t,n)),r}function ju(e,t){const n=Ps(e,t);if(n>=0)return e[1|n]}function Ps(e,t){return function _f(e,t,n){let r=0,i=e.length>>n;for(;i!==r;){const o=r+(i-r>>1),s=e[o<<n];if(t===s)return o<<n;s>t?i=o:r=o+1}return~(i<<n)}(e,t,1)}const ml=Jt(As("Optional"),8),yl=Jt(As("SkipSelf"),4);function El(e){return 128==(128&e.flags)}var bl=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(bl||{});const nD=/^>|^->|<!--|-->|--!>|<!-$/g,rD=/(<|>)/g,iD="\u200b$1\u200b";const zu=new Map;let oD=0;const Wu="__ngContext__";function Hr(e,t){Pn(t)?(e[Wu]=t[Zr],function aD(e){zu.set(e[Zr],e)}(t)):e[Wu]=t}let Ku;function Xu(e,t){return Ku(e,t)}function Sa(e){const t=e[nn];return Dn(t)?t[nn]:t}function Bf(e){return Hf(e[qr])}function $f(e){return Hf(e[Kn])}function Hf(e){for(;null!==e&&!Dn(e);)e=e[Kn];return e}function Fs(e,t,n,r,i){if(null!=r){let o,s=!1;Dn(r)?o=r:Pn(r)&&(s=!0,r=r[ln]);const c=Et(r);0===e&&null!==n?null==i?Kf(t,n,c):ps(t,n,c,i||null,!0):1===e&&null!==n?ps(t,n,c,i||null,!0):2===e?function Ol(e,t,n){const r=Tl(e,t);r&&function SD(e,t,n,r){e.removeChild(t,n,r)}(e,r,t,n)}(t,c,s):3===e&&t.destroyNode(c),null!=o&&function AD(e,t,n,r,i){const o=n[gn];o!==Et(n)&&Fs(t,e,r,o,i);for(let c=jn;c<n.length;c++){const g=n[c];Ta(g[Le],g,e,t,r,o)}}(t,e,o,n,i)}}function Qu(e,t){return e.createComment(function Rf(e){return e.replace(nD,t=>t.replace(rD,iD))}(t))}function Sl(e,t,n){return e.createElement(t,n)}function Gf(e,t){const n=e[Nn],r=n.indexOf(t);Kt(t),n.splice(r,1)}function Il(e,t){if(e.length<=jn)return;const n=jn+t,r=e[n];if(r){const i=r[Vn];null!==i&&i!==e&&Gf(i,r),t>0&&(e[n-1][Kn]=r[Kn]);const o=pl(e,jn+t);!function vD(e,t){Ta(e,t,t[lt],2,null,null),t[ln]=null,t[Ln]=null}(r[Le],r);const s=o[Sn];null!==s&&s.detachView(o[Le]),r[nn]=null,r[Kn]=null,r[mt]&=-129}return r}function qu(e,t){if(!(256&t[mt])){const n=t[lt];t[Vi]&&is(t[Vi]),t[wi]&&is(t[wi]),n.destroyNode&&Ta(e,t,n,3,null,null),function _D(e){let t=e[qr];if(!t)return Zu(e[Le],e);for(;t;){let n=null;if(Pn(t))n=t[qr];else{const r=t[jn];r&&(n=r)}if(!n){for(;t&&!t[Kn]&&t!==e;)Pn(t)&&Zu(t[Le],t),t=t[nn];null===t&&(t=e),Pn(t)&&Zu(t[Le],t),n=t&&t[Kn]}t=n}}(t)}}function Zu(e,t){if(!(256&t[mt])){t[mt]&=-129,t[mt]|=256,function MD(e,t){let n;if(null!=e&&null!=(n=e.destroyHooks))for(let r=0;r<n.length;r+=2){const i=t[n[r]];if(!(i instanceof va)){const o=n[r+1];if(Array.isArray(o))for(let s=0;s<o.length;s+=2){const c=i[o[s]],g=o[s+1];yn(4,c,g);try{g.call(c)}finally{yn(5,c,g)}}else{yn(4,i,o);try{o.call(i)}finally{yn(5,i,o)}}}}}(e,t),function bD(e,t){const n=e.cleanup,r=t[tr];if(null!==n)for(let o=0;o<n.length-1;o+=2)if("string"==typeof n[o]){const s=n[o+3];s>=0?r[s]():r[-s].unsubscribe(),o+=2}else n[o].call(r[n[o+1]]);null!==r&&(t[tr]=null);const i=t[Br];if(null!==i){t[Br]=null;for(let o=0;o<i.length;o++)(0,i[o])()}}(e,t),1===t[Le].type&&t[lt].destroy();const n=t[Vn];if(null!==n&&Dn(t[nn])){n!==t[nn]&&Gf(n,t);const r=t[Sn];null!==r&&r.detachView(e)}!function lD(e){zu.delete(e[Zr])}(t)}}function Ju(e,t,n){return function Wf(e,t,n){let r=t;for(;null!==r&&40&r.type;)r=(t=r).parent;if(null===r)return n[ln];{const{componentOffset:i}=r;if(i>-1){const{encapsulation:o}=e.data[r.directiveStart+i];if(o===vn.None||o===vn.Emulated)return null}return Cn(r,n)}}(e,t.parent,n)}function ps(e,t,n,r,i){e.insertBefore(t,n,r,i)}function Kf(e,t,n){e.appendChild(t,n)}function Xf(e,t,n,r,i){null!==r?ps(e,t,n,r,i):Kf(e,t,n)}function Tl(e,t){return e.parentNode(t)}let Yu,rc,Pl,Zf=function qf(e,t,n){return 40&e.type?Cn(e,n):null};function Al(e,t,n,r){const i=Ju(e,r,t),o=t[lt],c=function Qf(e,t,n){return Zf(e,t,n)}(r.parent||t[Ln],r,t);if(null!=i)if(Array.isArray(n))for(let g=0;g<n.length;g++)Xf(o,i,n[g],c,!1);else Xf(o,i,n,c,!1);void 0!==Yu&&Yu(o,r,t,n,i)}function Ia(e,t){if(null!==t){const n=t.type;if(3&n)return Cn(t,e);if(4&n)return ec(-1,e[t.index]);if(8&n){const r=t.child;if(null!==r)return Ia(e,r);{const i=e[t.index];return Dn(i)?ec(-1,i):Et(i)}}if(32&n)return Xu(t,e)()||Et(e[t.index]);{const r=Yf(e,t);return null!==r?Array.isArray(r)?r[0]:Ia(Sa(e[an]),r):Ia(e,t.next)}}return null}function Yf(e,t){return null!==t?e[an][Ln].projection[t.projection]:null}function ec(e,t){const n=jn+e+1;if(n<t.length){const r=t[n],i=r[Le].firstChild;if(null!==i)return Ia(r,i)}return t[gn]}function tc(e,t,n,r,i,o,s){for(;null!=n;){const c=r[n.index],g=n.type;if(s&&0===t&&(c&&Hr(Et(c),r),n.flags|=2),32!=(32&n.flags))if(8&g)tc(e,t,n.child,r,i,o,!1),Fs(t,e,i,c,o);else if(32&g){const _=Xu(n,r);let A;for(;A=_();)Fs(t,e,i,A,o);Fs(t,e,i,c,o)}else 16&g?th(e,t,r,n,i,o):Fs(t,e,i,c,o);n=s?n.projectionNext:n.next}}function Ta(e,t,n,r,i,o){tc(n,r,e.firstChild,t,i,o,!1)}function th(e,t,n,r,i,o){const s=n[an],g=s[Ln].projection[r.projection];if(Array.isArray(g))for(let _=0;_<g.length;_++)Fs(t,e,i,g[_],o);else{let _=g;const A=s[nn];El(r)&&(_.flags|=128),tc(e,t,_,A,i,o,!0)}}function nh(e,t,n){""===n?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function rh(e,t,n){const{mergedAttrs:r,classes:i,styles:o}=n;null!==r&&Dr(e,t,r),null!==i&&nh(e,t,i),null!==o&&function ND(e,t,n){e.setAttribute(t,"style",n)}(e,t,o)}function FD(e){rc=e}function sh(e){return function ic(){if(void 0===Pl&&(Pl=null,Ze.trustedTypes))try{Pl=Ze.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Pl}()?.createScriptURL(e)||e}class ah{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${$n})`}}function Zo(e){return e instanceof ah?e.changingThisBreaksApplicationSecurity:e}function Aa(e,t){const n=function BD(e){return e instanceof ah&&e.getTypeName()||null}(e);if(null!=n&&n!==t){if("ResourceURL"===n&&"URL"===t)return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${$n})`)}return n===t}const GD=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;var Vs=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Vs||{});function uc(e){const t=Na();return t?t.sanitize(Vs.URL,e)||"":Aa(e,"URL")?Zo(e):function oc(e){return(e=String(e)).match(GD)?e:"unsafe:"+e}(je(e))}function ph(e){const t=Na();if(t)return sh(t.sanitize(Vs.RESOURCE_URL,e)||"");if(Aa(e,"ResourceURL"))return sh(Zo(e));throw new xe(904,!1)}function gh(e,t,n){return function rC(e,t){return"src"===t&&("embed"===e||"frame"===e||"iframe"===e||"media"===e||"script"===e)||"href"===t&&("base"===e||"link"===e)?ph:uc}(t,n)(e)}function Na(){const e=_e();return e&&e[ur].sanitizer}const Pa=new ot("ENVIRONMENT_INITIALIZER"),mh=new ot("INJECTOR",-1),yh=new ot("INJECTOR_DEF_TYPES");class cc{get(t,n=bt){if(n===bt){const r=new Error(`NullInjectorError: No provider for ${Ve(t)}!`);throw r.name="NullInjectorError",r}return n}}function dc(e){return{\u0275providers:e}}function iC(...e){return{\u0275providers:vh(0,e),\u0275fromNgModule:!0}}function vh(e,...t){const n=[],r=new Set;let i;const o=s=>{n.push(s)};return Ns(t,s=>{const c=s;xl(c,o,[],r)&&(i||=[],i.push(c))}),void 0!==i&&Dh(i,o),n}function Dh(e,t){for(let n=0;n<e.length;n++){const{ngModule:r,providers:i}=e[n];fc(i,o=>{t(o,r)})}}function xl(e,t,n,r){if(!(e=Ee(e)))return!1;let i=null,o=Nt(e);const s=!o&&Mt(e);if(o||s){if(s&&!s.standalone)return!1;i=e}else{const g=e.ngModule;if(o=Nt(g),!o)return!1;i=g}const c=r.has(i);if(s){if(c)return!1;if(r.add(i),s.dependencies){const g="function"==typeof s.dependencies?s.dependencies():s.dependencies;for(const _ of g)xl(_,t,n,r)}}else{if(!o)return!1;{if(null!=o.imports&&!c){let _;r.add(i);try{Ns(o.imports,A=>{xl(A,t,n,r)&&(_||=[],_.push(A))})}finally{}void 0!==_&&Dh(_,t)}if(!c){const _=no(i)||(()=>new i);t({provide:i,useFactory:_,deps:pt},i),t({provide:yh,useValue:i,multi:!0},i),t({provide:Pa,useValue:()=>Oe(i),multi:!0},i)}const g=o.providers;if(null!=g&&!c){const _=e;fc(g,A=>{t(A,_)})}}}return i!==e&&void 0!==e.providers}function fc(e,t){for(let n of e)Ft(n)&&(n=n.\u0275providers),Array.isArray(n)?fc(n,t):t(n)}const oC=De({provide:String,useValue:De});function hc(e){return null!==e&&"object"==typeof e&&oC in e}function gs(e){return"function"==typeof e}const pc=new ot("Set Injector scope."),Fl={},aC={};let gc;function kl(){return void 0===gc&&(gc=new cc),gc}class vo{}class js extends vo{get destroyed(){return this._destroyed}constructor(t,n,r,i){super(),this.parent=n,this.source=r,this.scopes=i,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,yc(t,s=>this.processProvider(s)),this.records.set(mh,Us(void 0,this)),i.has("environment")&&this.records.set(vo,Us(void 0,this));const o=this.records.get(pc);null!=o&&"string"==typeof o.value&&this.scopes.add(o.value),this.injectorDefTypes=new Set(this.get(yh.multi,pt,ze.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;try{for(const n of this._ngOnDestroyHooks)n.ngOnDestroy();const t=this._onDestroyHooks;this._onDestroyHooks=[];for(const n of t)n()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear()}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();const n=Se(this),r=An(void 0);try{return t()}finally{Se(n),An(r)}}get(t,n=bt,r=ze.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Lr))return t[Lr](this);r=rn(r);const o=Se(this),s=An(void 0);try{if(!(r&ze.SkipSelf)){let g=this.records.get(t);if(void 0===g){const _=function fC(e){return"function"==typeof e||"object"==typeof e&&e instanceof ot}(t)&&Ct(t);g=_&&this.injectableDefInScope(_)?Us(mc(t),Fl):null,this.records.set(t,g)}if(null!=g)return this.hydrate(t,g)}return(r&ze.Self?kl():this.parent).get(t,n=r&ze.Optional&&n===bt?null:n)}catch(c){if("NullInjectorError"===c.name){if((c[Yt]=c[Yt]||[]).unshift(Ve(t)),o)throw c;return function Kr(e,t,n,r){const i=e[Yt];throw t[j]&&i.unshift(t[j]),e.message=function yr(e,t,n,r=null){e=e&&"\n"===e.charAt(0)&&"\u0275"==e.charAt(1)?e.slice(2):e;let i=Ve(t);if(Array.isArray(t))i=t.map(Ve).join(" -> ");else if("object"==typeof t){let o=[];for(let s in t)if(t.hasOwnProperty(s)){let c=t[s];o.push(s+":"+("string"==typeof c?JSON.stringify(c):Ve(c)))}i=`{${o.join(", ")}}`}return`${n}${r?"("+r+")":""}[${i}]: ${e.replace(Sr,"\n  ")}`}("\n"+e.message,i,n,r),e.ngTokenPath=i,e[Yt]=null,e}(c,t,"R3InjectorError",this.source)}throw c}finally{An(s),Se(o)}}resolveInjectorInitializers(){const t=Se(this),n=An(void 0);try{const i=this.get(Pa.multi,pt,ze.Self);for(const o of i)o()}finally{Se(t),An(n)}}toString(){const t=[],n=this.records;for(const r of n.keys())t.push(Ve(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new xe(205,!1)}processProvider(t){let n=gs(t=Ee(t))?t:Ee(t&&t.provide);const r=function uC(e){return hc(e)?Us(void 0,e.useValue):Us(Eh(e),Fl)}(t);if(gs(t)||!0!==t.multi)this.records.get(n);else{let i=this.records.get(n);i||(i=Us(void 0,Fl,!0),i.factory=()=>ir(i.multi),this.records.set(n,i)),n=t,i.multi.push(t)}this.records.set(n,r)}hydrate(t,n){return n.value===Fl&&(n.value=aC,n.value=n.factory()),"object"==typeof n.value&&n.value&&function dC(e){return null!==e&&"object"==typeof e&&"function"==typeof e.ngOnDestroy}(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}injectableDefInScope(t){if(!t.providedIn)return!1;const n=Ee(t.providedIn);return"string"==typeof n?"any"===n||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){const n=this._onDestroyHooks.indexOf(t);-1!==n&&this._onDestroyHooks.splice(n,1)}}function mc(e){const t=Ct(e),n=null!==t?t.factory:no(e);if(null!==n)return n;if(e instanceof ot)throw new xe(204,!1);if(e instanceof Function)return function lC(e){const t=e.length;if(t>0)throw function wa(e,t){const n=[];for(let r=0;r<e;r++)n.push(t);return n}(t,"?"),new xe(204,!1);const n=function kn(e){return e&&(e[hn]||e[Zt])||null}(e);return null!==n?()=>n.factory(e):()=>new e}(e);throw new xe(204,!1)}function Eh(e,t,n){let r;if(gs(e)){const i=Ee(e);return no(i)||mc(i)}if(hc(e))r=()=>Ee(e.useValue);else if(function _h(e){return!(!e||!e.useFactory)}(e))r=()=>e.useFactory(...ir(e.deps||[]));else if(function Ch(e){return!(!e||!e.useExisting)}(e))r=()=>Oe(Ee(e.useExisting));else{const i=Ee(e&&(e.useClass||e.provide));if(!function cC(e){return!!e.deps}(e))return no(i)||mc(i);r=()=>new i(...ir(e.deps))}return r}function Us(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function yc(e,t){for(const n of e)Array.isArray(n)?yc(n,t):n&&Ft(n)?yc(n.\u0275providers,t):t(n)}const wh=new ot("AppId",{providedIn:"root",factory:()=>hC}),hC="ng",bh=new ot("Platform Initializer"),vc=new ot("Platform ID",{providedIn:"platform",factory:()=>"unknown"}),pC=new ot("AnimationModuleType"),gC=new ot("CSP nonce",{providedIn:"root",factory:()=>function Ls(){if(void 0!==rc)return rc;if(typeof document<"u")return document;throw new xe(210,!1)}().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});let Mh=(e,t,n)=>null;function Sc(e,t,n=!1){return Mh(e,t,n)}class MC{}class Th{}class IC{resolveComponentFactory(t){throw function SC(e){const t=Error(`No component factory found for ${Ve(e)}.`);return t.ngComponent=e,t}(t)}}let $l=(()=>{class e{static{this.NULL=new IC}}return e})();function TC(){return Hs(fr(),_e())}function Hs(e,t){return new Fa(Cn(e,t))}let Fa=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=TC}}return e})();function AC(e){return e instanceof Fa?e.nativeElement:e}class Oh{}let OC=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>function NC(){const e=_e(),n=Je(fr().index,e);return(Pn(n)?n:e)[lt]}()}}return e})(),PC=(()=>{class e{static{this.\u0275prov=Ut({token:e,providedIn:"root",factory:()=>null})}}return e})();class Nh{constructor(t){this.full=t,this.major=t.split(".")[0],this.minor=t.split(".")[1],this.patch=t.split(".").slice(2).join(".")}}const RC=new Nh("16.2.12"),Ac={};function Fh(e,t=null,n=null,r){const i=kh(e,t,n,r);return i.resolveInjectorInitializers(),i}function kh(e,t=null,n=null,r,i=new Set){const o=[n||pt,iC(e)];return r=r||("object"==typeof e?void 0:Ve(e)),new js(o,t||kl(),r||null,i)}let io=(()=>{class e{static{this.THROW_IF_NOT_FOUND=bt}static{this.NULL=new cc}static create(n,r){if(Array.isArray(n))return Fh({name:""},r,n,"");{const i=n.name??"";return Fh({name:i},n.parent,n.providers,i)}}static{this.\u0275prov=Ut({token:e,providedIn:"any",factory:()=>Oe(mh)})}static{this.__NG_ELEMENT_ID__=-1}}return e})();function Nc(e){return e.ngOriginalError}class Jo{constructor(){this._console=console}handleError(t){const n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&Nc(t);for(;n&&Nc(n);)n=Nc(n);return n||null}}function Pc(e){return t=>{setTimeout(e,void 0,t)}}const Do=class BC extends d.B{constructor(t=!1){super(),this.__isAsync=t}emit(t){super.next(t)}subscribe(t,n,r){let i=t,o=n||(()=>null),s=r;if(t&&"object"==typeof t){const g=t;i=g.next?.bind(g),o=g.error?.bind(g),s=g.complete?.bind(g)}this.__isAsync&&(o=Pc(o),i&&(i=Pc(i)),s&&(s=Pc(s)));const c=super.subscribe({next:i,error:o,complete:s});return t instanceof w.yU&&t.add(c),c}};function Vh(...e){}class wr{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new Do(!1),this.onMicrotaskEmpty=new Do(!1),this.onStable=new Do(!1),this.onError=new Do(!1),typeof Zone>"u")throw new xe(908,!1);Zone.assertZonePatched();const i=this;i._nesting=0,i._outer=i._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(i._inner=i._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(i._inner=i._inner.fork(Zone.longStackTraceZoneSpec)),i.shouldCoalesceEventChangeDetection=!r&&n,i.shouldCoalesceRunChangeDetection=r,i.lastRequestAnimationFrameId=-1,i.nativeRequestAnimationFrame=function $C(){const e="function"==typeof Ze.requestAnimationFrame;let t=Ze[e?"requestAnimationFrame":"setTimeout"],n=Ze[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&n){const r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r);const i=n[Zone.__symbol__("OriginalDelegate")];i&&(n=i)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:n}}().nativeRequestAnimationFrame,function GC(e){const t=()=>{!function zC(e){e.isCheckStableRunning||-1!==e.lastRequestAnimationFrameId||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(Ze,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,xc(e),e.isCheckStableRunning=!0,Rc(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),xc(e))}(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,i,o,s,c)=>{if(function KC(e){return!(!Array.isArray(e)||1!==e.length)&&!0===e[0].data?.__ignore_ng_zone__}(c))return n.invokeTask(i,o,s,c);try{return jh(e),n.invokeTask(i,o,s,c)}finally{(e.shouldCoalesceEventChangeDetection&&"eventTask"===o.type||e.shouldCoalesceRunChangeDetection)&&t(),Uh(e)}},onInvoke:(n,r,i,o,s,c,g)=>{try{return jh(e),n.invoke(i,o,s,c,g)}finally{e.shouldCoalesceRunChangeDetection&&t(),Uh(e)}},onHasTask:(n,r,i,o)=>{n.hasTask(i,o),r===i&&("microTask"==o.change?(e._hasPendingMicrotasks=o.microTask,xc(e),Rc(e)):"macroTask"==o.change&&(e.hasPendingMacrotasks=o.macroTask))},onHandleError:(n,r,i,o)=>(n.handleError(i,o),e.runOutsideAngular(()=>e.onError.emit(o)),!1)})}(i)}static isInAngularZone(){return typeof Zone<"u"&&!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!wr.isInAngularZone())throw new xe(909,!1)}static assertNotInAngularZone(){if(wr.isInAngularZone())throw new xe(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,i){const o=this._inner,s=o.scheduleEventTask("NgZoneEvent: "+i,t,HC,Vh,Vh);try{return o.runTask(s,n,r)}finally{o.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}}const HC={};function Rc(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function xc(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&-1!==e.lastRequestAnimationFrameId)}function jh(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Uh(e){e._nesting--,Rc(e)}class WC{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new Do,this.onMicrotaskEmpty=new Do,this.onStable=new Do,this.onError=new Do}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,i){return t.apply(n,r)}}const Bh=new ot("",{providedIn:"root",factory:$h});function $h(){const e=ke(wr);let t=!0;return function le(...e){const t=(0,ie.lI)(e),n=(0,ie.R0)(e,1/0),r=e;return r.length?1===r.length?(0,X.Tg)(r[0]):(0,ae.U)(n)((0,ge.H)(r,t)):oe.w}(new de.c(i=>{t=e.isStable&&!e.hasPendingMacrotasks&&!e.hasPendingMicrotasks,e.runOutsideAngular(()=>{i.next(t),i.complete()})}),new de.c(i=>{let o;e.runOutsideAngular(()=>{o=e.onStable.subscribe(()=>{wr.assertNotInAngularZone(),queueMicrotask(()=>{!t&&!e.hasPendingMacrotasks&&!e.hasPendingMicrotasks&&(t=!0,i.next(!0))})})});const s=e.onUnstable.subscribe(()=>{wr.assertInAngularZone(),t&&(t=!1,e.runOutsideAngular(()=>{i.next(!1)}))});return()=>{o.unsubscribe(),s.unsubscribe()}}).pipe(Ne()))}function Vo(e){return e instanceof Function?e():e}let Fc=(()=>{class e{constructor(){this.renderDepth=0,this.handler=null}begin(){this.handler?.validateBegin(),this.renderDepth++}end(){this.renderDepth--,0===this.renderDepth&&this.handler?.execute()}ngOnDestroy(){this.handler?.destroy(),this.handler=null}static{this.\u0275prov=Ut({token:e,providedIn:"root",factory:()=>new e})}}return e})();function ka(e){for(;e;){e[mt]|=64;const t=Sa(e);if(co(e)&&!t)return e;e=t}return null}const Kh=new ot("",{providedIn:"root",factory:()=>!1});let Gl=null;function Zh(e,t){return e[t]??ep()}function Jh(e,t){const n=ep();n.producerNode?.length&&(e[t]=Gl,n.lView=e,Gl=Yh())}const r_={...ns,consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{ka(e.lView)},lView:null};function Yh(){return Object.create(r_)}function ep(){return Gl??=Yh(),Gl}const Ot={};function tp(e){np(Ht(),_e(),xr()+e,!1)}function np(e,t,n,r){if(!r)if(3==(3&t[mt])){const o=e.preOrderCheckHooks;null!==o&&ll(t,o,n)}else{const o=e.preOrderHooks;null!==o&&ul(t,o,0,n)}fs(n)}function Ws(e,t=ze.Default){const n=_e();return null===n?Oe(e,t):hf(fr(),n,Ee(e),t)}function rp(){throw new Error("invalid")}function Wl(e,t,n,r,i,o,s,c,g,_,A){const x=t.blueprint.slice();return x[ln]=i,x[mt]=140|r,(null!==_||e&&2048&e[mt])&&(x[mt]|=2048),Rr(x),x[nn]=x[Ur]=e,x[un]=n,x[ur]=s||e&&e[ur],x[lt]=c||e&&e[lt],x[Cr]=g||e&&e[Cr]||null,x[Ln]=o,x[Zr]=function sD(){return oD++}(),x[Pr]=A,x[So]=_,x[an]=2==t.type?e[an]:x,x}function Ks(e,t,n,r,i){let o=e.data[t];if(null===o)o=function kc(e,t,n,r,i){const o=il(),s=ga(),g=e.data[t]=function d_(e,t,n,r,i,o){let s=t?t.injectorIndex:-1,c=0;return function Fo(){return null!==ut.skipHydrationRootTNode}()&&(c|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:c,providerIndexes:0,value:i,attrs:o,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,s?o:o&&o.parent,n,t,r,i);return null===e.firstChild&&(e.firstChild=g),null!==o&&(s?null==o.child&&null!==g.parent&&(o.child=g):null===o.next&&(o.next=g,g.prev=o)),g}(e,t,n,r,i),function f(){return ut.lFrame.inI18n}()&&(o.flags|=32);else if(64&o.type){o.type=n,o.value=r,o.attrs=i;const s=function cs(){const e=ut.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}();o.injectorIndex=null===s?-1:s.injectorIndex}return zi(o,!0),o}function La(e,t,n,r){if(0===n)return-1;const i=t.length;for(let o=0;o<n;o++)t.push(r),e.blueprint.push(r),e.data.push(null);return i}function ip(e,t,n,r,i){const o=Zh(t,Vi),s=xr(),c=2&r;try{fs(-1),c&&t.length>St&&np(e,t,St,!1),yn(c?2:0,i);const _=c?o:null,A=Po(_);try{null!==_&&(_.dirty=!1),n(r,i)}finally{Go(_,A)}}finally{c&&null===t[Vi]&&Jh(t,Vi),fs(s),yn(c?3:1,i)}}function Lc(e,t,n){if(Bi(t)){const r=_r(null);try{const o=t.directiveEnd;for(let s=t.directiveStart;s<o;s++){const c=e.data[s];c.contentQueries&&c.contentQueries(1,n[s],s)}}finally{_r(r)}}}function Vc(e,t,n){Mu()&&(function v_(e,t,n,r){const i=n.directiveStart,o=n.directiveEnd;bi(n)&&function M_(e,t,n){const r=Cn(t,e),i=op(n);let s=16;n.signals?s=4096:n.onPush&&(s=64);const c=Kl(e,Wl(e,i,null,s,r,t,null,e[ur].rendererFactory.createRenderer(r,n),null,null,null));e[t.index]=c}(t,n,e.data[i+n.componentOffset]),e.firstCreatePass||dl(n,t),Hr(r,t);const s=n.initialInputs;for(let c=i;c<o;c++){const g=e.data[c],_=hs(t,e,c,n);Hr(_,t),null!==s&&S_(0,c-i,_,g,0,s),Rn(g)&&(Je(n.index,t)[un]=hs(t,e,c,n))}}(e,t,n,Cn(n,t)),64==(64&n.flags)&&cp(e,t,n))}function jc(e,t,n=Cn){const r=t.localNames;if(null!==r){let i=t.index+1;for(let o=0;o<r.length;o+=2){const s=r[o+1],c=-1===s?n(t,e):e[s];e[i++]=c}}}function op(e){const t=e.tView;return null===t||t.incompleteFirstPass?e.tView=Uc(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Uc(e,t,n,r,i,o,s,c,g,_,A){const x=St+r,H=x+i,W=function o_(e,t){const n=[];for(let r=0;r<t;r++)n.push(r<e?null:Ot);return n}(x,H),he="function"==typeof _?_():_;return W[Le]={type:e,blueprint:W,template:n,queries:null,viewQuery:c,declTNode:t,data:W.slice().fill(null,x),bindingStartIndex:x,expandoStartIndex:H,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof o?o():o,pipeRegistry:"function"==typeof s?s():s,firstChild:null,schemas:g,consts:he,incompleteFirstPass:!1,ssrId:A}}let sp=e=>null;function ap(e,t,n,r){for(let i in e)if(e.hasOwnProperty(i)){n=null===n?{}:n;const o=e[i];null===r?lp(n,t,i,o):r.hasOwnProperty(i)&&lp(n,t,r[i],o)}return n}function lp(e,t,n,r){e.hasOwnProperty(n)?e[n].push(t,r):e[n]=[t,r]}function Bc(e,t,n,r){if(Mu()){const i=null===r?null:{"":-1},o=function C_(e,t){const n=e.directiveRegistry;let r=null,i=null;if(n)for(let o=0;o<n.length;o++){const s=n[o];if(Ar(t,s.selectors,!1))if(r||(r=[]),Rn(s))if(null!==s.findHostDirectiveDefs){const c=[];i=i||new Map,s.findHostDirectiveDefs(s,c,i),r.unshift(...c,s),$c(e,t,c.length)}else r.unshift(s),$c(e,t,0);else i=i||new Map,s.findHostDirectiveDefs?.(s,r,i),r.push(s)}return null===r?null:[r,i]}(e,n);let s,c;null===o?s=c=null:[s,c]=o,null!==s&&up(e,t,n,s,i,c),i&&function __(e,t,n){if(t){const r=e.localNames=[];for(let i=0;i<t.length;i+=2){const o=n[t[i+1]];if(null==o)throw new xe(-301,!1);r.push(t[i],o)}}}(n,r,i)}n.mergedAttrs=ar(n.mergedAttrs,n.attrs)}function up(e,t,n,r,i,o){for(let _=0;_<r.length;_++)Fu(dl(n,t),e,r[_].type);!function w_(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}(n,e.data.length,r.length);for(let _=0;_<r.length;_++){const A=r[_];A.providersResolver&&A.providersResolver(A)}let s=!1,c=!1,g=La(e,t,r.length,null);for(let _=0;_<r.length;_++){const A=r[_];n.mergedAttrs=ar(n.mergedAttrs,A.hostAttrs),b_(e,n,t,g,A),E_(g,A,i),null!==A.contentQueries&&(n.flags|=4),(null!==A.hostBindings||null!==A.hostAttrs||0!==A.hostVars)&&(n.flags|=64);const x=A.type.prototype;!s&&(x.ngOnChanges||x.ngOnInit||x.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!c&&(x.ngOnChanges||x.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),c=!0),g++}!function f_(e,t,n){const i=t.directiveEnd,o=e.data,s=t.attrs,c=[];let g=null,_=null;for(let A=t.directiveStart;A<i;A++){const x=o[A],H=n?n.get(x):null,he=H?H.outputs:null;g=ap(x.inputs,A,g,H?H.inputs:null),_=ap(x.outputs,A,_,he);const Me=null===g||null===s||Vr(t)?null:I_(g,A,s);c.push(Me)}null!==g&&(g.hasOwnProperty("class")&&(t.flags|=8),g.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=c,t.inputs=g,t.outputs=_}(e,n,o)}function cp(e,t,n){const r=n.directiveStart,i=n.directiveEnd,o=n.index,s=function m(){return ut.lFrame.currentDirectiveIndex}();try{fs(o);for(let c=r;c<i;c++){const g=e.data[c],_=t[c];C(c),(null!==g.hostBindings||0!==g.hostVars||null!==g.hostAttrs)&&D_(g,_)}}finally{fs(-1),C(s)}}function D_(e,t){null!==e.hostBindings&&e.hostBindings(1,t)}function $c(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function E_(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Rn(t)&&(n[""]=e)}}function b_(e,t,n,r,i){e.data[r]=i;const o=i.factory||(i.factory=no(i.type)),s=new va(o,Rn(i),Ws);e.blueprint[r]=s,n[r]=s,function m_(e,t,n,r,i){const o=i.hostBindings;if(o){let s=e.hostBindingOpCodes;null===s&&(s=e.hostBindingOpCodes=[]);const c=~t.index;(function y_(e){let t=e.length;for(;t>0;){const n=e[--t];if("number"==typeof n&&n<0)return n}return 0})(s)!=c&&s.push(c),s.push(n,r,o)}}(e,t,r,La(e,n,i.hostVars,Ot),i)}function Co(e,t,n,r,i,o){const s=Cn(e,t);!function Hc(e,t,n,r,i,o,s){if(null==o)e.removeAttribute(t,i,n);else{const c=null==s?je(o):s(o,r||"",i);e.setAttribute(t,i,c,n)}}(t[lt],s,o,e.value,n,r,i)}function S_(e,t,n,r,i,o){const s=o[t];if(null!==s)for(let c=0;c<s.length;)dp(r,n,s[c++],s[c++],s[c++])}function dp(e,t,n,r,i){const o=_r(null);try{const s=e.inputTransforms;null!==s&&s.hasOwnProperty(r)&&(i=s[r].call(t,i)),null!==e.setInput?e.setInput(t,i,n,r):t[r]=i}finally{_r(o)}}function I_(e,t,n){let r=null,i=0;for(;i<n.length;){const o=n[i];if(0!==o)if(5!==o){if("number"==typeof o)break;if(e.hasOwnProperty(o)){null===r&&(r=[]);const s=e[o];for(let c=0;c<s.length;c+=2)if(s[c]===t){r.push(o,s[c+1],n[i+1]);break}}i+=2}else i+=2;else i+=4}return r}function fp(e,t,n,r){return[e,!0,!1,t,null,0,r,n,null,null,null]}function hp(e,t){const n=e.contentQueries;if(null!==n)for(let r=0;r<n.length;r+=2){const o=n[r+1];if(-1!==o){const s=e.data[o];te(n[r]),s.contentQueries(2,t[o],o)}}}function Kl(e,t){return e[qr]?e[Ei][Kn]=t:e[qr]=t,e[Ei]=t,t}function zc(e,t,n){te(0);const r=_r(null);try{t(e,n)}finally{_r(r)}}function pp(e){return e[tr]||(e[tr]=[])}function gp(e){return e.cleanup||(e.cleanup=[])}function yp(e,t){const n=e[Cr],r=n?n.get(Jo,null):null;r&&r.handleError(t)}function Gc(e,t,n,r,i){for(let o=0;o<n.length;){const s=n[o++],c=n[o++];dp(e.data[s],t[s],r,c,i)}}function jo(e,t,n){const r=Ai(t,e);!function zf(e,t,n){e.setValue(t,n)}(e[lt],r,n)}function T_(e,t){const n=Je(t,e),r=n[Le];!function A_(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}(r,n);const i=n[ln];null!==i&&null===n[Pr]&&(n[Pr]=Sc(i,n[Cr])),Wc(r,n,n[un])}function Wc(e,t,n){wt(t);try{const r=e.viewQuery;null!==r&&zc(1,r,n);const i=e.template;null!==i&&ip(e,t,i,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),e.staticContentQueries&&hp(e,t),e.staticViewQueries&&zc(2,e.viewQuery,n);const o=e.components;null!==o&&function O_(e,t){for(let n=0;n<t.length;n++)T_(e,t[n])}(t,o)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[mt]&=-5,ko()}}let vp=(()=>{class e{constructor(){this.all=new Set,this.queue=new Map}create(n,r,i){const o=typeof Zone>"u"?null:Zone.current,s=function ue(e,t,n){const r=Object.create(Ue);n&&(r.consumerAllowSignalWrites=!0),r.fn=e,r.schedule=t;const i=s=>{r.cleanupFn=s};return r.ref={notify:()=>No(r),run:()=>{if(r.dirty=!1,r.hasRun&&!rs(r))return;r.hasRun=!0;const s=Po(r);try{r.cleanupFn(),r.cleanupFn=Pe,r.fn(i)}finally{Go(r,s)}},cleanup:()=>r.cleanupFn()},r.ref}(n,_=>{this.all.has(_)&&this.queue.set(_,o)},i);let c;this.all.add(s),s.notify();const g=()=>{s.cleanup(),c?.(),this.all.delete(s),this.queue.delete(s)};return c=r?.onDestroy(g),{destroy:g}}flush(){if(0!==this.queue.size)for(const[n,r]of this.queue)this.queue.delete(n),r?r.run(()=>n.run()):n.run()}get isQueueEmpty(){return 0===this.queue.size}static{this.\u0275prov=Ut({token:e,providedIn:"root",factory:()=>new e})}}return e})();function Xl(e,t,n){let r=n?e.styles:null,i=n?e.classes:null,o=0;if(null!==t)for(let s=0;s<t.length;s++){const c=t[s];"number"==typeof c?o=c:1==o?i=nt(i,c):2==o&&(r=nt(r,c+": "+t[++s]+";"))}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=i:e.classesWithoutHost=i}function Va(e,t,n,r,i=!1){for(;null!==n;){const o=t[n.index];null!==o&&r.push(Et(o)),Dn(o)&&Dp(o,r);const s=n.type;if(8&s)Va(e,t,n.child,r);else if(32&s){const c=Xu(n,t);let g;for(;g=c();)r.push(g)}else if(16&s){const c=Yf(t,n);if(Array.isArray(c))r.push(...c);else{const g=Sa(t[an]);Va(g[Le],g,c,r,!0)}}n=i?n.projectionNext:n.next}return r}function Dp(e,t){for(let n=jn;n<e.length;n++){const r=e[n],i=r[Le].firstChild;null!==i&&Va(r[Le],r,i,t)}e[gn]!==e[ln]&&t.push(e[gn])}function Ql(e,t,n,r=!0){const i=t[ur],o=i.rendererFactory,s=i.afterRenderEventManager;o.begin?.(),s?.begin();try{Cp(e,t,e.template,n)}catch(g){throw r&&yp(t,g),g}finally{o.end?.(),i.effectManager?.flush(),s?.end()}}function Cp(e,t,n,r){const i=t[mt];if(256!=(256&i)){t[ur].effectManager?.flush(),wt(t);try{Rr(t),function Au(e){return ut.lFrame.bindingIndex=e}(e.bindingStartIndex),null!==n&&ip(e,t,n,2,r);const s=3==(3&i);if(s){const _=e.preOrderCheckHooks;null!==_&&ll(t,_,null)}else{const _=e.preOrderHooks;null!==_&&ul(t,_,0,null),Ou(t,0)}if(function R_(e){for(let t=Bf(e);null!==t;t=$f(t)){if(!t[eo])continue;const n=t[Nn];for(let r=0;r<n.length;r++){Oi(n[r])}}}(t),_p(t,2),null!==e.contentQueries&&hp(e,t),s){const _=e.contentCheckHooks;null!==_&&ll(t,_)}else{const _=e.contentHooks;null!==_&&ul(t,_,1),Ou(t,1)}!function i_(e,t){const n=e.hostBindingOpCodes;if(null===n)return;const r=Zh(t,wi);try{for(let i=0;i<n.length;i++){const o=n[i];if(o<0)fs(~o);else{const s=o,c=n[++i],g=n[++i];u(c,s),r.dirty=!1;const _=Po(r);try{g(2,t[s])}finally{Go(r,_)}}}}finally{null===t[wi]&&Jh(t,wi),fs(-1)}}(e,t);const c=e.components;null!==c&&wp(t,c,0);const g=e.viewQuery;if(null!==g&&zc(2,g,r),s){const _=e.viewCheckHooks;null!==_&&ll(t,_)}else{const _=e.viewHooks;null!==_&&ul(t,_,2),Ou(t,2)}!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),t[mt]&=-73,Kt(t)}finally{ko()}}}function _p(e,t){for(let n=Bf(e);null!==n;n=$f(n))for(let r=jn;r<n.length;r++)Ep(n[r],t)}function x_(e,t,n){Ep(Je(t,e),n)}function Ep(e,t){if(!function $t(e){return 128==(128&e[mt])}(e))return;const n=e[Le],r=e[mt];if(80&r&&0===t||1024&r||2===t)Cp(n,e,n.template,e[un]);else if(e[li]>0){_p(e,1);const i=n.components;null!==i&&wp(e,i,1)}}function wp(e,t,n){for(let r=0;r<t.length;r++)x_(e,t[r],n)}class ja{get rootNodes(){const t=this._lView,n=t[Le];return Va(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[un]}set context(t){this._lView[un]=t}get destroyed(){return 256==(256&this._lView[mt])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const t=this._lView[nn];if(Dn(t)){const n=t[8],r=n?n.indexOf(this):-1;r>-1&&(Il(t,r),pl(n,r))}this._attachedToViewContainer=!1}qu(this._lView[Le],this._lView)}onDestroy(t){!function pa(e,t){if(256==(256&e[mt]))throw new xe(911,!1);null===e[Br]&&(e[Br]=[]),e[Br].push(t)}(this._lView,t)}markForCheck(){ka(this._cdRefInjectingView||this._lView)}detach(){this._lView[mt]&=-129}reattach(){this._lView[mt]|=128}detectChanges(){Ql(this._lView[Le],this._lView,this.context)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new xe(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,function CD(e,t){Ta(e,t,t[lt],2,null,null)}(this._lView[Le],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new xe(902,!1);this._appRef=t}}class F_ extends ja{constructor(t){super(t),this._view=t}detectChanges(){const t=this._view;Ql(t[Le],t,t[un],!1)}checkNoChanges(){}get context(){return null}}class bp extends $l{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){const n=Mt(t);return new Ua(n,this.ngModule)}}function Mp(e){const t=[];for(let n in e)e.hasOwnProperty(n)&&t.push({propName:e[n],templateName:n});return t}class L_{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=rn(r);const i=this.injector.get(t,Ac,r);return i!==Ac||n===Ac?i:this.parentInjector.get(t,n,r)}}class Ua extends Th{get inputs(){const t=this.componentDef,n=t.inputTransforms,r=Mp(t.inputs);if(null!==n)for(const i of r)n.hasOwnProperty(i.propName)&&(i.transform=n[i.propName]);return r}get outputs(){return Mp(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=function bo(e){return e.map(Zi).join(",")}(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,i){let o=(i=i||this.ngModule)instanceof vo?i:i?.injector;o&&null!==this.componentDef.getStandaloneInjector&&(o=this.componentDef.getStandaloneInjector(o)||o);const s=o?new L_(t,o):t,c=s.get(Oh,null);if(null===c)throw new xe(407,!1);const x={rendererFactory:c,sanitizer:s.get(PC,null),effectManager:s.get(vp,null),afterRenderEventManager:s.get(Fc,null)},H=c.createRenderer(null,this.componentDef),W=this.componentDef.selectors[0][0]||"div",he=r?function s_(e,t,n,r){const o=r.get(Kh,!1)||n===vn.ShadowDom,s=e.selectRootElement(t,o);return function a_(e){sp(e)}(s),s}(H,r,this.componentDef.encapsulation,s):Sl(H,W,function k_(e){const t=e.toLowerCase();return"svg"===t?"svg":"math"===t?"math":null}(W)),et=this.componentDef.signals?4608:this.componentDef.onPush?576:528;let Ce=null;null!==he&&(Ce=Sc(he,s,!0));const At=Uc(0,null,null,1,0,null,null,null,null,null,null),xt=Wl(null,At,null,et,null,null,x,H,s,null,Ce);let pn,mi;wt(xt);try{const Uo=this.componentDef;let ca,Zd=null;Uo.findHostDirectiveDefs?(ca=[],Zd=new Map,Uo.findHostDirectiveDefs(Uo,ca,Zd),ca.push(Uo)):ca=[Uo];const HM=function j_(e,t){const n=e[Le],r=St;return e[r]=t,Ks(n,r,2,"#host",null)}(xt,he),zM=function U_(e,t,n,r,i,o,s){const c=i[Le];!function B_(e,t,n,r){for(const i of e)t.mergedAttrs=ar(t.mergedAttrs,i.hostAttrs);null!==t.mergedAttrs&&(Xl(t,t.mergedAttrs,!0),null!==n&&rh(r,n,t))}(r,e,t,s);let g=null;null!==t&&(g=Sc(t,i[Cr]));const _=o.rendererFactory.createRenderer(t,n);let A=16;n.signals?A=4096:n.onPush&&(A=64);const x=Wl(i,op(n),null,A,i[e.index],e,o,_,null,null,g);return c.firstCreatePass&&$c(c,e,r.length-1),Kl(i,x),i[e.index]=x}(HM,he,Uo,ca,xt,x,H);mi=ls(At,St),he&&function H_(e,t,n,r){if(r)Dr(e,n,["ng-version",RC.full]);else{const{attrs:i,classes:o}=function Bo(e){const t=[],n=[];let r=1,i=2;for(;r<e.length;){let o=e[r];if("string"==typeof o)2===i?""!==o&&t.push(o,e[++r]):8===i&&n.push(o);else{if(!Zn(i))break;i=o}r++}return{attrs:t,classes:n}}(t.selectors[0]);i&&Dr(e,n,i),o&&o.length>0&&nh(e,n,o.join(" "))}}(H,Uo,he,r),void 0!==n&&function z_(e,t,n){const r=e.projection=[];for(let i=0;i<t.length;i++){const o=n[i];r.push(null!=o?Array.from(o):null)}}(mi,this.ngContentSelectors,n),pn=function $_(e,t,n,r,i,o){const s=fr(),c=i[Le],g=Cn(s,i);up(c,i,s,n,null,r);for(let A=0;A<n.length;A++)Hr(hs(i,c,s.directiveStart+A,s),i);cp(c,i,s),g&&Hr(g,i);const _=hs(i,c,s.directiveStart+s.componentOffset,s);if(e[un]=i[un]=_,null!==o)for(const A of o)A(_,t);return Lc(c,s,e),_}(zM,Uo,ca,Zd,xt,[G_]),Wc(At,xt,null)}finally{ko()}return new V_(this.componentType,pn,Hs(mi,xt),xt,mi)}}class V_ extends MC{constructor(t,n,r,i,o){super(),this.location=r,this._rootLView=i,this._tNode=o,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new F_(i),this.componentType=t}setInput(t,n){const r=this._tNode.inputs;let i;if(null!==r&&(i=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;const o=this._rootLView;Gc(o[Le],o,i,t,n),this.previousInputValues.set(t,n),ka(Je(this._tNode.index,o))}}get injector(){return new Yr(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}}function G_(){const e=fr();al(_e()[Le],e)}function Kc(e){let t=function Sp(e){return Object.getPrototypeOf(e.prototype).constructor}(e.type),n=!0;const r=[e];for(;t;){let i;if(Rn(e))i=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new xe(903,!1);i=t.\u0275dir}if(i){if(n){r.push(i);const s=e;s.inputs=ql(e.inputs),s.inputTransforms=ql(e.inputTransforms),s.declaredInputs=ql(e.declaredInputs),s.outputs=ql(e.outputs);const c=i.hostBindings;c&&Q_(e,c);const g=i.viewQuery,_=i.contentQueries;if(g&&K_(e,g),_&&X_(e,_),Te(e.inputs,i.inputs),Te(e.declaredInputs,i.declaredInputs),Te(e.outputs,i.outputs),null!==i.inputTransforms&&(null===s.inputTransforms&&(s.inputTransforms={}),Te(s.inputTransforms,i.inputTransforms)),Rn(i)&&i.data.animation){const A=e.data;A.animation=(A.animation||[]).concat(i.data.animation)}}const o=i.features;if(o)for(let s=0;s<o.length;s++){const c=o[s];c&&c.ngInherit&&c(e),c===Kc&&(n=!1)}}t=Object.getPrototypeOf(t)}!function W_(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){const i=e[r];i.hostVars=t+=i.hostVars,i.hostAttrs=ar(i.hostAttrs,n=ar(n,i.hostAttrs))}}(r)}function ql(e){return e===Gn?{}:e===pt?[]:e}function K_(e,t){const n=e.viewQuery;e.viewQuery=n?(r,i)=>{t(r,i),n(r,i)}:t}function X_(e,t){const n=e.contentQueries;e.contentQueries=n?(r,i,o)=>{t(r,i,o),n(r,i,o)}:t}function Q_(e,t){const n=e.hostBindings;e.hostBindings=n?(r,i)=>{t(r,i),n(r,i)}:t}function Op(e){const t=e.inputConfig,n={};for(const r in t)if(t.hasOwnProperty(r)){const i=t[r];Array.isArray(i)&&i[2]&&(n[r]=i[2])}e.inputTransforms=n}function Zl(e){return!!Xc(e)&&(Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e)}function Xc(e){return null!==e&&("function"==typeof e||"object"==typeof e)}function _o(e,t,n){return e[t]=n}function zr(e,t,n){return!Object.is(e[t],n)&&(e[t]=n,!0)}function ms(e,t,n,r){const i=zr(e,t,n);return zr(e,t+1,r)||i}function Qc(e,t,n,r){const i=_e();return zr(i,ds(),t)&&(Ht(),Co(Bn(),i,e,t,n,r)),Qc}function Qs(e,t,n,r){return zr(e,ds(),n)?t+je(n)+r:Ot}function qs(e,t,n,r,i,o){const c=ms(e,function go(){return ut.lFrame.bindingIndex}(),n,i);return mo(2),c?t+je(n)+r+je(i)+o:Ot}function Hp(e,t,n,r,i,o,s,c){const g=_e(),_=Ht(),A=e+St,x=_.firstCreatePass?function CE(e,t,n,r,i,o,s,c,g){const _=t.consts,A=Ks(t,e,4,s||null,qn(_,c));Bc(t,n,A,qn(_,g)),al(t,A);const x=A.tView=Uc(2,A,r,i,o,t.directiveRegistry,t.pipeRegistry,null,t.schemas,_,null);return null!==t.queries&&(t.queries.template(t,A),x.queries=t.queries.embeddedTView(A)),A}(A,_,g,t,n,r,i,o,s):_.data[A];zi(x,!1);const H=zp(_,g,x,e);sl()&&Al(_,g,H,x),Hr(H,g),Kl(g,g[A]=fp(H,g,H,x)),Bt(x)&&Vc(_,g,x),null!=s&&jc(g,x,c)}let zp=function Gp(e,t,n,r){return qo(!0),t[lt].createComment("")};function td(e,t,n){const r=_e();return zr(r,ds(),t)&&function Pi(e,t,n,r,i,o,s,c){const g=Cn(t,n);let A,_=t.inputs;!c&&null!=_&&(A=_[r])?(Gc(e,n,A,r,i),bi(t)&&function p_(e,t){const n=Je(t,e);16&n[mt]||(n[mt]|=64)}(n,t.index)):3&t.type&&(r=function h_(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(r),i=null!=s?s(i,t.value||"",r):i,o.setProperty(g,r,i))}(Ht(),Bn(),r,e,t,r[lt],n,!1),td}function nd(e,t,n,r,i){const s=i?"class":"style";Gc(e,n,t.inputs[s],s,r)}function nu(e,t,n,r){const i=_e(),o=Ht(),s=St+e,c=i[lt],g=o.firstCreatePass?function ME(e,t,n,r,i,o){const s=t.consts,g=Ks(t,e,2,r,qn(s,i));return Bc(t,n,g,qn(s,o)),null!==g.attrs&&Xl(g,g.attrs,!1),null!==g.mergedAttrs&&Xl(g,g.mergedAttrs,!0),null!==t.queries&&t.queries.elementStart(t,g),g}(s,o,i,t,n,r):o.data[s],_=Wp(o,i,g,c,t,e);i[s]=_;const A=Bt(g);return zi(g,!0),rh(c,_,g),32!=(32&g.flags)&&sl()&&Al(o,i,_,g),0===function xo(){return ut.lFrame.elementDepthCount}()&&Hr(_,i),function wu(){ut.lFrame.elementDepthCount++}(),A&&(Vc(o,i,g),Lc(o,g,i)),null!==r&&jc(i,g),nu}function ru(){let e=fr();ga()?ma():(e=e.parent,zi(e,!1));const t=e;(function Su(e){return ut.skipHydrationRootTNode===e})(t)&&function Iu(){ut.skipHydrationRootTNode=null}(),function bu(){ut.lFrame.elementDepthCount--}();const n=Ht();return n.firstCreatePass&&(al(n,e),Bi(e)&&n.queries.elementEnd(e)),null!=t.classesWithoutHost&&function vv(e){return 0!=(8&e.flags)}(t)&&nd(n,t,_e(),t.classesWithoutHost,!0),null!=t.stylesWithoutHost&&function Dv(e){return 0!=(16&e.flags)}(t)&&nd(n,t,_e(),t.stylesWithoutHost,!1),ru}function rd(e,t,n,r){return nu(e,t,n,r),ru(),rd}let Wp=(e,t,n,r,i,o)=>(qo(!0),Sl(r,i,function nf(){return ut.lFrame.currentNamespace}()));function iu(e,t,n){const r=_e(),i=Ht(),o=e+St,s=i.firstCreatePass?function TE(e,t,n,r,i){const o=t.consts,s=qn(o,r),c=Ks(t,e,8,"ng-container",s);return null!==s&&Xl(c,s,!0),Bc(t,n,c,qn(o,i)),null!==t.queries&&t.queries.elementStart(t,c),c}(o,i,r,t,n):i.data[o];zi(s,!0);const c=Xp(i,r,s,e);return r[o]=c,sl()&&Al(i,r,c,s),Hr(c,r),Bt(s)&&(Vc(i,r,s),Lc(i,s,r)),null!=n&&jc(r,s),iu}function ou(){let e=fr();const t=Ht();return ga()?ma():(e=e.parent,zi(e,!1)),t.firstCreatePass&&(al(t,e),Bi(e)&&t.queries.elementEnd(e)),ou}let Xp=(e,t,n,r)=>(qo(!0),Qu(t[lt],""));function Qp(){return _e()}function id(e){return!!e&&"function"==typeof e.then}function qp(e){return!!e&&"function"==typeof e.subscribe}function od(e,t,n,r){const i=_e(),o=Ht(),s=fr();return function Jp(e,t,n,r,i,o,s){const c=Bt(r),_=e.firstCreatePass&&gp(e),A=t[un],x=pp(t);let H=!0;if(3&r.type||s){const Me=Cn(r,t),He=s?s(Me):Me,et=x.length,Ce=s?xt=>s(Et(xt[r.index])):r.index;let At=null;if(!s&&c&&(At=function NE(e,t,n,r){const i=e.cleanup;if(null!=i)for(let o=0;o<i.length-1;o+=2){const s=i[o];if(s===n&&i[o+1]===r){const c=t[tr],g=i[o+2];return c.length>g?c[g]:null}"string"==typeof s&&(o+=2)}return null}(e,t,i,r.index)),null!==At)(At.__ngLastListenerFn__||At).__ngNextListenerFn__=o,At.__ngLastListenerFn__=o,H=!1;else{o=eg(r,t,A,o,!1);const xt=n.listen(He,i,o);x.push(o,xt),_&&_.push(i,Ce,et,et+1)}}else o=eg(r,t,A,o,!1);const W=r.outputs;let he;if(H&&null!==W&&(he=W[i])){const Me=he.length;if(Me)for(let He=0;He<Me;He+=2){const pn=t[he[He]][he[He+1]].subscribe(o),mi=x.length;x.push(o,pn),_&&_.push(i,r.index,mi,-(mi+1))}}}(o,i,i[lt],s,e,t,r),od}function Yp(e,t,n,r){try{return yn(6,t,n),!1!==n(r)}catch(i){return yp(e,i),!1}finally{yn(7,t,n)}}function eg(e,t,n,r,i){return function o(s){if(s===Function)return r;ka(e.componentOffset>-1?Je(e.index,t):t);let g=Yp(t,n,r,s),_=o.__ngNextListenerFn__;for(;_;)g=Yp(t,n,_,s)&&g,_=_.__ngNextListenerFn__;return i&&!1===g&&s.preventDefault(),g}}function tg(e=1){return function ya(e){return(ut.lFrame.contextLView=function Qo(e,t){for(;e>0;)t=t[Ur],e--;return t}(e,ut.lFrame.contextLView))[un]}(e)}function su(e,t){return e<<17|t<<2}function Yo(e){return e>>17&32767}function ad(e){return 2|e}function ys(e){return(131068&e)>>2}function ld(e,t){return-131069&e|t<<2}function ud(e){return 1|e}function dg(e,t,n,r,i){const o=e[n+1],s=null===t;let c=r?Yo(o):ys(o),g=!1;for(;0!==c&&(!1===g||s);){const A=e[c+1];UE(e[c],t)&&(g=!0,e[c+1]=r?ud(A):ad(A)),c=r?Yo(A):ys(A)}g&&(e[n+1]=r?ad(o):ud(o))}function UE(e,t){return null===e||null==t||(Array.isArray(e)?e[1]:e)===t||!(!Array.isArray(e)||"string"!=typeof t)&&Ps(e,t)>=0}const pr={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function fg(e){return e.substring(pr.key,pr.keyEnd)}function hg(e,t){const n=pr.textEnd;return n===t?-1:(t=pr.keyEnd=function zE(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}(e,pr.key=t,n),ra(e,t,n))}function ra(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function cd(e,t,n){return oo(e,t,n,!1),cd}function dd(e,t){return oo(e,t,null,!0),dd}function vg(e){so(YE,wo,e,!0)}function wo(e,t){for(let n=function $E(e){return function gg(e){pr.key=0,pr.keyEnd=0,pr.value=0,pr.valueEnd=0,pr.textEnd=e.length}(e),hg(e,ra(e,0,pr.textEnd))}(t);n>=0;n=hg(t,n))Ni(e,fg(t),!0)}function oo(e,t,n,r){const i=_e(),o=Ht(),s=mo(2);o.firstUpdatePass&&Cg(o,e,s,r),t!==Ot&&zr(i,s,t)&&Eg(o,o.data[xr()],i,i[lt],e,i[s+1]=function tw(e,t){return null==e||""===e||("string"==typeof t?e+=t:"object"==typeof e&&(e=Ve(Zo(e)))),e}(t,n),r,s)}function so(e,t,n,r){const i=Ht(),o=mo(2);i.firstUpdatePass&&Cg(i,null,o,r);const s=_e();if(n!==Ot&&zr(s,o,n)){const c=i.data[xr()];if(bg(c,r)&&!Dg(i,o)){let g=r?c.classesWithoutHost:c.stylesWithoutHost;null!==g&&(n=nt(g,n||"")),nd(i,c,s,n,r)}else!function ew(e,t,n,r,i,o,s,c){i===Ot&&(i=pt);let g=0,_=0,A=0<i.length?i[0]:null,x=0<o.length?o[0]:null;for(;null!==A||null!==x;){const H=g<i.length?i[g+1]:void 0,W=_<o.length?o[_+1]:void 0;let Me,he=null;A===x?(g+=2,_+=2,H!==W&&(he=x,Me=W)):null===x||null!==A&&A<x?(g+=2,he=A):(_+=2,he=x,Me=W),null!==he&&Eg(e,t,n,r,he,Me,s,c),A=g<i.length?i[g]:null,x=_<o.length?o[_]:null}}(i,c,s,s[lt],s[o+1],s[o+1]=function JE(e,t,n){if(null==n||""===n)return pt;const r=[],i=Zo(n);if(Array.isArray(i))for(let o=0;o<i.length;o++)e(r,i[o],!0);else if("object"==typeof i)for(const o in i)i.hasOwnProperty(o)&&e(r,o,i[o]);else"string"==typeof i&&t(r,i);return r}(e,t,n),r,o)}}function Dg(e,t){return t>=e.expandoStartIndex}function Cg(e,t,n,r){const i=e.data;if(null===i[n+1]){const o=i[xr()],s=Dg(e,n);bg(o,r)&&null===t&&!s&&(t=!1),t=function XE(e,t,n,r){const i=function O(e){const t=ut.lFrame.currentDirectiveIndex;return-1===t?null:e[t]}(e);let o=r?t.residualClasses:t.residualStyles;if(null===i)0===(r?t.classBindings:t.styleBindings)&&(n=Ga(n=fd(null,e,t,n,r),t.attrs,r),o=null);else{const s=t.directiveStylingLast;if(-1===s||e[s]!==i)if(n=fd(i,e,t,n,r),null===o){let g=function QE(e,t,n){const r=n?t.classBindings:t.styleBindings;if(0!==ys(r))return e[Yo(r)]}(e,t,r);void 0!==g&&Array.isArray(g)&&(g=fd(null,e,t,g[1],r),g=Ga(g,t.attrs,r),function qE(e,t,n,r){e[Yo(n?t.classBindings:t.styleBindings)]=r}(e,t,r,g))}else o=function ZE(e,t,n){let r;const i=t.directiveEnd;for(let o=1+t.directiveStylingLast;o<i;o++)r=Ga(r,e[o].hostAttrs,n);return Ga(r,t.attrs,n)}(e,t,r)}return void 0!==o&&(r?t.residualClasses=o:t.residualStyles=o),n}(i,o,t,r),function VE(e,t,n,r,i,o){let s=o?t.classBindings:t.styleBindings,c=Yo(s),g=ys(s);e[r]=n;let A,_=!1;if(Array.isArray(n)?(A=n[1],(null===A||Ps(n,A)>0)&&(_=!0)):A=n,i)if(0!==g){const H=Yo(e[c+1]);e[r+1]=su(H,c),0!==H&&(e[H+1]=ld(e[H+1],r)),e[c+1]=function kE(e,t){return 131071&e|t<<17}(e[c+1],r)}else e[r+1]=su(c,0),0!==c&&(e[c+1]=ld(e[c+1],r)),c=r;else e[r+1]=su(g,0),0===c?c=r:e[g+1]=ld(e[g+1],r),g=r;_&&(e[r+1]=ad(e[r+1])),dg(e,A,r,!0),dg(e,A,r,!1),function jE(e,t,n,r,i){const o=i?e.residualClasses:e.residualStyles;null!=o&&"string"==typeof t&&Ps(o,t)>=0&&(n[r+1]=ud(n[r+1]))}(t,A,e,r,o),s=su(c,g),o?t.classBindings=s:t.styleBindings=s}(i,o,t,n,s,r)}}function fd(e,t,n,r,i){let o=null;const s=n.directiveEnd;let c=n.directiveStylingLast;for(-1===c?c=n.directiveStart:c++;c<s&&(o=t[c],r=Ga(r,o.hostAttrs,i),o!==e);)c++;return null!==e&&(n.directiveStylingLast=c),r}function Ga(e,t,n){const r=n?1:2;let i=-1;if(null!==t)for(let o=0;o<t.length;o++){const s=t[o];"number"==typeof s?i=s:i===r&&(Array.isArray(e)||(e=void 0===e?[]:["",e]),Ni(e,s,!!n||t[++o]))}return void 0===e?null:e}function YE(e,t,n){const r=String(t);""!==r&&!r.includes(" ")&&Ni(e,r,n)}function Eg(e,t,n,r,i,o,s,c){if(!(3&t.type))return;const g=e.data,_=g[c+1],A=function LE(e){return 1==(1&e)}(_)?wg(g,t,n,i,ys(_),s):void 0;au(A)||(au(o)||function FE(e){return 2==(2&e)}(_)&&(o=wg(g,null,n,i,c,s)),function OD(e,t,n,r,i){if(t)i?e.addClass(n,r):e.removeClass(n,r);else{let o=-1===r.indexOf("-")?void 0:bl.DashCase;null==i?e.removeStyle(n,r,o):("string"==typeof i&&i.endsWith("!important")&&(i=i.slice(0,-10),o|=bl.Important),e.setStyle(n,r,i,o))}}(r,s,Ai(xr(),n),i,o))}function wg(e,t,n,r,i,o){const s=null===t;let c;for(;i>0;){const g=e[i],_=Array.isArray(g),A=_?g[1]:g,x=null===A;let H=n[i+1];H===Ot&&(H=x?pt:void 0);let W=x?ju(H,r):A===r?H:void 0;if(_&&!au(W)&&(W=ju(g,r)),au(W)&&(c=W,s))return c;const he=e[i+1];i=s?Yo(he):ys(he)}if(null!==t){let g=o?t.residualClasses:t.residualStyles;null!=g&&(c=ju(g,r))}return c}function au(e){return void 0!==e}function bg(e,t){return 0!=(e.flags&(t?8:16))}function Mg(e,t=""){const n=_e(),r=Ht(),i=e+St,o=r.firstCreatePass?Ks(r,i,1,t,null):r.data[i],s=Sg(r,n,o,t,e);n[i]=s,sl()&&Al(r,n,s,o),zi(o,!1)}let Sg=(e,t,n,r,i)=>(qo(!0),function Ml(e,t){return e.createText(t)}(t[lt],r));function hd(e){return lu("",e,""),hd}function lu(e,t,n){const r=_e(),i=Qs(r,e,t,n);return i!==Ot&&jo(r,xr(),i),lu}function pd(e,t,n,r,i){const o=_e(),s=qs(o,e,t,n,r,i);return s!==Ot&&jo(o,xr(),s),pd}function xg(e,t,n){so(Ni,wo,Qs(_e(),e,t,n),!0)}function Fg(e,t,n,r,i){so(Ni,wo,qs(_e(),e,t,n,r,i),!0)}const vs=void 0;var _w=["en",[["a","p"],["AM","PM"],vs],[["AM","PM"],vs,vs],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],vs,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],vs,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",vs,"{1} 'at' {0}",vs],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",function Cw(e){const n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return 1===n&&0===r?1:5}];let ia={};function gd(e){const t=function Ew(e){return e.toLowerCase().replace(/_/g,"-")}(e);let n=Xg(t);if(n)return n;const r=t.split("-")[0];if(n=Xg(r),n)return n;if("en"===r)return _w;throw new xe(701,!1)}function Kg(e){return gd(e)[oa.PluralCase]}function Xg(e){return e in ia||(ia[e]=Ze.ng&&Ze.ng.common&&Ze.ng.common.locales&&Ze.ng.common.locales[e]),ia[e]}var oa=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(oa||{});const sa="en-US";let Qg=sa;function vd(e,t,n,r,i){if(e=Ee(e),Array.isArray(e))for(let o=0;o<e.length;o++)vd(e[o],t,n,r,i);else{const o=Ht(),s=_e(),c=fr();let g=gs(e)?e:Ee(e.provide);const _=Eh(e),A=1048575&c.providerIndexes,x=c.directiveStart,H=c.providerIndexes>>20;if(gs(e)||!e.multi){const W=new va(_,i,Ws),he=Cd(g,t,i?A:A+H,x);-1===he?(Fu(dl(c,s),o,g),Dd(o,e,t.length),t.push(g),c.directiveStart++,c.directiveEnd++,i&&(c.providerIndexes+=1048576),n.push(W),s.push(W)):(n[he]=W,s[he]=W)}else{const W=Cd(g,t,A+H,x),he=Cd(g,t,A,A+H),He=he>=0&&n[he];if(i&&!He||!i&&!(W>=0&&n[W])){Fu(dl(c,s),o,g);const et=function Cb(e,t,n,r,i){const o=new va(e,n,Ws);return o.multi=[],o.index=t,o.componentProviders=0,Cm(o,i,r&&!n),o}(i?Db:vb,n.length,i,r,_);!i&&He&&(n[he].providerFactory=et),Dd(o,e,t.length,0),t.push(g),c.directiveStart++,c.directiveEnd++,i&&(c.providerIndexes+=1048576),n.push(et),s.push(et)}else Dd(o,e,W>-1?W:he,Cm(n[i?he:W],_,!i&&r));!i&&r&&He&&n[he].componentProviders++}}}function Dd(e,t,n,r){const i=gs(t),o=function sC(e){return!!e.useClass}(t);if(i||o){const g=(o?Ee(t.useClass):t).prototype.ngOnDestroy;if(g){const _=e.destroyHooks||(e.destroyHooks=[]);if(!i&&t.multi){const A=_.indexOf(n);-1===A?_.push(n,[r,g]):_[A+1].push(r,g)}else _.push(n,g)}}}function Cm(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Cd(e,t,n,r){for(let i=n;i<r;i++)if(t[i]===e)return i;return-1}function vb(e,t,n,r){return _d(this.multi,[])}function Db(e,t,n,r){const i=this.multi;let o;if(this.providerFactory){const s=this.providerFactory.componentProviders,c=hs(n,n[Le],this.providerFactory.index,r);o=c.slice(0,s),_d(i,o);for(let g=s;g<c.length;g++)o.push(c[g])}else o=[],_d(i,o);return o}function _d(e,t){for(let n=0;n<e.length;n++)t.push((0,e[n])());return t}function _m(e,t=[]){return n=>{n.providersResolver=(r,i)=>function yb(e,t,n){const r=Ht();if(r.firstCreatePass){const i=Rn(e);vd(n,r.data,r.blueprint,i,!0),vd(t,r.data,r.blueprint,i,!1)}}(r,i?i(e):e,t)}}class Ds{}class Em{}function _b(e,t){return new Ed(e,t??null,[])}class Ed extends Ds{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new bp(this);const i=bn(t);this._bootstrapComponents=Vo(i.bootstrap),this._r3Injector=kh(t,n,[{provide:Ds,useValue:this},{provide:$l,useValue:this.componentFactoryResolver},...r],Ve(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){const t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}}class wd extends Em{constructor(t){super(),this.moduleType=t}create(t){return new Ed(this.moduleType,t,[])}}class wm extends Ds{constructor(t){super(),this.componentFactoryResolver=new bp(this),this.instance=null;const n=new js([...t.providers,{provide:Ds,useValue:this},{provide:$l,useValue:this.componentFactoryResolver}],t.parent||kl(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}}function bm(e,t,n=null){return new wm({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}let wb=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){const r=vh(0,n.type),i=r.length>0?bm([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,i)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(const n of this.cachedInjectors.values())null!==n&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=Ut({token:e,providedIn:"environment",factory:()=>new e(Oe(vo))})}}return e})();function Mm(e){e.getStandaloneInjector=t=>t.get(wb).getOrCreateStandaloneInjector(e)}function Pm(e,t,n){const r=$r()+e,i=_e();return i[r]===Ot?_o(i,r,n?t.call(n):t()):function Ba(e,t){return e[t]}(i,r)}function Rm(e,t,n,r){return Lm(_e(),$r(),e,t,n,r)}function xm(e,t,n,r,i){return Vm(_e(),$r(),e,t,n,r,i)}function Fm(e,t,n,r,i,o){return jm(_e(),$r(),e,t,n,r,i,o)}function km(e,t,n,r,i,o,s){return function Um(e,t,n,r,i,o,s,c,g){const _=t+n;return function Ki(e,t,n,r,i,o){const s=ms(e,t,n,r);return ms(e,t+2,i,o)||s}(e,_,i,o,s,c)?_o(e,_+4,g?r.call(g,i,o,s,c):r(i,o,s,c)):Za(e,_+4)}(_e(),$r(),e,t,n,r,i,o,s)}function Za(e,t){const n=e[t];return n===Ot?void 0:n}function Lm(e,t,n,r,i,o){const s=t+n;return zr(e,s,i)?_o(e,s+1,o?r.call(o,i):r(i)):Za(e,s+1)}function Vm(e,t,n,r,i,o,s){const c=t+n;return ms(e,c,i,o)?_o(e,c+2,s?r.call(s,i,o):r(i,o)):Za(e,c+2)}function jm(e,t,n,r,i,o,s,c){const g=t+n;return function Jl(e,t,n,r,i){const o=ms(e,t,n,r);return zr(e,t+2,i)||o}(e,g,i,o,s)?_o(e,g+3,c?r.call(c,i,o,s):r(i,o,s)):Za(e,g+3)}function $m(e,t){const n=Ht();let r;const i=e+St;n.firstCreatePass?(r=function Lb(e,t){if(t)for(let n=t.length-1;n>=0;n--){const r=t[n];if(e===r.name)return r}}(t,n.pipeRegistry),n.data[i]=r,r.onDestroy&&(n.destroyHooks??=[]).push(i,r.onDestroy)):r=n.data[i];const o=r.factory||(r.factory=no(r.type)),c=An(Ws);try{const g=cl(!1),_=o();return cl(g),function wE(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}(n,_e(),i,_),_}finally{An(c)}}function Hm(e,t,n){const r=e+St,i=_e(),o=Xe(i,r);return Ja(i,r)?Lm(i,$r(),t,o.transform,n,o):o.transform(n)}function zm(e,t,n,r){const i=e+St,o=_e(),s=Xe(o,i);return Ja(o,i)?Vm(o,$r(),t,s.transform,n,r,s):s.transform(n,r)}function Gm(e,t,n,r,i){const o=e+St,s=_e(),c=Xe(s,o);return Ja(s,o)?jm(s,$r(),t,c.transform,n,r,i,c):c.transform(n,r,i)}function Ja(e,t){return e[Le].data[t].pure}function Ub(){return this._results[Symbol.iterator]()}class Md{get changes(){return this._changes||(this._changes=new Do)}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._results=[],this._changesDetected=!1,this._changes=null,this.length=0,this.first=void 0,this.last=void 0;const n=Md.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=Ub)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){const r=this;r.dirty=!1;const i=function Wi(e){return e.flat(Number.POSITIVE_INFINITY)}(t);(this._changesDetected=!function Rv(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let i=e[r],o=t[r];if(n&&(i=n(i),o=n(o)),o!==i)return!1}return!0}(r._results,i,n))&&(r._results=i,r.length=i.length,r.last=i[this.length-1],r.first=i[0])}notifyOnChanges(){this._changes&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}setDirty(){this.dirty=!0}destroy(){this.changes.complete(),this.changes.unsubscribe()}}function $b(e,t,n,r=!0){const i=t[Le];if(function ED(e,t,n,r){const i=jn+r,o=n.length;r>0&&(n[i-1][Kn]=t),r<o-jn?(t[Kn]=n[i],Cf(n,jn+r,t)):(n.push(t),t[Kn]=null),t[nn]=n;const s=t[Vn];null!==s&&n!==s&&function wD(e,t){const n=e[Nn];t[an]!==t[nn][nn][an]&&(e[eo]=!0),null===n?e[Nn]=[t]:n.push(t)}(s,t);const c=t[Sn];null!==c&&c.insertView(e),t[mt]|=128}(i,t,e,n),r){const o=ec(n,e),s=t[lt],c=Tl(s,e[gn]);null!==c&&function DD(e,t,n,r,i,o){r[ln]=i,r[Ln]=t,Ta(e,r,n,1,i,o)}(i,e[Ln],s,t,c,o)}}let Ya=(()=>{class e{static{this.__NG_ELEMENT_ID__=Gb}}return e})();const Hb=Ya,zb=class extends Hb{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){const i=function Bb(e,t,n,r){const i=t.tView,c=Wl(e,i,n,4096&e[mt]?4096:16,null,t,null,null,null,r?.injector??null,r?.hydrationInfo??null);c[Vn]=e[t.index];const _=e[Sn];return null!==_&&(c[Sn]=_.createEmbeddedView(i)),Wc(i,c,n),c}(this._declarationLView,this._declarationTContainer,t,{injector:n,hydrationInfo:r});return new ja(i)}};function Gb(){return hu(fr(),_e())}function hu(e,t){return 4&e.type?new zb(t,e,Hs(e,t)):null}let gu=(()=>{class e{static{this.__NG_ELEMENT_ID__=Zb}}return e})();function Zb(){return Jm(fr(),_e())}const Jb=gu,qm=class extends Jb{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Hs(this._hostTNode,this._hostLView)}get injector(){return new Yr(this._hostTNode,this._hostLView)}get parentInjector(){const t=fl(this._hostTNode,this._hostLView);if(Pu(t)){const n=Ca(t,this._hostLView),r=Da(t);return new Yr(n[Le].data[r+8],n)}return new Yr(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){const n=Zm(this._lContainer);return null!==n&&n[t]||null}get length(){return this._lContainer.length-jn}createEmbeddedView(t,n,r){let i,o;"number"==typeof r?i=r:null!=r&&(i=r.index,o=r.injector);const c=t.createEmbeddedViewImpl(n||{},o,null);return this.insertImpl(c,i,false),c}createComponent(t,n,r,i,o){const s=t&&!function Ea(e){return"function"==typeof e}(t);let c;if(s)c=n;else{const Me=n||{};c=Me.index,r=Me.injector,i=Me.projectableNodes,o=Me.environmentInjector||Me.ngModuleRef}const g=s?t:new Ua(Mt(t)),_=r||this.parentInjector;if(!o&&null==g.ngModule){const He=(s?_:this.parentInjector).get(vo,null);He&&(o=He)}Mt(g.componentType??{});const W=g.create(_,i,null,o);return this.insertImpl(W.hostView,c,false),W}insert(t,n){return this.insertImpl(t,n,!1)}insertImpl(t,n,r){const i=t._lView;if(function xn(e){return Dn(e[nn])}(i)){const g=this.indexOf(t);if(-1!==g)this.detach(g);else{const _=i[nn],A=new qm(_,_[Ln],_[nn]);A.detach(A.indexOf(t))}}const s=this._adjustIndex(n),c=this._lContainer;return $b(c,i,s,!r),t.attachToViewContainerRef(),Cf(Sd(c),s,t),t}move(t,n){return this.insert(t,n)}indexOf(t){const n=Zm(this._lContainer);return null!==n?n.indexOf(t):-1}remove(t){const n=this._adjustIndex(t,-1),r=Il(this._lContainer,n);r&&(pl(Sd(this._lContainer),n),qu(r[Le],r))}detach(t){const n=this._adjustIndex(t,-1),r=Il(this._lContainer,n);return r&&null!=pl(Sd(this._lContainer),n)?new ja(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Zm(e){return e[8]}function Sd(e){return e[8]||(e[8]=[])}function Jm(e,t){let n;const r=t[e.index];return Dn(r)?n=r:(n=fp(r,t,null,e),t[e.index]=n,Kl(t,n)),Ym(n,t,e,r),new qm(n,e,t)}let Ym=function ey(e,t,n,r){if(e[gn])return;let i;i=8&n.type?Et(r):function Yb(e,t){const n=e[lt],r=n.createComment(""),i=Cn(t,e);return ps(n,Tl(n,i),r,function ID(e,t){return e.nextSibling(t)}(n,i),!1),r}(t,n),e[gn]=i};class Id{constructor(t){this.queryList=t,this.matches=null}clone(){return new Id(this.queryList)}setDirty(){this.queryList.setDirty()}}class Td{constructor(t=[]){this.queries=t}createEmbeddedView(t){const n=t.queries;if(null!==n){const r=null!==t.contentQueries?t.contentQueries[0]:n.length,i=[];for(let o=0;o<r;o++){const s=n.getByIndex(o);i.push(this.queries[s.indexInDeclarationView].clone())}return new Td(i)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)null!==uy(t,n).matches&&this.queries[n].setDirty()}}class ty{constructor(t,n,r=null){this.predicate=t,this.flags=n,this.read=r}}class Ad{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){const i=null!==n?n.length:0,o=this.getByIndex(r).embeddedTView(t,i);o&&(o.indexInDeclarationView=r,null!==n?n.push(o):n=[o])}return null!==n?new Ad(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}}class Od{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new Od(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&1!=(1&this.metadata.flags)){const n=this._declarationNodeIndex;let r=t.parent;for(;null!==r&&8&r.type&&r.index!==n;)r=r.parent;return n===(null!==r?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){const r=this.metadata.predicate;if(Array.isArray(r))for(let i=0;i<r.length;i++){const o=r[i];this.matchTNodeWithReadOption(t,n,n0(n,o)),this.matchTNodeWithReadOption(t,n,hl(n,t,o,!1,!1))}else r===Ya?4&n.type&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,hl(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(null!==r){const i=this.metadata.read;if(null!==i)if(i===Fa||i===gu||i===Ya&&4&n.type)this.addMatch(n.index,-2);else{const o=hl(n,t,i,!1,!1);null!==o&&this.addMatch(n.index,o)}else this.addMatch(n.index,r)}}addMatch(t,n){null===this.matches?this.matches=[t,n]:this.matches.push(t,n)}}function n0(e,t){const n=e.localNames;if(null!==n)for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1];return null}function o0(e,t,n,r){return-1===n?function r0(e,t){return 11&e.type?Hs(e,t):4&e.type?hu(e,t):null}(t,e):-2===n?function s0(e,t,n){return n===Fa?Hs(t,e):n===Ya?hu(t,e):n===gu?Jm(t,e):void 0}(e,t,r):hs(e,e[Le],n,t)}function ny(e,t,n,r){const i=t[Sn].queries[r];if(null===i.matches){const o=e.data,s=n.matches,c=[];for(let g=0;g<s.length;g+=2){const _=s[g];c.push(_<0?null:o0(t,o[_],s[g+1],n.metadata.read))}i.matches=c}return i.matches}function Nd(e,t,n,r){const i=e.queries.getByIndex(n),o=i.matches;if(null!==o){const s=ny(e,t,i,n);for(let c=0;c<o.length;c+=2){const g=o[c];if(g>0)r.push(s[c/2]);else{const _=o[c+1],A=t[-g];for(let x=jn;x<A.length;x++){const H=A[x];H[Vn]===H[nn]&&Nd(H[Le],H,_,r)}if(null!==A[Nn]){const x=A[Nn];for(let H=0;H<x.length;H++){const W=x[H];Nd(W[Le],W,_,r)}}}}}return r}function ry(e){const t=_e(),n=Ht(),r=F();te(r+1);const i=uy(n,r);if(e.dirty&&function Dt(e){return 4==(4&e[mt])}(t)===(2==(2&i.metadata.flags))){if(null===i.matches)e.reset([]);else{const o=i.crossesNgTemplate?Nd(n,t,r,[]):ny(n,t,i,r);e.reset(o,AC),e.notifyOnChanges()}return!0}return!1}function iy(e,t,n){const r=Ht();r.firstCreatePass&&(ly(r,new ty(e,t,n),-1),2==(2&t)&&(r.staticViewQueries=!0)),ay(r,_e(),t)}function oy(e,t,n,r){const i=Ht();if(i.firstCreatePass){const o=fr();ly(i,new ty(t,n,r),o.index),function l0(e,t){const n=e.contentQueries||(e.contentQueries=[]);t!==(n.length?n[n.length-1]:-1)&&n.push(e.queries.length-1,t)}(i,e),2==(2&n)&&(i.staticContentQueries=!0)}ay(i,_e(),n)}function sy(){return function a0(e,t){return e[Sn].queries[t].queryList}(_e(),F())}function ay(e,t,n){const r=new Md(4==(4&n));(function c_(e,t,n,r){const i=pp(t);i.push(n),e.firstCreatePass&&gp(e).push(r,i.length-1)})(e,t,r,r.destroy),null===t[Sn]&&(t[Sn]=new Td),t[Sn].queries.push(new Id(r))}function ly(e,t,n){null===e.queries&&(e.queries=new Ad),e.queries.track(new Od(t,n))}function uy(e,t){return e.queries.getByIndex(t)}function Pd(e){return!!bn(e)}const Sy=new ot("Application Initializer");let Ld=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=ke(Sy,{optional:!0})??[]}runInitializers(){if(this.initialized)return;const n=[];for(const i of this.appInits){const o=i();if(id(o))n.push(o);else if(qp(o)){const s=new Promise((c,g)=>{o.subscribe({complete:c,error:g})});n.push(s)}}const r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(i=>{this.reject(i)}),0===n.length&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Ut({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Iy=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Ut({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();const yu=new ot("LocaleId",{providedIn:"root",factory:()=>ke(yu,ze.Optional|ze.SkipSelf)||function O0(){return typeof $localize<"u"&&$localize.locale||sa}()}),N0=new ot("DefaultCurrencyCode",{providedIn:"root",factory:()=>"USD"});let Ty=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new pe.t(!1)}add(){this.hasPendingTasks.next(!0);const n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),0===this.pendingTasks.size&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks.next(!1)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Ut({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();class R0{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}}let x0=(()=>{class e{compileModuleSync(n){return new wd(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){const r=this.compileModuleSync(n),o=Vo(bn(n).declarations).reduce((s,c)=>{const g=Mt(c);return g&&s.push(new Ua(g)),s},[]);return new R0(r,o)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Ut({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const Py=new ot(""),Ry=new ot("");let Ud,nM=(()=>{class e{constructor(n,r,i){this._ngZone=n,this.registry=r,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,Ud||(function rM(e){Ud=e}(i),i.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._didWork=!0,this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{wr.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;0!==this._callbacks.length;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb(this._didWork)}this._didWork=!1});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>!r.updateCb||!r.updateCb(n)||(clearTimeout(r.timeoutId),!1)),this._didWork=!0}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,i){let o=-1;r&&r>0&&(o=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==o),n(this._didWork,this.getPendingTasks())},r)),this._callbacks.push({doneCb:n,timeoutId:o,updateCb:i})}whenStable(n,r,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,i),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,i){return[]}static{this.\u0275fac=function(r){return new(r||e)(Oe(wr),Oe(xy),Oe(Ry))}}static{this.\u0275prov=Ut({token:e,factory:e.\u0275fac})}}return e})(),xy=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return Ud?.findTestabilityInTree(this,n,r)??null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Ut({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})(),es=null;const Fy=new ot("AllowMultipleToken"),Bd=new ot("PlatformDestroyListeners"),$d=new ot("appBootstrapListener");class sM{constructor(t,n){this.name=t,this.token=n}}function Vy(e,t,n=[]){const r=`Platform: ${t}`,i=new ot(r);return(o=[])=>{let s=Hd();if(!s||s.injector.get(Fy,!1)){const c=[...n,...o,{provide:i,useValue:!0}];e?e(c):function aM(e){if(es&&!es.get(Fy,!1))throw new xe(400,!1);(function ky(){!function ve(e){k=e}(()=>{throw new xe(600,!1)})})(),es=e;const t=e.get(Uy);(function Ly(e){e.get(bh,null)?.forEach(n=>n())})(e)}(function jy(e=[],t){return io.create({name:t,providers:[{provide:pc,useValue:"platform"},{provide:Bd,useValue:new Set([()=>es=null])},...e]})}(c,r))}return function uM(e){const t=Hd();if(!t)throw new xe(401,!1);return t}()}}function Hd(){return es?.get(Uy)??null}let Uy=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){const i=function cM(e="zone.js",t){return"noop"===e?new WC:"zone.js"===e?new wr(t):e}(r?.ngZone,function By(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing}));return i.run(()=>{const o=function Eb(e,t,n){return new Ed(e,t,n)}(n.moduleType,this.injector,function Wy(e){return[{provide:wr,useFactory:e},{provide:Pa,multi:!0,useFactory:()=>{const t=ke(fM,{optional:!0});return()=>t.initialize()}},{provide:Gy,useFactory:dM},{provide:Bh,useFactory:$h}]}(()=>i)),s=o.injector.get(Jo,null);return i.runOutsideAngular(()=>{const c=i.onError.subscribe({next:g=>{s.handleError(g)}});o.onDestroy(()=>{Du(this._modules,o),c.unsubscribe()})}),function $y(e,t,n){try{const r=n();return id(r)?r.catch(i=>{throw t.runOutsideAngular(()=>e.handleError(i)),i}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}(s,i,()=>{const c=o.injector.get(Ld);return c.runInitializers(),c.donePromise.then(()=>(function qg(e){Gt(e,"Expected localeId to be defined"),"string"==typeof e&&(Qg=e.toLowerCase().replace(/_/g,"-"))}(o.injector.get(yu,sa)||sa),this._moduleDoBootstrap(o),o))})})}bootstrapModule(n,r=[]){const i=Hy({},r);return function iM(e,t,n){const r=new wd(n);return Promise.resolve(r)}(0,0,n).then(o=>this.bootstrapModuleFactory(o,i))}_moduleDoBootstrap(n){const r=n.injector.get(ua);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(i=>r.bootstrap(i));else{if(!n.instance.ngDoBootstrap)throw new xe(-403,!1);n.instance.ngDoBootstrap(r)}this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new xe(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());const n=this._injector.get(Bd,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static{this.\u0275fac=function(r){return new(r||e)(Oe(io))}}static{this.\u0275prov=Ut({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();function Hy(e,t){return Array.isArray(t)?t.reduce(Hy,e):{...e,...t}}let ua=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=ke(Gy),this.zoneIsStable=ke(Bh),this.componentTypes=[],this.components=[],this.isStable=ke(Ty).hasPendingTasks.pipe((0,Lt.n)(n=>n?(0,Z.of)(!1):this.zoneIsStable),function jt(e,t=Tt.D){return e=e??Qt,(0,be.N)((n,r)=>{let i,o=!0;n.subscribe((0,Vt._)(r,s=>{const c=t(s);(o||!e(i,c))&&(o=!1,i=c,r.next(s))}))})}(),Ne()),this._injector=ke(vo)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,r){const i=n instanceof Th;if(!this._injector.get(Ld).done)throw!i&&Or(n),new xe(405,!1);let s;s=i?n:this._injector.get($l).resolveComponentFactory(n),this.componentTypes.push(s.componentType);const c=function oM(e){return e.isBoundToModule}(s)?void 0:this._injector.get(Ds),_=s.create(io.NULL,[],r||s.selector,c),A=_.location.nativeElement,x=_.injector.get(Py,null);return x?.registerApplication(A),_.onDestroy(()=>{this.detachView(_.hostView),Du(this.components,_),x?.unregisterApplication(A)}),this._loadComponent(_),_}tick(){if(this._runningTick)throw new xe(101,!1);try{this._runningTick=!0;for(let n of this._views)n.detectChanges()}catch(n){this.internalErrorHandler(n)}finally{this._runningTick=!1}}attachView(n){const r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){const r=n;Du(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);const r=this._injector.get($d,[]);r.push(...this._bootstrapListeners),r.forEach(i=>i(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Du(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new xe(406,!1);const n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Ut({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Du(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}const Gy=new ot("",{providedIn:"root",factory:()=>ke(Jo).handleError.bind(void 0)});function dM(){const e=ke(wr),t=ke(Jo);return n=>e.runOutsideAngular(()=>t.handleError(n))}let fM=(()=>{class e{constructor(){this.zone=ke(wr),this.applicationRef=ke(ua)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Ut({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();let pM=(()=>{class e{static{this.__NG_ELEMENT_ID__=gM}}return e})();function gM(e){return function mM(e,t,n){if(bi(e)&&!n){const r=Je(e.index,t);return new ja(r,r)}return 47&e.type?new ja(t[an],t):null}(fr(),_e(),16==(16&e))}class Zy{constructor(){}supports(t){return Zl(t)}create(t){return new _M(t)}}const CM=(e,t)=>t;class _M{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||CM}forEachItem(t){let n;for(n=this._itHead;null!==n;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,i=0,o=null;for(;n||r;){const s=!r||n&&n.currentIndex<Yy(r,i,o)?n:r,c=Yy(s,i,o),g=s.currentIndex;if(s===r)i--,r=r._nextRemoved;else if(n=n._next,null==s.previousIndex)i++;else{o||(o=[]);const _=c-i,A=g-i;if(_!=A){for(let H=0;H<_;H++){const W=H<o.length?o[H]:o[H]=0,he=W+H;A<=he&&he<_&&(o[H]=W+1)}o[s.previousIndex]=A-_}}c!==g&&t(s,c,g)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;null!==n;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;null!==n;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;null!==n;n=n._nextIdentityChange)t(n)}diff(t){if(null==t&&(t=[]),!Zl(t))throw new xe(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let i,o,s,n=this._itHead,r=!1;if(Array.isArray(t)){this.length=t.length;for(let c=0;c<this.length;c++)o=t[c],s=this._trackByFn(c,o),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,o,s,c)),Object.is(n.item,o)||this._addIdentityChange(n,o)):(n=this._mismatch(n,o,s,c),r=!0),n=n._next}else i=0,function nE(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{const n=e[Symbol.iterator]();let r;for(;!(r=n.next()).done;)t(r.value)}}(t,c=>{s=this._trackByFn(i,c),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,c,s,i)),Object.is(n.item,c)||this._addIdentityChange(n,c)):(n=this._mismatch(n,c,s,i),r=!0),n=n._next,i++}),this.length=i;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;null!==t;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;null!==t;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,i){let o;return null===t?o=this._itTail:(o=t._prev,this._remove(t)),null!==(t=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,o,i)):null!==(t=null===this._linkedRecords?null:this._linkedRecords.get(r,i))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,o,i)):t=this._addAfter(new EM(n,r),o,i),t}_verifyReinsertion(t,n,r,i){let o=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null);return null!==o?t=this._reinsertAfter(o,t._prev,i):t.currentIndex!=i&&(t.currentIndex=i,this._addToMoves(t,i)),t}_truncate(t){for(;null!==t;){const n=t._next;this._addToRemovals(this._unlink(t)),t=n}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(t);const i=t._prevRemoved,o=t._nextRemoved;return null===i?this._removalsHead=o:i._nextRemoved=o,null===o?this._removalsTail=i:o._prevRemoved=i,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail=null===this._additionsTail?this._additionsHead=t:this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){const i=null===n?this._itHead:n._next;return t._next=i,t._prev=n,null===i?this._itTail=t:i._prev=t,null===n?this._itHead=t:n._next=t,null===this._linkedRecords&&(this._linkedRecords=new Jy),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){null!==this._linkedRecords&&this._linkedRecords.remove(t);const n=t._prev,r=t._next;return null===n?this._itHead=r:n._next=r,null===r?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail=null===this._movesTail?this._movesHead=t:this._movesTail._nextMoved=t),t}_addToRemovals(t){return null===this._unlinkedRecords&&(this._unlinkedRecords=new Jy),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=t:this._identityChangesTail._nextIdentityChange=t,t}}class EM{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class wM{constructor(){this._head=null,this._tail=null}add(t){null===this._head?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;null!==r;r=r._nextDup)if((null===n||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){const n=t._prevDup,r=t._nextDup;return null===n?this._head=r:n._nextDup=r,null===r?this._tail=n:r._prevDup=n,null===this._head}}class Jy{constructor(){this.map=new Map}put(t){const n=t.trackById;let r=this.map.get(n);r||(r=new wM,this.map.set(n,r)),r.add(t)}get(t,n){const i=this.map.get(t);return i?i.get(t,n):null}remove(t){const n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function Yy(e,t,n){const r=e.previousIndex;if(null===r)return r;let i=0;return n&&r<n.length&&(i=n[r]),r+t+i}class ev{constructor(){}supports(t){return t instanceof Map||Xc(t)}create(){return new bM}}class bM{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead}forEachItem(t){let n;for(n=this._mapHead;null!==n;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;null!==n;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;null!==n;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}diff(t){if(t){if(!(t instanceof Map||Xc(t)))throw new xe(900,!1)}else t=new Map;return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,i)=>{if(n&&n.key===i)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{const o=this._getOrCreateRecordForKey(i,r);n=this._insertBeforeOrAppend(n,o)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;null!==r;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){const r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){const i=this._records.get(t);this._maybeAddToChanges(i,n);const o=i._prev,s=i._next;return o&&(o._next=s),s&&(s._prev=o),i._next=null,i._prev=null,i}const r=new MM(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;null!==t;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;null!=t;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){null===this._additionsHead?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){null===this._changesHead?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}}class MM{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}}function tv(){return new Xd([new Zy])}let Xd=(()=>{class e{static{this.\u0275prov=Ut({token:e,providedIn:"root",factory:tv})}constructor(n){this.factories=n}static create(n,r){if(null!=r){const i=r.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||tv()),deps:[[e,new yl,new ml]]}}find(n){const r=this.factories.find(i=>i.supports(n));if(null!=r)return r;throw new xe(901,!1)}}return e})();function nv(){return new Qd([new ev])}let Qd=(()=>{class e{static{this.\u0275prov=Ut({token:e,providedIn:"root",factory:nv})}constructor(n){this.factories=n}static create(n,r){if(r){const i=r.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||nv()),deps:[[e,new yl,new ml]]}}find(n){const r=this.factories.find(i=>i.supports(n));if(r)return r;throw new xe(901,!1)}}return e})();const TM=Vy(null,"core",[]);let AM=(()=>{class e{constructor(n){}static{this.\u0275fac=function(r){return new(r||e)(Oe(ua))}}static{this.\u0275mod=ai({type:e})}static{this.\u0275inj=ht({})}}return e})();function UM(e){return"boolean"==typeof e?e:null!=e&&"false"!==e}function $M(e){const t=Mt(e);if(!t)return null;const n=new Ua(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}},4341:(tt,ye,R)=>{R.d(ye,{Zm:()=>Tt,me:()=>De,ok:()=>Dn,JD:()=>Qr,j4:()=>Mt,YN:()=>Rn,zX:()=>Ei,VZ:()=>an,BC:()=>ht,cb:()=>Ct,vS:()=>oi,xH:()=>mt,Q0:()=>Zi,Fm:()=>jr,X1:()=>co,wz:()=>Le,k0:()=>Ee,qT:()=>qi,y7:()=>un});var d=R(540),w=R(177),de=R(6648),ae=R(1985),X=R(3073),oe=R(8750),ie=R(9326),ge=R(4360),le=R(6450),pe=R(8496),ne=R(6354);let be=(()=>{class b{constructor(v,P){this._renderer=v,this._elementRef=P,this.onChange=B=>{},this.onTouched=()=>{}}setProperty(v,P){this._renderer.setProperty(this._elementRef.nativeElement,v,P)}registerOnTouched(v){this.onTouched=v}registerOnChange(v){this.onChange=v}setDisabledState(v){this.setProperty("disabled",v)}static{this.\u0275fac=function(P){return new(P||b)(d.rXU(d.sFG),d.rXU(d.aKT))}}static{this.\u0275dir=d.FsC({type:b})}}return b})(),Ne=(()=>{class b extends be{static{this.\u0275fac=function(){let v;return function(B){return(v||(v=d.xGo(b)))(B||b)}}()}static{this.\u0275dir=d.FsC({type:b,features:[d.Vt3]})}}return b})();const Ge=new d.nKC("NgValueAccessor"),Lt={provide:Ge,useExisting:(0,d.Rfq)(()=>Tt),multi:!0};let Tt=(()=>{class b extends Ne{writeValue(v){this.setProperty("checked",v)}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=d.xGo(b)))(B||b)}}()}static{this.\u0275dir=d.FsC({type:b,selectors:[["input","type","checkbox","formControlName",""],["input","type","checkbox","formControl",""],["input","type","checkbox","ngModel",""]],hostBindings:function(P,B){1&P&&d.bIt("change",function(vt){return B.onChange(vt.target.checked)})("blur",function(){return B.onTouched()})},features:[d.Jv_([Lt]),d.Vt3]})}}return b})();const Vt={provide:Ge,useExisting:(0,d.Rfq)(()=>De),multi:!0},Qt=new d.nKC("CompositionEventMode");let De=(()=>{class b extends be{constructor(v,P,B){super(v,P),this._compositionMode=B,this._composing=!1,null==this._compositionMode&&(this._compositionMode=!function jt(){const b=(0,w.QT)()?(0,w.QT)().getUserAgent():"";return/android (\d+)/.test(b.toLowerCase())}())}writeValue(v){this.setProperty("value",v??"")}_handleInput(v){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(v)}_compositionStart(){this._composing=!0}_compositionEnd(v){this._composing=!1,this._compositionMode&&this.onChange(v)}static{this.\u0275fac=function(P){return new(P||b)(d.rXU(d.sFG),d.rXU(d.aKT),d.rXU(Qt,8))}}static{this.\u0275dir=d.FsC({type:b,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(P,B){1&P&&d.bIt("input",function(vt){return B._handleInput(vt.target.value)})("blur",function(){return B.onTouched()})("compositionstart",function(){return B._compositionStart()})("compositionend",function(vt){return B._compositionEnd(vt.target.value)})},features:[d.Jv_([Vt]),d.Vt3]})}}return b})();function Te(b){return null==b||("string"==typeof b||Array.isArray(b))&&0===b.length}function Ve(b){return null!=b&&"number"==typeof b.length}const nt=new d.nKC("NgValidators"),Re=new d.nKC("NgAsyncValidators"),Ae=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;class Ee{static min(S){return rt(S)}static max(S){return Ft(S)}static required(S){return function _n(b){return Te(b.value)?{required:!0}:null}(S)}static requiredTrue(S){return function $n(b){return!0===b.value?null:{required:!0}}(S)}static email(S){return function xe(b){return Te(b.value)||Ae.test(b.value)?null:{email:!0}}(S)}static minLength(S){return function en(b){return S=>Te(S.value)||!Ve(S.value)?null:S.value.length<b?{minlength:{requiredLength:b,actualLength:S.value.length}}:null}(S)}static maxLength(S){return function je(b){return S=>Ve(S.value)&&S.value.length>b?{maxlength:{requiredLength:b,actualLength:S.value.length}}:null}(S)}static pattern(S){return function ft(b){if(!b)return Fn;let S,v;return"string"==typeof b?(v="","^"!==b.charAt(0)&&(v+="^"),v+=b,"$"!==b.charAt(b.length-1)&&(v+="$"),S=new RegExp(v)):(v=b.toString(),S=b),P=>{if(Te(P.value))return null;const B=P.value;return S.test(B)?null:{pattern:{requiredPattern:v,actualValue:B}}}}(S)}static nullValidator(S){return null}static compose(S){return we(S)}static composeAsync(S){return ct(S)}}function rt(b){return S=>{if(Te(S.value)||Te(b))return null;const v=parseFloat(S.value);return!isNaN(v)&&v<b?{min:{min:b,actual:S.value}}:null}}function Ft(b){return S=>{if(Te(S.value)||Te(b))return null;const v=parseFloat(S.value);return!isNaN(v)&&v>b?{max:{max:b,actual:S.value}}:null}}function Fn(b){return null}function Fr(b){return null!=b}function Q(b){return(0,d.jNT)(b)?(0,de.H)(b):b}function Y(b){let S={};return b.forEach(v=>{S=null!=v?{...S,...v}:S}),0===Object.keys(S).length?null:S}function J(b,S){return S.map(v=>v(b))}function Ie(b){return b.map(S=>function se(b){return!b.validate}(S)?S:v=>S.validate(v))}function we(b){if(!b)return null;const S=b.filter(Fr);return 0==S.length?null:function(v){return Y(J(v,S))}}function We(b){return null!=b?we(Ie(b)):null}function ct(b){if(!b)return null;const S=b.filter(Fr);return 0==S.length?null:function(v){return function Z(...b){const S=(0,ie.ms)(b),{args:v,keys:P}=(0,X.D)(b),B=new ae.c(Ke=>{const{length:vt}=v;if(!vt)return void Ke.complete();const Qn=new Array(vt);let ui=vt,Mi=vt;for(let $i=0;$i<vt;$i++){let to=!1;(0,oe.Tg)(v[$i]).subscribe((0,ge._)(Ke,Si=>{to||(to=!0,Mi--),Qn[$i]=Si},()=>ui--,void 0,()=>{(!ui||!to)&&(Mi||Ke.next(P?(0,pe.e)(P,Qn):Qn),Ke.complete())}))}});return S?B.pipe((0,le.I)(S)):B}(J(v,S).map(Q)).pipe((0,ne.T)(Y))}}function zt(b){return null!=b?ct(Ie(b)):null}function at(b,S){return null===b?[S]:Array.isArray(b)?[...b,S]:[b,S]}function kt(b){return b._rawValidators}function qt(b){return b._rawAsyncValidators}function Jn(b){return b?Array.isArray(b)?b:[b]:[]}function gt(b,S){return Array.isArray(b)?b.includes(S):b===S}function fn(b,S){const v=Jn(S);return Jn(b).forEach(B=>{gt(v,B)||v.push(B)}),v}function Gt(b,S){return Jn(S).filter(v=>!gt(b,v))}class Qe{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(S){this._rawValidators=S||[],this._composedValidatorFn=We(this._rawValidators)}_setAsyncValidators(S){this._rawAsyncValidators=S||[],this._composedAsyncValidatorFn=zt(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(S){this._onDestroyCallbacks.push(S)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(S=>S()),this._onDestroyCallbacks=[]}reset(S=void 0){this.control&&this.control.reset(S)}hasError(S,v){return!!this.control&&this.control.hasError(S,v)}getError(S,v){return this.control?this.control.getError(S,v):null}}class tn extends Qe{get formDirective(){return null}get path(){return null}}class it extends Qe{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}}class Yn{constructor(S){this._cd=S}get isTouched(){return!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return!!this._cd?.submitted}}let ht=(()=>{class b extends Yn{constructor(v){super(v)}static{this.\u0275fac=function(P){return new(P||b)(d.rXU(it,2))}}static{this.\u0275dir=d.FsC({type:b,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(P,B){2&P&&d.AVh("ng-untouched",B.isUntouched)("ng-touched",B.isTouched)("ng-pristine",B.isPristine)("ng-dirty",B.isDirty)("ng-valid",B.isValid)("ng-invalid",B.isInvalid)("ng-pending",B.isPending)},features:[d.Vt3]})}}return b})(),Ct=(()=>{class b extends Yn{constructor(v){super(v)}static{this.\u0275fac=function(P){return new(P||b)(d.rXU(tn,10))}}static{this.\u0275dir=d.FsC({type:b,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(P,B){2&P&&d.AVh("ng-untouched",B.isUntouched)("ng-touched",B.isTouched)("ng-pristine",B.isPristine)("ng-dirty",B.isDirty)("ng-valid",B.isValid)("ng-invalid",B.isInvalid)("ng-pending",B.isPending)("ng-submitted",B.isSubmitted)},features:[d.Vt3]})}}return b})();const ot="VALID",br="INVALID",On="PENDING",mr="DISABLED";function Mr(b){return(z(b)?b.validators:b)||null}function K(b,S){return(z(S)?S.asyncValidators:b)||null}function z(b){return null!=b&&!Array.isArray(b)&&"object"==typeof b}function me(b,S,v){const P=b.controls;if(!(S?Object.keys(P):P).length)throw new d.wOt(1e3,"");if(!P[v])throw new d.wOt(1001,"")}function Fe(b,S,v){b._forEachChild((P,B)=>{if(void 0===v[B])throw new d.wOt(1002,"")})}class Ye{constructor(S,v){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(S),this._assignAsyncValidators(v)}get validator(){return this._composedValidatorFn}set validator(S){this._rawValidators=this._composedValidatorFn=S}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(S){this._rawAsyncValidators=this._composedAsyncValidatorFn=S}get parent(){return this._parent}get valid(){return this.status===ot}get invalid(){return this.status===br}get pending(){return this.status==On}get disabled(){return this.status===mr}get enabled(){return this.status!==mr}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(S){this._assignValidators(S)}setAsyncValidators(S){this._assignAsyncValidators(S)}addValidators(S){this.setValidators(fn(S,this._rawValidators))}addAsyncValidators(S){this.setAsyncValidators(fn(S,this._rawAsyncValidators))}removeValidators(S){this.setValidators(Gt(S,this._rawValidators))}removeAsyncValidators(S){this.setAsyncValidators(Gt(S,this._rawAsyncValidators))}hasValidator(S){return gt(this._rawValidators,S)}hasAsyncValidator(S){return gt(this._rawAsyncValidators,S)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(S={}){this.touched=!0,this._parent&&!S.onlySelf&&this._parent.markAsTouched(S)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(S=>S.markAllAsTouched())}markAsUntouched(S={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(v=>{v.markAsUntouched({onlySelf:!0})}),this._parent&&!S.onlySelf&&this._parent._updateTouched(S)}markAsDirty(S={}){this.pristine=!1,this._parent&&!S.onlySelf&&this._parent.markAsDirty(S)}markAsPristine(S={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(v=>{v.markAsPristine({onlySelf:!0})}),this._parent&&!S.onlySelf&&this._parent._updatePristine(S)}markAsPending(S={}){this.status=On,!1!==S.emitEvent&&this.statusChanges.emit(this.status),this._parent&&!S.onlySelf&&this._parent.markAsPending(S)}disable(S={}){const v=this._parentMarkedDirty(S.onlySelf);this.status=mr,this.errors=null,this._forEachChild(P=>{P.disable({...S,onlySelf:!0})}),this._updateValue(),!1!==S.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors({...S,skipPristineCheck:v}),this._onDisabledChange.forEach(P=>P(!0))}enable(S={}){const v=this._parentMarkedDirty(S.onlySelf);this.status=ot,this._forEachChild(P=>{P.enable({...S,onlySelf:!0})}),this.updateValueAndValidity({onlySelf:!0,emitEvent:S.emitEvent}),this._updateAncestors({...S,skipPristineCheck:v}),this._onDisabledChange.forEach(P=>P(!1))}_updateAncestors(S){this._parent&&!S.onlySelf&&(this._parent.updateValueAndValidity(S),S.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(S){this._parent=S}getRawValue(){return this.value}updateValueAndValidity(S={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===ot||this.status===On)&&this._runAsyncValidator(S.emitEvent)),!1!==S.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!S.onlySelf&&this._parent.updateValueAndValidity(S)}_updateTreeValidity(S={emitEvent:!0}){this._forEachChild(v=>v._updateTreeValidity(S)),this.updateValueAndValidity({onlySelf:!0,emitEvent:S.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?mr:ot}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(S){if(this.asyncValidator){this.status=On,this._hasOwnPendingAsyncValidator=!0;const v=Q(this.asyncValidator(this));this._asyncValidationSubscription=v.subscribe(P=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(P,{emitEvent:S})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(S,v={}){this.errors=S,this._updateControlsErrors(!1!==v.emitEvent)}get(S){let v=S;return null==v||(Array.isArray(v)||(v=v.split(".")),0===v.length)?null:v.reduce((P,B)=>P&&P._find(B),this)}getError(S,v){const P=v?this.get(v):this;return P&&P.errors?P.errors[S]:null}hasError(S,v){return!!this.getError(S,v)}get root(){let S=this;for(;S._parent;)S=S._parent;return S}_updateControlsErrors(S){this.status=this._calculateStatus(),S&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(S)}_initObservables(){this.valueChanges=new d.bkB,this.statusChanges=new d.bkB}_calculateStatus(){return this._allControlsDisabled()?mr:this.errors?br:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(On)?On:this._anyControlsHaveStatus(br)?br:ot}_anyControlsHaveStatus(S){return this._anyControls(v=>v.status===S)}_anyControlsDirty(){return this._anyControls(S=>S.dirty)}_anyControlsTouched(){return this._anyControls(S=>S.touched)}_updatePristine(S={}){this.pristine=!this._anyControlsDirty(),this._parent&&!S.onlySelf&&this._parent._updatePristine(S)}_updateTouched(S={}){this.touched=this._anyControlsTouched(),this._parent&&!S.onlySelf&&this._parent._updateTouched(S)}_registerOnCollectionChange(S){this._onCollectionChange=S}_setUpdateStrategy(S){z(S)&&null!=S.updateOn&&(this._updateOn=S.updateOn)}_parentMarkedDirty(S){return!S&&!(!this._parent||!this._parent.dirty)&&!this._parent._anyControlsDirty()}_find(S){return null}_assignValidators(S){this._rawValidators=Array.isArray(S)?S.slice():S,this._composedValidatorFn=function ce(b){return Array.isArray(b)?We(b):b||null}(this._rawValidators)}_assignAsyncValidators(S){this._rawAsyncValidators=Array.isArray(S)?S.slice():S,this._composedAsyncValidatorFn=function V(b){return Array.isArray(b)?zt(b):b||null}(this._rawAsyncValidators)}}class bt extends Ye{constructor(S,v,P){super(Mr(v),K(P,v)),this.controls=S,this._initObservables(),this._setUpdateStrategy(v),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(S,v){return this.controls[S]?this.controls[S]:(this.controls[S]=v,v.setParent(this),v._registerOnCollectionChange(this._onCollectionChange),v)}addControl(S,v,P={}){this.registerControl(S,v),this.updateValueAndValidity({emitEvent:P.emitEvent}),this._onCollectionChange()}removeControl(S,v={}){this.controls[S]&&this.controls[S]._registerOnCollectionChange(()=>{}),delete this.controls[S],this.updateValueAndValidity({emitEvent:v.emitEvent}),this._onCollectionChange()}setControl(S,v,P={}){this.controls[S]&&this.controls[S]._registerOnCollectionChange(()=>{}),delete this.controls[S],v&&this.registerControl(S,v),this.updateValueAndValidity({emitEvent:P.emitEvent}),this._onCollectionChange()}contains(S){return this.controls.hasOwnProperty(S)&&this.controls[S].enabled}setValue(S,v={}){Fe(this,0,S),Object.keys(S).forEach(P=>{me(this,!0,P),this.controls[P].setValue(S[P],{onlySelf:!0,emitEvent:v.emitEvent})}),this.updateValueAndValidity(v)}patchValue(S,v={}){null!=S&&(Object.keys(S).forEach(P=>{const B=this.controls[P];B&&B.patchValue(S[P],{onlySelf:!0,emitEvent:v.emitEvent})}),this.updateValueAndValidity(v))}reset(S={},v={}){this._forEachChild((P,B)=>{P.reset(S?S[B]:null,{onlySelf:!0,emitEvent:v.emitEvent})}),this._updatePristine(v),this._updateTouched(v),this.updateValueAndValidity(v)}getRawValue(){return this._reduceChildren({},(S,v,P)=>(S[P]=v.getRawValue(),S))}_syncPendingControls(){let S=this._reduceChildren(!1,(v,P)=>!!P._syncPendingControls()||v);return S&&this.updateValueAndValidity({onlySelf:!0}),S}_forEachChild(S){Object.keys(this.controls).forEach(v=>{const P=this.controls[v];P&&S(P,v)})}_setUpControls(){this._forEachChild(S=>{S.setParent(this),S._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(S){for(const[v,P]of Object.entries(this.controls))if(this.contains(v)&&S(P))return!0;return!1}_reduceValue(){return this._reduceChildren({},(v,P,B)=>((P.enabled||this.disabled)&&(v[B]=P.value),v))}_reduceChildren(S,v){let P=S;return this._forEachChild((B,Ke)=>{P=v(P,B,Ke)}),P}_allControlsDisabled(){for(const S of Object.keys(this.controls))if(this.controls[S].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(S){return this.controls.hasOwnProperty(S)?this.controls[S]:null}}class zn extends bt{}const G=new d.nKC("CallSetDisabledState",{providedIn:"root",factory:()=>j}),j="always";function $(b,S){return[...S.path,b]}function fe(b,S,v=j){Wt(b,S),S.valueAccessor.writeValue(b.value),(b.disabled||"always"===v)&&S.valueAccessor.setDisabledState?.(b.disabled),function rn(b,S){S.valueAccessor.registerOnChange(v=>{b._pendingValue=v,b._pendingChange=!0,b._pendingDirty=!0,"change"===b.updateOn&&Jt(b,S)})}(b,S),function er(b,S){const v=(P,B)=>{S.valueAccessor.writeValue(P),B&&S.viewToModelUpdate(P)};b.registerOnChange(v),S._registerOnDestroy(()=>{b._unregisterOnChange(v)})}(b,S),function ir(b,S){S.valueAccessor.registerOnTouched(()=>{b._pendingTouched=!0,"blur"===b.updateOn&&b._pendingChange&&Jt(b,S),"submit"!==b.updateOn&&b.markAsTouched()})}(b,S),function Oe(b,S){if(S.valueAccessor.setDisabledState){const v=P=>{S.valueAccessor.setDisabledState(P)};b.registerOnDisabledChange(v),S._registerOnDestroy(()=>{b._unregisterOnDisabledChange(v)})}}(b,S)}function Se(b,S,v=!0){const P=()=>{};S.valueAccessor&&(S.valueAccessor.registerOnChange(P),S.valueAccessor.registerOnTouched(P)),ke(b,S),b&&(S._invokeOnDestroyCallbacks(),b._registerOnCollectionChange(()=>{}))}function Be(b,S){b.forEach(v=>{v.registerOnValidatorChange&&v.registerOnValidatorChange(S)})}function Wt(b,S){const v=kt(b);null!==S.validator?b.setValidators(at(v,S.validator)):"function"==typeof v&&b.setValidators([v]);const P=qt(b);null!==S.asyncValidator?b.setAsyncValidators(at(P,S.asyncValidator)):"function"==typeof P&&b.setAsyncValidators([P]);const B=()=>b.updateValueAndValidity();Be(S._rawValidators,B),Be(S._rawAsyncValidators,B)}function ke(b,S){let v=!1;if(null!==b){if(null!==S.validator){const B=kt(b);if(Array.isArray(B)&&B.length>0){const Ke=B.filter(vt=>vt!==S.validator);Ke.length!==B.length&&(v=!0,b.setValidators(Ke))}}if(null!==S.asyncValidator){const B=qt(b);if(Array.isArray(B)&&B.length>0){const Ke=B.filter(vt=>vt!==S.asyncValidator);Ke.length!==B.length&&(v=!0,b.setAsyncValidators(Ke))}}}const P=()=>{};return Be(S._rawValidators,P),Be(S._rawAsyncValidators,P),v}function Jt(b,S){b._pendingDirty&&b.markAsDirty(),b.setValue(b._pendingValue,{emitModelToViewChange:!1}),S.viewToModelUpdate(b._pendingValue),b._pendingChange=!1}function or(b,S){if(!b.hasOwnProperty("model"))return!1;const v=b.model;return!!v.isFirstChange()||!Object.is(S,v.currentValue)}function Tr(b,S){if(!S)return null;let v,P,B;return Array.isArray(S),S.forEach(Ke=>{Ke.constructor===De?v=Ke:function Ir(b){return Object.getPrototypeOf(b.constructor)===Ne}(Ke)?P=Ke:B=Ke}),B||P||v||null}function Ri(b,S){const v=b.indexOf(S);v>-1&&b.splice(v,1)}function xi(b){return"object"==typeof b&&null!==b&&2===Object.keys(b).length&&"value"in b&&"disabled"in b}const ar=class extends Ye{constructor(S=null,v,P){super(Mr(v),K(P,v)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(S),this._setUpdateStrategy(v),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),z(v)&&(v.nonNullable||v.initialValueIsDefault)&&(this.defaultValue=xi(S)?S.value:S)}setValue(S,v={}){this.value=this._pendingValue=S,this._onChange.length&&!1!==v.emitModelToViewChange&&this._onChange.forEach(P=>P(this.value,!1!==v.emitViewToModelChange)),this.updateValueAndValidity(v)}patchValue(S,v={}){this.setValue(S,v)}reset(S=this.defaultValue,v={}){this._applyFormState(S),this.markAsPristine(v),this.markAsUntouched(v),this.setValue(this.value,v),this._pendingChange=!1}_updateValue(){}_anyControls(S){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(S){this._onChange.push(S)}_unregisterOnChange(S){Ri(this._onChange,S)}registerOnDisabledChange(S){this._onDisabledChange.push(S)}_unregisterOnDisabledChange(S){Ri(this._onDisabledChange,S)}_forEachChild(S){}_syncPendingControls(){return!("submit"!==this.updateOn||(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),!this._pendingChange)||(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),0))}_applyFormState(S){xi(S)?(this.value=this._pendingValue=S.value,S.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=S}},Qi={provide:it,useExisting:(0,d.Rfq)(()=>oi)},ki=(()=>Promise.resolve())();let oi=(()=>{class b extends it{constructor(v,P,B,Ke,vt,Qn){super(),this._changeDetectorRef=vt,this.callSetDisabledState=Qn,this.control=new ar,this._registered=!1,this.name="",this.update=new d.bkB,this._parent=v,this._setValidators(P),this._setAsyncValidators(B),this.valueAccessor=Tr(0,Ke)}ngOnChanges(v){if(this._checkForErrors(),!this._registered||"name"in v){if(this._registered&&(this._checkName(),this.formDirective)){const P=v.name.previousValue;this.formDirective.removeControl({name:P,path:this._getPath(P)})}this._setUpControl()}"isDisabled"in v&&this._updateDisabled(v),or(v,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(v){this.viewModel=v,this.update.emit(v)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&null!=this.options.updateOn&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!(!this.options||!this.options.standalone)}_setUpStandalone(){fe(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),this._isStandalone()}_updateValue(v){ki.then(()=>{this.control.setValue(v,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(v){const P=v.isDisabled.currentValue,B=0!==P&&(0,d.L39)(P);ki.then(()=>{B&&!this.control.disabled?this.control.disable():!B&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(v){return this._parent?$(v,this._parent):[v]}static{this.\u0275fac=function(P){return new(P||b)(d.rXU(tn,9),d.rXU(nt,10),d.rXU(Re,10),d.rXU(Ge,10),d.rXU(d.gRc,8),d.rXU(G,8))}}static{this.\u0275dir=d.FsC({type:b,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:["disabled","isDisabled"],model:["ngModel","model"],options:["ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[d.Jv_([Qi]),d.Vt3,d.OA$]})}}return b})(),qi=(()=>{class b{static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275dir=d.FsC({type:b,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}}return b})();const lo={provide:Ge,useExisting:(0,d.Rfq)(()=>Zi),multi:!0};let Zi=(()=>{class b extends Ne{writeValue(v){this.setProperty("value",v??"")}registerOnChange(v){this.onChange=P=>{v(""==P?null:parseFloat(P))}}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=d.xGo(b)))(B||b)}}()}static{this.\u0275dir=d.FsC({type:b,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(P,B){1&P&&d.bIt("input",function(vt){return B.onChange(vt.target.value)})("blur",function(){return B.onTouched()})},features:[d.Jv_([lo]),d.Vt3]})}}return b})();const bo={provide:Ge,useExisting:(0,d.Rfq)(()=>jr),multi:!0};let si=(()=>{class b{static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275mod=d.$C({type:b})}static{this.\u0275inj=d.G2t({})}}return b})(),uo=(()=>{class b{constructor(){this._accessors=[]}add(v,P){this._accessors.push([v,P])}remove(v){for(let P=this._accessors.length-1;P>=0;--P)if(this._accessors[P][1]===v)return void this._accessors.splice(P,1)}select(v){this._accessors.forEach(P=>{this._isSameGroup(P,v)&&P[1]!==v&&P[1].fireUncheck(v.value)})}_isSameGroup(v,P){return!!v[0].control&&v[0]._parent===P._control._parent&&v[1].name===P.name}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275prov=d.jDH({token:b,factory:b.\u0275fac,providedIn:si})}}return b})(),jr=(()=>{class b extends Ne{constructor(v,P,B,Ke){super(v,P),this._registry=B,this._injector=Ke,this.setDisabledStateFired=!1,this.onChange=()=>{},this.callSetDisabledState=(0,d.WQX)(G,{optional:!0})??j}ngOnInit(){this._control=this._injector.get(it),this._checkName(),this._registry.add(this._control,this)}ngOnDestroy(){this._registry.remove(this)}writeValue(v){this._state=v===this.value,this.setProperty("checked",this._state)}registerOnChange(v){this._fn=v,this.onChange=()=>{v(this.value),this._registry.select(this)}}setDisabledState(v){(this.setDisabledStateFired||v||"whenDisabledForLegacyCode"===this.callSetDisabledState)&&this.setProperty("disabled",v),this.setDisabledStateFired=!0}fireUncheck(v){this.writeValue(v)}_checkName(){!this.name&&this.formControlName&&(this.name=this.formControlName)}static{this.\u0275fac=function(P){return new(P||b)(d.rXU(d.sFG),d.rXU(d.aKT),d.rXU(uo),d.rXU(d.zZn))}}static{this.\u0275dir=d.FsC({type:b,selectors:[["input","type","radio","formControlName",""],["input","type","radio","formControl",""],["input","type","radio","ngModel",""]],hostBindings:function(P,B){1&P&&d.bIt("change",function(){return B.onChange()})("blur",function(){return B.onTouched()})},inputs:{name:"name",formControlName:"formControlName",value:"value"},features:[d.Jv_([bo]),d.Vt3]})}}return b})();const Ji=new d.nKC("NgModelWithFormControlWarning"),Yi={provide:tn,useExisting:(0,d.Rfq)(()=>Mt)};let Mt=(()=>{class b extends tn{constructor(v,P,B){super(),this.callSetDisabledState=B,this.submitted=!1,this._onCollectionChange=()=>this._updateDomValue(),this.directives=[],this.form=null,this.ngSubmit=new d.bkB,this._setValidators(v),this._setAsyncValidators(P)}ngOnChanges(v){this._checkFormPresent(),v.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(ke(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(v){const P=this.form.get(v.path);return fe(P,v,this.callSetDisabledState),P.updateValueAndValidity({emitEvent:!1}),this.directives.push(v),P}getControl(v){return this.form.get(v.path)}removeControl(v){Se(v.control||null,v,!1),function vr(b,S){const v=b.indexOf(S);v>-1&&b.splice(v,1)}(this.directives,v)}addFormGroup(v){this._setUpFormContainer(v)}removeFormGroup(v){this._cleanUpFormContainer(v)}getFormGroup(v){return this.form.get(v.path)}addFormArray(v){this._setUpFormContainer(v)}removeFormArray(v){this._cleanUpFormContainer(v)}getFormArray(v){return this.form.get(v.path)}updateModel(v,P){this.form.get(v.path).setValue(P)}onSubmit(v){return this.submitted=!0,function ni(b,S){b._syncPendingControls(),S.forEach(v=>{const P=v.control;"submit"===P.updateOn&&P._pendingChange&&(v.viewToModelUpdate(P._pendingValue),P._pendingChange=!1)})}(this.form,this.directives),this.ngSubmit.emit(v),"dialog"===v?.target?.method}onReset(){this.resetForm()}resetForm(v=void 0){this.form.reset(v),this.submitted=!1}_updateDomValue(){this.directives.forEach(v=>{const P=v.control,B=this.form.get(v.path);P!==B&&(Se(P||null,v),(b=>b instanceof ar)(B)&&(fe(B,v,this.callSetDisabledState),v.control=B))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(v){const P=this.form.get(v.path);(function Kr(b,S){Wt(b,S)})(P,v),P.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(v){if(this.form){const P=this.form.get(v.path);P&&function yr(b,S){return ke(b,S)}(P,v)&&P.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){Wt(this.form,this),this._oldForm&&ke(this._oldForm,this)}_checkFormPresent(){}static{this.\u0275fac=function(P){return new(P||b)(d.rXU(nt,10),d.rXU(Re,10),d.rXU(G,8))}}static{this.\u0275dir=d.FsC({type:b,selectors:[["","formGroup",""]],hostBindings:function(P,B){1&P&&d.bIt("submit",function(vt){return B.onSubmit(vt)})("reset",function(){return B.onReset()})},inputs:{form:["formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[d.Jv_([Yi]),d.Vt3,d.OA$]})}}return b})();const Nr={provide:it,useExisting:(0,d.Rfq)(()=>Qr)};let Qr=(()=>{class b extends it{set isDisabled(v){}static{this._ngModelWarningSentOnce=!1}constructor(v,P,B,Ke,vt){super(),this._ngModelWarningConfig=vt,this._added=!1,this.name=null,this.update=new d.bkB,this._ngModelWarningSent=!1,this._parent=v,this._setValidators(P),this._setAsyncValidators(B),this.valueAccessor=Tr(0,Ke)}ngOnChanges(v){this._added||this._setUpControl(),or(v,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(v){this.viewModel=v,this.update.emit(v)}get path(){return $(null==this.name?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}_setUpControl(){this._checkParentType(),this.control=this.formDirective.addControl(this),this._added=!0}static{this.\u0275fac=function(P){return new(P||b)(d.rXU(tn,13),d.rXU(nt,10),d.rXU(Re,10),d.rXU(Ge,10),d.rXU(Ji,8))}}static{this.\u0275dir=d.FsC({type:b,selectors:[["","formControlName",""]],inputs:{name:["formControlName","name"],isDisabled:["disabled","isDisabled"],model:["ngModel","model"]},outputs:{update:"ngModelChange"},features:[d.Jv_([Nr]),d.Vt3,d.OA$]})}}return b})();const Mo={provide:Ge,useExisting:(0,d.Rfq)(()=>Le),multi:!0};function _i(b,S){return null==b?`${S}`:(S&&"object"==typeof S&&(S="Object"),`${b}: ${S}`.slice(0,50))}let Le=(()=>{class b extends Ne{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(v){this._compareWith=v}writeValue(v){this.value=v;const B=_i(this._getOptionId(v),v);this.setProperty("value",B)}registerOnChange(v){this.onChange=P=>{this.value=this._getOptionValue(P),v(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(v){for(const P of this._optionMap.keys())if(this._compareWith(this._optionMap.get(P),v))return P;return null}_getOptionValue(v){const P=function ln(b){return b.split(":")[0]}(v);return this._optionMap.has(P)?this._optionMap.get(P):v}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=d.xGo(b)))(B||b)}}()}static{this.\u0275dir=d.FsC({type:b,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(P,B){1&P&&d.bIt("change",function(vt){return B.onChange(vt.target.value)})("blur",function(){return B.onTouched()})},inputs:{compareWith:"compareWith"},features:[d.Jv_([Mo]),d.Vt3]})}}return b})(),mt=(()=>{class b{constructor(v,P,B){this._element=v,this._renderer=P,this._select=B,this._select&&(this.id=this._select._registerOption())}set ngValue(v){null!=this._select&&(this._select._optionMap.set(this.id,v),this._setElementValue(_i(this.id,v)),this._select.writeValue(this._select.value))}set value(v){this._setElementValue(v),this._select&&this._select.writeValue(this._select.value)}_setElementValue(v){this._renderer.setProperty(this._element.nativeElement,"value",v)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static{this.\u0275fac=function(P){return new(P||b)(d.rXU(d.aKT),d.rXU(d.sFG),d.rXU(Le,9))}}static{this.\u0275dir=d.FsC({type:b,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}})}}return b})();const nn={provide:Ge,useExisting:(0,d.Rfq)(()=>tr),multi:!0};function Kn(b,S){return null==b?`${S}`:("string"==typeof S&&(S=`'${S}'`),S&&"object"==typeof S&&(S="Object"),`${b}: ${S}`.slice(0,50))}let tr=(()=>{class b extends Ne{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(v){this._compareWith=v}writeValue(v){let P;if(this.value=v,Array.isArray(v)){const B=v.map(Ke=>this._getOptionId(Ke));P=(Ke,vt)=>{Ke._setSelected(B.indexOf(vt.toString())>-1)}}else P=(B,Ke)=>{B._setSelected(!1)};this._optionMap.forEach(P)}registerOnChange(v){this.onChange=P=>{const B=[],Ke=P.selectedOptions;if(void 0!==Ke){const vt=Ke;for(let Qn=0;Qn<vt.length;Qn++){const Mi=this._getOptionValue(vt[Qn].value);B.push(Mi)}}else{const vt=P.options;for(let Qn=0;Qn<vt.length;Qn++){const ui=vt[Qn];if(ui.selected){const Mi=this._getOptionValue(ui.value);B.push(Mi)}}}this.value=B,v(B)}}_registerOption(v){const P=(this._idCounter++).toString();return this._optionMap.set(P,v),P}_getOptionId(v){for(const P of this._optionMap.keys())if(this._compareWith(this._optionMap.get(P)._value,v))return P;return null}_getOptionValue(v){const P=function li(b){return b.split(":")[0]}(v);return this._optionMap.has(P)?this._optionMap.get(P)._value:v}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=d.xGo(b)))(B||b)}}()}static{this.\u0275dir=d.FsC({type:b,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(P,B){1&P&&d.bIt("change",function(vt){return B.onChange(vt.target)})("blur",function(){return B.onTouched()})},inputs:{compareWith:"compareWith"},features:[d.Jv_([nn]),d.Vt3]})}}return b})(),un=(()=>{class b{constructor(v,P,B){this._element=v,this._renderer=P,this._select=B,this._select&&(this.id=this._select._registerOption(this))}set ngValue(v){null!=this._select&&(this._value=v,this._setElementValue(Kn(this.id,v)),this._select.writeValue(this._select.value))}set value(v){this._select?(this._value=v,this._setElementValue(Kn(this.id,v)),this._select.writeValue(this._select.value)):this._setElementValue(v)}_setElementValue(v){this._renderer.setProperty(this._element.nativeElement,"value",v)}_setSelected(v){this._renderer.setProperty(this._element.nativeElement,"selected",v)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static{this.\u0275fac=function(P){return new(P||b)(d.rXU(d.aKT),d.rXU(d.sFG),d.rXU(tr,9))}}static{this.\u0275dir=d.FsC({type:b,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}})}}return b})();function ur(b){return"number"==typeof b?b:parseFloat(b)}let lt=(()=>{class b{constructor(){this._validator=Fn}ngOnChanges(v){if(this.inputName in v){const P=this.normalizeInput(v[this.inputName].currentValue);this._enabled=this.enabled(P),this._validator=this._enabled?this.createValidator(P):Fn,this._onChange&&this._onChange()}}validate(v){return this._validator(v)}registerOnValidatorChange(v){this._onChange=v}enabled(v){return null!=v}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275dir=d.FsC({type:b,features:[d.OA$]})}}return b})();const qr={provide:nt,useExisting:(0,d.Rfq)(()=>Ei),multi:!0};let Ei=(()=>{class b extends lt{constructor(){super(...arguments),this.inputName="max",this.normalizeInput=v=>ur(v),this.createValidator=v=>Ft(v)}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=d.xGo(b)))(B||b)}}()}static{this.\u0275dir=d.FsC({type:b,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(P,B){2&P&&d.BMQ("max",B._enabled?B.max:null)},inputs:{max:"max"},features:[d.Jv_([qr]),d.Vt3]})}}return b})();const Ur={provide:nt,useExisting:(0,d.Rfq)(()=>an),multi:!0};let an=(()=>{class b extends lt{constructor(){super(...arguments),this.inputName="min",this.normalizeInput=v=>ur(v),this.createValidator=v=>rt(v)}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=d.xGo(b)))(B||b)}}()}static{this.\u0275dir=d.FsC({type:b,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(P,B){2&P&&d.BMQ("min",B._enabled?B.min:null)},inputs:{min:"min"},features:[d.Jv_([Ur]),d.Vt3]})}}return b})(),Nn=(()=>{class b{static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275mod=d.$C({type:b})}static{this.\u0275inj=d.G2t({imports:[si]})}}return b})();class Ui extends Ye{constructor(S,v,P){super(Mr(v),K(P,v)),this.controls=S,this._initObservables(),this._setUpdateStrategy(v),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}at(S){return this.controls[this._adjustIndex(S)]}push(S,v={}){this.controls.push(S),this._registerControl(S),this.updateValueAndValidity({emitEvent:v.emitEvent}),this._onCollectionChange()}insert(S,v,P={}){this.controls.splice(S,0,v),this._registerControl(v),this.updateValueAndValidity({emitEvent:P.emitEvent})}removeAt(S,v={}){let P=this._adjustIndex(S);P<0&&(P=0),this.controls[P]&&this.controls[P]._registerOnCollectionChange(()=>{}),this.controls.splice(P,1),this.updateValueAndValidity({emitEvent:v.emitEvent})}setControl(S,v,P={}){let B=this._adjustIndex(S);B<0&&(B=0),this.controls[B]&&this.controls[B]._registerOnCollectionChange(()=>{}),this.controls.splice(B,1),v&&(this.controls.splice(B,0,v),this._registerControl(v)),this.updateValueAndValidity({emitEvent:P.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(S,v={}){Fe(this,0,S),S.forEach((P,B)=>{me(this,!1,B),this.at(B).setValue(P,{onlySelf:!0,emitEvent:v.emitEvent})}),this.updateValueAndValidity(v)}patchValue(S,v={}){null!=S&&(S.forEach((P,B)=>{this.at(B)&&this.at(B).patchValue(P,{onlySelf:!0,emitEvent:v.emitEvent})}),this.updateValueAndValidity(v))}reset(S=[],v={}){this._forEachChild((P,B)=>{P.reset(S[B],{onlySelf:!0,emitEvent:v.emitEvent})}),this._updatePristine(v),this._updateTouched(v),this.updateValueAndValidity(v)}getRawValue(){return this.controls.map(S=>S.getRawValue())}clear(S={}){this.controls.length<1||(this._forEachChild(v=>v._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:S.emitEvent}))}_adjustIndex(S){return S<0?S+this.length:S}_syncPendingControls(){let S=this.controls.reduce((v,P)=>!!P._syncPendingControls()||v,!1);return S&&this.updateValueAndValidity({onlySelf:!0}),S}_forEachChild(S){this.controls.forEach((v,P)=>{S(v,P)})}_updateValue(){this.value=this.controls.filter(S=>S.enabled||this.disabled).map(S=>S.value)}_anyControls(S){return this.controls.some(v=>v.enabled&&S(v))}_setUpControls(){this._forEachChild(S=>this._registerControl(S))}_allControlsDisabled(){for(const S of this.controls)if(S.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(S){S.setParent(this),S._registerOnCollectionChange(this._onCollectionChange)}_find(S){return this.at(S)??null}}function Pn(b){return!!b&&(void 0!==b.asyncValidators||void 0!==b.validators||void 0!==b.updateOn)}let Dn=(()=>{class b{constructor(){this.useNonNullable=!1}get nonNullable(){const v=new b;return v.useNonNullable=!0,v}group(v,P=null){const B=this._reduceControls(v);let Ke={};return Pn(P)?Ke=P:null!==P&&(Ke.validators=P.validator,Ke.asyncValidators=P.asyncValidator),new bt(B,Ke)}record(v,P=null){const B=this._reduceControls(v);return new zn(B,P)}control(v,P,B){let Ke={};return this.useNonNullable?(Pn(P)?Ke=P:(Ke.validators=P,Ke.asyncValidators=B),new ar(v,{...Ke,nonNullable:!0})):new ar(v,P,B)}array(v,P,B){const Ke=v.map(vt=>this._createControl(vt));return new Ui(Ke,P,B)}_reduceControls(v){const P={};return Object.keys(v).forEach(B=>{P[B]=this._createControl(v[B])}),P}_createControl(v){return v instanceof ar||v instanceof Ye?v:Array.isArray(v)?this.control(v[0],v.length>1?v[1]:null,v.length>2?v[2]:null):this.control(v)}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275prov=d.jDH({token:b,factory:b.\u0275fac,providedIn:"root"})}}return b})(),Rn=(()=>{class b{static withConfig(v){return{ngModule:b,providers:[{provide:G,useValue:v.callSetDisabledState??j}]}}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275mod=d.$C({type:b})}static{this.\u0275inj=d.G2t({imports:[Nn]})}}return b})(),co=(()=>{class b{static withConfig(v){return{ngModule:b,providers:[{provide:Ji,useValue:v.warnOnNgModelWithFormControl??"always"},{provide:G,useValue:v.callSetDisabledState??j}]}}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275mod=d.$C({type:b})}static{this.\u0275inj=d.G2t({imports:[Nn]})}}return b})()},345:(tt,ye,R)=>{R.d(ye,{B7:()=>Ae,Bb:()=>gt,hE:()=>it,sG:()=>at});var d=R(540),w=R(177);class de extends w.VF{constructor(){super(...arguments),this.supportsDOMEvents=!0}}class ae extends de{static makeCurrent(){(0,w.ZD)(new ae)}onAndCancel(K,V,z){return K.addEventListener(V,z),()=>{K.removeEventListener(V,z)}}dispatchEvent(K,V){K.dispatchEvent(V)}remove(K){K.parentNode&&K.parentNode.removeChild(K)}createElement(K,V){return(V=V||this.getDefaultDocument()).createElement(K)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(K){return K.nodeType===Node.ELEMENT_NODE}isShadowRoot(K){return K instanceof DocumentFragment}getGlobalEventTarget(K,V){return"window"===V?window:"document"===V?K:"body"===V?K.body:null}getBaseHref(K){const V=function oe(){return X=X||document.querySelector("base"),X?X.getAttribute("href"):null}();return null==V?null:function ge(ce){ie=ie||document.createElement("a"),ie.setAttribute("href",ce);const K=ie.pathname;return"/"===K.charAt(0)?K:`/${K}`}(V)}resetBaseElement(){X=null}getUserAgent(){return window.navigator.userAgent}getCookie(K){return(0,w._b)(document.cookie,K)}}let ie,X=null,pe=(()=>{class ce{build(){return new XMLHttpRequest}static{this.\u0275fac=function(z){return new(z||ce)}}static{this.\u0275prov=d.jDH({token:ce,factory:ce.\u0275fac})}}return ce})();const Z=new d.nKC("EventManagerPlugins");let ne=(()=>{class ce{constructor(V,z){this._zone=z,this._eventNameToPlugin=new Map,V.forEach(me=>{me.manager=this}),this._plugins=V.slice().reverse()}addEventListener(V,z,me){return this._findPluginFor(z).addEventListener(V,z,me)}getZone(){return this._zone}_findPluginFor(V){let z=this._eventNameToPlugin.get(V);if(z)return z;if(z=this._plugins.find(Fe=>Fe.supports(V)),!z)throw new d.wOt(5101,!1);return this._eventNameToPlugin.set(V,z),z}static{this.\u0275fac=function(z){return new(z||ce)(d.KVO(Z),d.KVO(d.SKi))}}static{this.\u0275prov=d.jDH({token:ce,factory:ce.\u0275fac})}}return ce})();class be{constructor(K){this._doc=K}}const Ne="ng-app-id";let Ge=(()=>{class ce{constructor(V,z,me,Fe={}){this.doc=V,this.appId=z,this.nonce=me,this.platformId=Fe,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=(0,w.Vy)(Fe),this.resetHostNodes()}addStyles(V){for(const z of V)1===this.changeUsageCount(z,1)&&this.onStyleAdded(z)}removeStyles(V){for(const z of V)this.changeUsageCount(z,-1)<=0&&this.onStyleRemoved(z)}ngOnDestroy(){const V=this.styleNodesInDOM;V&&(V.forEach(z=>z.remove()),V.clear());for(const z of this.getAllStyles())this.onStyleRemoved(z);this.resetHostNodes()}addHost(V){this.hostNodes.add(V);for(const z of this.getAllStyles())this.addStyleToHost(V,z)}removeHost(V){this.hostNodes.delete(V)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(V){for(const z of this.hostNodes)this.addStyleToHost(z,V)}onStyleRemoved(V){const z=this.styleRef;z.get(V)?.elements?.forEach(me=>me.remove()),z.delete(V)}collectServerRenderedStyles(){const V=this.doc.head?.querySelectorAll(`style[${Ne}="${this.appId}"]`);if(V?.length){const z=new Map;return V.forEach(me=>{null!=me.textContent&&z.set(me.textContent,me)}),z}return null}changeUsageCount(V,z){const me=this.styleRef;if(me.has(V)){const Fe=me.get(V);return Fe.usage+=z,Fe.usage}return me.set(V,{usage:z,elements:[]}),z}getStyleElement(V,z){const me=this.styleNodesInDOM,Fe=me?.get(z);if(Fe?.parentNode===V)return me.delete(z),Fe.removeAttribute(Ne),Fe;{const Ye=this.doc.createElement("style");return this.nonce&&Ye.setAttribute("nonce",this.nonce),Ye.textContent=z,this.platformIsServer&&Ye.setAttribute(Ne,this.appId),Ye}}addStyleToHost(V,z){const me=this.getStyleElement(V,z);V.appendChild(me);const Fe=this.styleRef,Ye=Fe.get(z)?.elements;Ye?Ye.push(me):Fe.set(z,{elements:[me],usage:1})}resetHostNodes(){const V=this.hostNodes;V.clear(),V.add(this.doc.head)}static{this.\u0275fac=function(z){return new(z||ce)(d.KVO(w.qQ),d.KVO(d.sZ2),d.KVO(d.BIS,8),d.KVO(d.Agw))}}static{this.\u0275prov=d.jDH({token:ce,factory:ce.\u0275fac})}}return ce})();const Lt={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},Tt=/%COMP%/g,Te=new d.nKC("RemoveStylesOnCompDestroy",{providedIn:"root",factory:()=>!1});function Re(ce,K){return K.map(V=>V.replace(Tt,ce))}let Ae=(()=>{class ce{constructor(V,z,me,Fe,Ye,bt,Pt,Yt=null){this.eventManager=V,this.sharedStylesHost=z,this.appId=me,this.removeStylesOnCompDestroy=Fe,this.doc=Ye,this.platformId=bt,this.ngZone=Pt,this.nonce=Yt,this.rendererByCompId=new Map,this.platformIsServer=(0,w.Vy)(bt),this.defaultRenderer=new Ee(V,Ye,Pt,this.platformIsServer)}createRenderer(V,z){if(!V||!z)return this.defaultRenderer;this.platformIsServer&&z.encapsulation===d.gXe.ShadowDom&&(z={...z,encapsulation:d.gXe.Emulated});const me=this.getOrCreateRenderer(V,z);return me instanceof en?me.applyToHost(V):me instanceof xe&&me.applyStyles(),me}getOrCreateRenderer(V,z){const me=this.rendererByCompId;let Fe=me.get(z.id);if(!Fe){const Ye=this.doc,bt=this.ngZone,Pt=this.eventManager,Yt=this.sharedStylesHost,zn=this.removeStylesOnCompDestroy,Sr=this.platformIsServer;switch(z.encapsulation){case d.gXe.Emulated:Fe=new en(Pt,Yt,z,this.appId,zn,Ye,bt,Sr);break;case d.gXe.ShadowDom:return new $n(Pt,Yt,V,z,Ye,bt,this.nonce,Sr);default:Fe=new xe(Pt,Yt,z,zn,Ye,bt,Sr)}me.set(z.id,Fe)}return Fe}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(z){return new(z||ce)(d.KVO(ne),d.KVO(Ge),d.KVO(d.sZ2),d.KVO(Te),d.KVO(w.qQ),d.KVO(d.Agw),d.KVO(d.SKi),d.KVO(d.BIS))}}static{this.\u0275prov=d.jDH({token:ce,factory:ce.\u0275fac})}}return ce})();class Ee{constructor(K,V,z,me){this.eventManager=K,this.doc=V,this.ngZone=z,this.platformIsServer=me,this.data=Object.create(null),this.destroyNode=null}destroy(){}createElement(K,V){return V?this.doc.createElementNS(Lt[V]||V,K):this.doc.createElement(K)}createComment(K){return this.doc.createComment(K)}createText(K){return this.doc.createTextNode(K)}appendChild(K,V){(_n(K)?K.content:K).appendChild(V)}insertBefore(K,V,z){K&&(_n(K)?K.content:K).insertBefore(V,z)}removeChild(K,V){K&&K.removeChild(V)}selectRootElement(K,V){let z="string"==typeof K?this.doc.querySelector(K):K;if(!z)throw new d.wOt(-5104,!1);return V||(z.textContent=""),z}parentNode(K){return K.parentNode}nextSibling(K){return K.nextSibling}setAttribute(K,V,z,me){if(me){V=me+":"+V;const Fe=Lt[me];Fe?K.setAttributeNS(Fe,V,z):K.setAttribute(V,z)}else K.setAttribute(V,z)}removeAttribute(K,V,z){if(z){const me=Lt[z];me?K.removeAttributeNS(me,V):K.removeAttribute(`${z}:${V}`)}else K.removeAttribute(V)}addClass(K,V){K.classList.add(V)}removeClass(K,V){K.classList.remove(V)}setStyle(K,V,z,me){me&(d.czy.DashCase|d.czy.Important)?K.style.setProperty(V,z,me&d.czy.Important?"important":""):K.style[V]=z}removeStyle(K,V,z){z&d.czy.DashCase?K.style.removeProperty(V):K.style[V]=""}setProperty(K,V,z){K[V]=z}setValue(K,V){K.nodeValue=V}listen(K,V,z){if("string"==typeof K&&!(K=(0,w.QT)().getGlobalEventTarget(this.doc,K)))throw new Error(`Unsupported event target ${K} for event ${V}`);return this.eventManager.addEventListener(K,V,this.decoratePreventDefault(z))}decoratePreventDefault(K){return V=>{if("__ngUnwrap__"===V)return K;!1===(this.platformIsServer?this.ngZone.runGuarded(()=>K(V)):K(V))&&V.preventDefault()}}}function _n(ce){return"TEMPLATE"===ce.tagName&&void 0!==ce.content}class $n extends Ee{constructor(K,V,z,me,Fe,Ye,bt,Pt){super(K,Fe,Ye,Pt),this.sharedStylesHost=V,this.hostEl=z,this.shadowRoot=z.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const Yt=Re(me.id,me.styles);for(const zn of Yt){const Sr=document.createElement("style");bt&&Sr.setAttribute("nonce",bt),Sr.textContent=zn,this.shadowRoot.appendChild(Sr)}}nodeOrShadowRoot(K){return K===this.hostEl?this.shadowRoot:K}appendChild(K,V){return super.appendChild(this.nodeOrShadowRoot(K),V)}insertBefore(K,V,z){return super.insertBefore(this.nodeOrShadowRoot(K),V,z)}removeChild(K,V){return super.removeChild(this.nodeOrShadowRoot(K),V)}parentNode(K){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(K)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}}class xe extends Ee{constructor(K,V,z,me,Fe,Ye,bt,Pt){super(K,Fe,Ye,bt),this.sharedStylesHost=V,this.removeStylesOnCompDestroy=me,this.styles=Pt?Re(Pt,z.styles):z.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}}class en extends xe{constructor(K,V,z,me,Fe,Ye,bt,Pt){const Yt=me+"-"+z.id;super(K,V,z,Fe,Ye,bt,Pt,Yt),this.contentAttr=function Ve(ce){return"_ngcontent-%COMP%".replace(Tt,ce)}(Yt),this.hostAttr=function nt(ce){return"_nghost-%COMP%".replace(Tt,ce)}(Yt)}applyToHost(K){this.applyStyles(),this.setAttribute(K,this.hostAttr,"")}createElement(K,V){const z=super.createElement(K,V);return super.setAttribute(z,this.contentAttr,""),z}}let je=(()=>{class ce extends be{constructor(V){super(V)}supports(V){return!0}addEventListener(V,z,me){return V.addEventListener(z,me,!1),()=>this.removeEventListener(V,z,me)}removeEventListener(V,z,me){return V.removeEventListener(z,me)}static{this.\u0275fac=function(z){return new(z||ce)(d.KVO(w.qQ))}}static{this.\u0275prov=d.jDH({token:ce,factory:ce.\u0275fac})}}return ce})();const ft=["alt","control","meta","shift"],Fn={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Fr={alt:ce=>ce.altKey,control:ce=>ce.ctrlKey,meta:ce=>ce.metaKey,shift:ce=>ce.shiftKey};let Q=(()=>{class ce extends be{constructor(V){super(V)}supports(V){return null!=ce.parseEventName(V)}addEventListener(V,z,me){const Fe=ce.parseEventName(z),Ye=ce.eventCallback(Fe.fullKey,me,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>(0,w.QT)().onAndCancel(V,Fe.domEventName,Ye))}static parseEventName(V){const z=V.toLowerCase().split("."),me=z.shift();if(0===z.length||"keydown"!==me&&"keyup"!==me)return null;const Fe=ce._normalizeKey(z.pop());let Ye="",bt=z.indexOf("code");if(bt>-1&&(z.splice(bt,1),Ye="code."),ft.forEach(Yt=>{const zn=z.indexOf(Yt);zn>-1&&(z.splice(zn,1),Ye+=Yt+".")}),Ye+=Fe,0!=z.length||0===Fe.length)return null;const Pt={};return Pt.domEventName=me,Pt.fullKey=Ye,Pt}static matchEventFullKeyCode(V,z){let me=Fn[V.key]||V.key,Fe="";return z.indexOf("code.")>-1&&(me=V.code,Fe="code."),!(null==me||!me)&&(me=me.toLowerCase()," "===me?me="space":"."===me&&(me="dot"),ft.forEach(Ye=>{Ye!==me&&(0,Fr[Ye])(V)&&(Fe+=Ye+".")}),Fe+=me,Fe===z)}static eventCallback(V,z,me){return Fe=>{ce.matchEventFullKeyCode(Fe,V)&&me.runGuarded(()=>z(Fe))}}static _normalizeKey(V){return"esc"===V?"escape":V}static{this.\u0275fac=function(z){return new(z||ce)(d.KVO(w.qQ))}}static{this.\u0275prov=d.jDH({token:ce,factory:ce.\u0275fac})}}return ce})();const at=(0,d.oH4)(d.fpN,"browser",[{provide:d.Agw,useValue:w.AJ},{provide:d.PLl,useValue:function we(){ae.makeCurrent()},multi:!0},{provide:w.qQ,useFactory:function ct(){return(0,d.TL$)(document),document},deps:[]}]),kt=new d.nKC(""),qt=[{provide:d.e01,useClass:class le{addToWindow(K){d.JZv.getAngularTestability=(z,me=!0)=>{const Fe=K.findTestabilityInTree(z,me);if(null==Fe)throw new d.wOt(5103,!1);return Fe},d.JZv.getAllAngularTestabilities=()=>K.getAllTestabilities(),d.JZv.getAllAngularRootElements=()=>K.getAllRootElements(),d.JZv.frameworkStabilizers||(d.JZv.frameworkStabilizers=[]),d.JZv.frameworkStabilizers.push(z=>{const me=d.JZv.getAllAngularTestabilities();let Fe=me.length,Ye=!1;const bt=function(Pt){Ye=Ye||Pt,Fe--,0==Fe&&z(Ye)};me.forEach(Pt=>{Pt.whenStable(bt)})})}findTestabilityInTree(K,V,z){return null==V?null:K.getTestability(V)??(z?(0,w.QT)().isShadowRoot(V)?this.findTestabilityInTree(K,V.host,!0):this.findTestabilityInTree(K,V.parentElement,!0):null)}},deps:[]},{provide:d.WHO,useClass:d.NYb,deps:[d.SKi,d.giA,d.e01]},{provide:d.NYb,useClass:d.NYb,deps:[d.SKi,d.giA,d.e01]}],Jn=[{provide:d.H8p,useValue:"root"},{provide:d.zcH,useFactory:function We(){return new d.zcH},deps:[]},{provide:Z,useClass:je,multi:!0,deps:[w.qQ,d.SKi,d.Agw]},{provide:Z,useClass:Q,multi:!0,deps:[w.qQ]},Ae,Ge,ne,{provide:d._9s,useExisting:Ae},{provide:w.N0,useClass:pe,deps:[]},[]];let gt=(()=>{class ce{constructor(V){}static withServerTransition(V){return{ngModule:ce,providers:[{provide:d.sZ2,useValue:V.appId}]}}static{this.\u0275fac=function(z){return new(z||ce)(d.KVO(kt,12))}}static{this.\u0275mod=d.$C({type:ce})}static{this.\u0275inj=d.G2t({providers:[...Jn,...qt],imports:[w.MD,d.Hbi]})}}return ce})(),it=(()=>{class ce{constructor(V){this._doc=V}getTitle(){return this._doc.title}setTitle(V){this._doc.title=V||""}static{this.\u0275fac=function(z){return new(z||ce)(d.KVO(w.qQ))}}static{this.\u0275prov=d.jDH({token:ce,factory:function(z){let me=null;return me=z?new z:function tn(){return new it((0,d.KVO)(w.qQ))}(),me},providedIn:"root"})}}return ce})();typeof window<"u"&&window},6448:(tt,ye,R)=>{R.d(ye,{nX:()=>Nr,wF:()=>Vr,Ix:()=>Et,Wk:()=>Cn,wQ:()=>as,iI:()=>fr,n3:()=>li});var d=R(540),w=R(1985),de=R(8071),X=R(6648),oe=R(7673),ie=R(4412),ge=R(3073),le=R(3669),pe=R(6450),Z=R(9326),ne=R(8496),be=R(4360),Ne=R(5225);function Ge(...f){const p=(0,Z.lI)(f),u=(0,Z.ms)(f),{args:m,keys:C}=(0,ge.D)(f);if(0===m.length)return(0,X.H)([],p);const O=new w.c(function Lt(f,p,u=le.D){return m=>{Tt(p,()=>{const{length:C}=f,O=new Array(C);let F=C,te=C;for(let ee=0;ee<C;ee++)Tt(p,()=>{const $e=(0,X.H)(f[ee],p);let wt=!1;$e.subscribe((0,be._)(m,dn=>{O[ee]=dn,wt||(wt=!0,te--),te||m.next(u(O.slice()))},()=>{--F||m.complete()}))},m)},m)}}(m,p,C?F=>(0,ne.e)(C,F):le.D));return u?O.pipe((0,pe.I)(u)):O}function Tt(f,p,u){f?(0,Ne.N)(u,f,p):p()}const jt=(0,R(1853).L)(f=>function(){f(this),this.name="EmptyError",this.message="no elements in sequence"});var Qt=R(6365);function Te(...f){return function De(){return(0,Qt.U)(1)}()((0,X.H)(f,(0,Z.lI)(f)))}var Ve=R(8750);function nt(f){return new w.c(p=>{(0,Ve.Tg)(f()).subscribe(p)})}var Re=R(1203),Ae=R(8810),Ee=R(983),rt=R(8359),Ft=R(9974);function _n(){return(0,Ft.N)((f,p)=>{let u=null;f._refCount++;const m=(0,be._)(p,void 0,void 0,void 0,()=>{if(!f||f._refCount<=0||0<--f._refCount)return void(u=null);const C=f._connection,O=u;u=null,C&&(!O||C===O)&&C.unsubscribe(),p.unsubscribe()});f.subscribe(m),m.closed||(u=f.connect())})}class $n extends w.c{constructor(p,u){super(),this.source=p,this.subjectFactory=u,this._subject=null,this._refCount=0,this._connection=null,(0,Ft.S)(p)&&(this.lift=p.lift)}_subscribe(p){return this.getSubject().subscribe(p)}getSubject(){const p=this._subject;return(!p||p.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;const{_connection:p}=this;this._subject=this._connection=null,p?.unsubscribe()}connect(){let p=this._connection;if(!p){p=this._connection=new rt.yU;const u=this.getSubject();p.add(this.source.subscribe((0,be._)(u,void 0,()=>{this._teardown(),u.complete()},m=>{this._teardown(),u.error(m)},()=>this._teardown()))),p.closed&&(this._connection=null,p=rt.yU.EMPTY)}return p}refCount(){return _n()(this)}}var xe=R(1413),en=R(177),je=R(6354),ft=R(5558);function Fn(f){return f<=0?()=>Ee.w:(0,Ft.N)((p,u)=>{let m=0;p.subscribe((0,be._)(u,C=>{++m<=f&&(u.next(C),f<=m&&u.complete())}))})}var Q=R(5964),Y=R(1397);function J(f){return(0,Ft.N)((p,u)=>{let m=!1;p.subscribe((0,be._)(u,C=>{m=!0,u.next(C)},()=>{m||u.next(f),u.complete()}))})}function se(f=Ie){return(0,Ft.N)((p,u)=>{let m=!1;p.subscribe((0,be._)(u,C=>{m=!0,u.next(C)},()=>m?u.complete():u.error(f())))})}function Ie(){return new jt}function we(f,p){const u=arguments.length>=2;return m=>m.pipe(f?(0,Q.p)((C,O)=>f(C,O,m)):le.D,Fn(1),u?J(p):se(()=>new jt))}var We=R(274),ct=R(8141),zt=R(9437);function qt(f){return f<=0?()=>Ee.w:(0,Ft.N)((p,u)=>{let m=[];p.subscribe((0,be._)(u,C=>{m.push(C),f<m.length&&m.shift()},()=>{for(const C of m)u.next(C);u.complete()},void 0,()=>{m=null}))})}var fn=R(980),Gt=R(5343),tn=R(345);const it="primary",Yn=Symbol("RouteTitle");class Ut{constructor(p){this.params=p||{}}has(p){return Object.prototype.hasOwnProperty.call(this.params,p)}get(p){if(this.has(p)){const u=this.params[p];return Array.isArray(u)?u[0]:u}return null}getAll(p){if(this.has(p)){const u=this.params[p];return Array.isArray(u)?u:[u]}return[]}get keys(){return Object.keys(this.params)}}function Tn(f){return new Ut(f)}function ht(f,p,u){const m=u.path.split("/");if(m.length>f.length||"full"===u.pathMatch&&(p.hasChildren()||m.length<f.length))return null;const C={};for(let O=0;O<m.length;O++){const F=m[O],te=f[O];if(F.startsWith(":"))C[F.substring(1)]=te;else if(F!==te.path)return null}return{consumed:f.slice(0,m.length),posParams:C}}function En(f,p){const u=f?Object.keys(f):void 0,m=p?Object.keys(p):void 0;if(!u||!m||u.length!=m.length)return!1;let C;for(let O=0;O<u.length;O++)if(C=u[O],!Hn(f[C],p[C]))return!1;return!0}function Hn(f,p){if(Array.isArray(f)&&Array.isArray(p)){if(f.length!==p.length)return!1;const u=[...f].sort(),m=[...p].sort();return u.every((C,O)=>m[O]===C)}return f===p}function kn(f){return f.length>0?f[f.length-1]:null}function Nt(f){return function ae(f){return!!f&&(f instanceof w.c||(0,de.T)(f.lift)&&(0,de.T)(f.subscribe))}(f)?f:(0,d.jNT)(f)?(0,X.H)(Promise.resolve(f)):(0,oe.of)(f)}const hn={exact:function ze(f,p,u){if(!ot(f.segments,p.segments)||!gr(f.segments,p.segments,u)||f.numberOfChildren!==p.numberOfChildren)return!1;for(const m in p.children)if(!f.children[m]||!ze(f.children[m],p.children[m],u))return!1;return!0},subset:kr},ti={exact:function Xi(f,p){return En(f,p)},subset:function Gr(f,p){return Object.keys(p).length<=Object.keys(f).length&&Object.keys(p).every(u=>Hn(f[u],p[u]))},ignored:()=>!0};function Zt(f,p,u){return hn[u.paths](f.root,p.root,u.matrixParams)&&ti[u.queryParams](f.queryParams,p.queryParams)&&!("exact"===u.fragment&&f.fragment!==p.fragment)}function kr(f,p,u){return An(f,p,p.segments,u)}function An(f,p,u,m){if(f.segments.length>u.length){const C=f.segments.slice(0,u.length);return!(!ot(C,u)||p.hasChildren()||!gr(C,u,m))}if(f.segments.length===u.length){if(!ot(f.segments,u)||!gr(f.segments,u,m))return!1;for(const C in p.children)if(!f.children[C]||!kr(f.children[C],p.children[C],m))return!1;return!0}{const C=u.slice(0,f.segments.length),O=u.slice(f.segments.length);return!!(ot(f.segments,C)&&gr(f.segments,C,m)&&f.children[it])&&An(f.children[it],p,O,m)}}function gr(f,p,u){return p.every((m,C)=>ti[u](f[C].parameters,m.parameters))}class wn{constructor(p=new Ze([],{}),u={},m=null){this.root=p,this.queryParams=u,this.fragment=m}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=Tn(this.queryParams)),this._queryParamMap}toString(){return Mr.serialize(this)}}class Ze{constructor(p,u){this.segments=p,this.children=u,this.parent=null,Object.values(u).forEach(m=>m.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return ce(this)}}class Wr{constructor(p,u){this.path=p,this.parameters=u}get parameterMap(){return this._parameterMap||(this._parameterMap=Tn(this.parameters)),this._parameterMap}toString(){return Pt(this)}}function ot(f,p){return f.length===p.length&&f.every((u,m)=>u.path===p[m].path)}let On=(()=>{class f{static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=d.jDH({token:f,factory:function(){return new mr},providedIn:"root"})}}return f})();class mr{parse(p){const u=new Wt(p);return new wn(u.parseRootSegment(),u.parseQueryParams(),u.parseFragment())}serialize(p){const u=`/${K(p.root,!0)}`,m=function zn(f){const p=Object.keys(f).map(u=>{const m=f[u];return Array.isArray(m)?m.map(C=>`${z(u)}=${z(C)}`).join("&"):`${z(u)}=${z(m)}`}).filter(u=>!!u);return p.length?`?${p.join("&")}`:""}(p.queryParams);return`${u}${m}${"string"==typeof p.fragment?`#${function me(f){return encodeURI(f)}(p.fragment)}`:""}`}}const Mr=new mr;function ce(f){return f.segments.map(p=>Pt(p)).join("/")}function K(f,p){if(!f.hasChildren())return ce(f);if(p){const u=f.children[it]?K(f.children[it],!1):"",m=[];return Object.entries(f.children).forEach(([C,O])=>{C!==it&&m.push(`${C}:${K(O,!1)}`)}),m.length>0?`${u}(${m.join("//")})`:u}{const u=function br(f,p){let u=[];return Object.entries(f.children).forEach(([m,C])=>{m===it&&(u=u.concat(p(C,m)))}),Object.entries(f.children).forEach(([m,C])=>{m!==it&&(u=u.concat(p(C,m)))}),u}(f,(m,C)=>C===it?[K(f.children[it],!1)]:[`${C}:${K(m,!1)}`]);return 1===Object.keys(f.children).length&&null!=f.children[it]?`${ce(f)}/${u[0]}`:`${ce(f)}/(${u.join("//")})`}}function V(f){return encodeURIComponent(f).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function z(f){return V(f).replace(/%3B/gi,";")}function Fe(f){return V(f).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Ye(f){return decodeURIComponent(f)}function bt(f){return Ye(f.replace(/\+/g,"%20"))}function Pt(f){return`${Fe(f.path)}${function Yt(f){return Object.keys(f).map(p=>`;${Fe(p)}=${Fe(f[p])}`).join("")}(f.parameters)}`}const Sr=/^[^\/()?;#]+/;function G(f){const p=f.match(Sr);return p?p[0]:""}const j=/^[^\/()?;=#]+/,fe=/^[^=?&#]+/,Be=/^[^&#]+/;class Wt{constructor(p){this.url=p,this.remaining=p}parseRootSegment(){return this.consumeOptional("/"),""===this.remaining||this.peekStartsWith("?")||this.peekStartsWith("#")?new Ze([],{}):new Ze([],this.parseChildren())}parseQueryParams(){const p={};if(this.consumeOptional("?"))do{this.parseQueryParam(p)}while(this.consumeOptional("&"));return p}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(""===this.remaining)return{};this.consumeOptional("/");const p=[];for(this.peekStartsWith("(")||p.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),p.push(this.parseSegment());let u={};this.peekStartsWith("/(")&&(this.capture("/"),u=this.parseParens(!0));let m={};return this.peekStartsWith("(")&&(m=this.parseParens(!1)),(p.length>0||Object.keys(u).length>0)&&(m[it]=new Ze(p,u)),m}parseSegment(){const p=G(this.remaining);if(""===p&&this.peekStartsWith(";"))throw new d.wOt(4009,!1);return this.capture(p),new Wr(Ye(p),this.parseMatrixParams())}parseMatrixParams(){const p={};for(;this.consumeOptional(";");)this.parseParam(p);return p}parseParam(p){const u=function $(f){const p=f.match(j);return p?p[0]:""}(this.remaining);if(!u)return;this.capture(u);let m="";if(this.consumeOptional("=")){const C=G(this.remaining);C&&(m=C,this.capture(m))}p[Ye(u)]=Ye(m)}parseQueryParam(p){const u=function Se(f){const p=f.match(fe);return p?p[0]:""}(this.remaining);if(!u)return;this.capture(u);let m="";if(this.consumeOptional("=")){const F=function Oe(f){const p=f.match(Be);return p?p[0]:""}(this.remaining);F&&(m=F,this.capture(m))}const C=bt(u),O=bt(m);if(p.hasOwnProperty(C)){let F=p[C];Array.isArray(F)||(F=[F],p[C]=F),F.push(O)}else p[C]=O}parseParens(p){const u={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){const m=G(this.remaining),C=this.remaining[m.length];if("/"!==C&&")"!==C&&";"!==C)throw new d.wOt(4010,!1);let O;m.indexOf(":")>-1?(O=m.slice(0,m.indexOf(":")),this.capture(O),this.capture(":")):p&&(O=it);const F=this.parseChildren();u[O]=1===Object.keys(F).length?F[it]:new Ze([],F),this.consumeOptional("//")}return u}peekStartsWith(p){return this.remaining.startsWith(p)}consumeOptional(p){return!!this.peekStartsWith(p)&&(this.remaining=this.remaining.substring(p.length),!0)}capture(p){if(!this.consumeOptional(p))throw new d.wOt(4011,!1)}}function ke(f){return f.segments.length>0?new Ze([],{[it]:f}):f}function rn(f){const p={};for(const m of Object.keys(f.children)){const O=rn(f.children[m]);if(m===it&&0===O.segments.length&&O.hasChildren())for(const[F,te]of Object.entries(O.children))p[F]=te;else(O.segments.length>0||O.hasChildren())&&(p[m]=O)}return function ir(f){if(1===f.numberOfChildren&&f.children[it]){const p=f.children[it];return new Ze(f.segments.concat(p.segments),p.children)}return f}(new Ze(f.segments,p))}function Jt(f){return f instanceof wn}function Kr(f){let p;const C=ke(function u(O){const F={};for(const ee of O.children){const $e=u(ee);F[ee.outlet]=$e}const te=new Ze(O.url,F);return O===f&&(p=te),te}(f.root));return p??C}function yr(f,p,u,m){let C=f;for(;C.parent;)C=C.parent;if(0===p.length)return vn(C,C,C,u,m);const O=function or(f){if("string"==typeof f[0]&&1===f.length&&"/"===f[0])return new pt(!0,0,f);let p=0,u=!1;const m=f.reduce((C,O,F)=>{if("object"==typeof O&&null!=O){if(O.outlets){const te={};return Object.entries(O.outlets).forEach(([ee,$e])=>{te[ee]="string"==typeof $e?$e.split("/"):$e}),[...C,{outlets:te}]}if(O.segmentPath)return[...C,O.segmentPath]}return"string"!=typeof O?[...C,O]:0===F?(O.split("/").forEach((te,ee)=>{0==ee&&"."===te||(0==ee&&""===te?u=!0:".."===te?p++:""!=te&&C.push(te))}),C):[...C,O]},[]);return new pt(u,p,m)}(p);if(O.toRoot())return vn(C,C,new Ze([],{}),u,m);const F=function ni(f,p,u){if(f.isAbsolute)return new Ir(p,!0,0);if(!u)return new Ir(p,!1,NaN);if(null===u.parent)return new Ir(u,!0,0);const m=dt(f.commands[0])?0:1;return function Tr(f,p,u){let m=f,C=p,O=u;for(;O>C;){if(O-=C,m=m.parent,!m)throw new d.wOt(4005,!1);C=m.segments.length}return new Ir(m,!1,C-O)}(u,u.segments.length-1+m,f.numberOfDoubleDots)}(O,C,f),te=F.processChildren?Lr(F.segmentGroup,F.index,O.commands):ri(F.segmentGroup,F.index,O.commands);return vn(C,F.segmentGroup,te,u,m)}function dt(f){return"object"==typeof f&&null!=f&&!f.outlets&&!f.segmentPath}function Rt(f){return"object"==typeof f&&null!=f&&f.outlets}function vn(f,p,u,m,C){let F,O={};m&&Object.entries(m).forEach(([ee,$e])=>{O[ee]=Array.isArray($e)?$e.map(wt=>`${wt}`):`${$e}`}),F=f===p?u:Gn(f,p,u);const te=ke(rn(F));return new wn(te,O,C)}function Gn(f,p,u){const m={};return Object.entries(f.children).forEach(([C,O])=>{m[C]=O===p?u:Gn(O,p,u)}),new Ze(f.segments,m)}class pt{constructor(p,u,m){if(this.isAbsolute=p,this.numberOfDoubleDots=u,this.commands=m,p&&m.length>0&&dt(m[0]))throw new d.wOt(4003,!1);const C=m.find(Rt);if(C&&C!==kn(m))throw new d.wOt(4004,!1)}toRoot(){return this.isAbsolute&&1===this.commands.length&&"/"==this.commands[0]}}class Ir{constructor(p,u,m){this.segmentGroup=p,this.processChildren=u,this.index=m}}function ri(f,p,u){if(f||(f=new Ze([],{})),0===f.segments.length&&f.hasChildren())return Lr(f,p,u);const m=function sr(f,p,u){let m=0,C=p;const O={match:!1,pathIndex:0,commandIndex:0};for(;C<f.segments.length;){if(m>=u.length)return O;const F=f.segments[C],te=u[m];if(Rt(te))break;const ee=`${te}`,$e=m<u.length-1?u[m+1]:null;if(C>0&&void 0===ee)break;if(ee&&$e&&"object"==typeof $e&&void 0===$e.outlets){if(!ar(ee,$e,F))return O;m+=2}else{if(!ar(ee,{},F))return O;m++}C++}return{match:!0,pathIndex:C,commandIndex:m}}(f,p,u),C=u.slice(m.commandIndex);if(m.match&&m.pathIndex<f.segments.length){const O=new Ze(f.segments.slice(0,m.pathIndex),{});return O.children[it]=new Ze(f.segments.slice(m.pathIndex),f.children),Lr(O,0,C)}return m.match&&0===C.length?new Ze(f.segments,{}):m.match&&!f.hasChildren()?Dr(f,p,u):m.match?Lr(f,0,C):Dr(f,p,u)}function Lr(f,p,u){if(0===u.length)return new Ze(f.segments,{});{const m=function vr(f){return Rt(f[0])?f[0].outlets:{[it]:f}}(u),C={};if(Object.keys(m).some(O=>O!==it)&&f.children[it]&&1===f.numberOfChildren&&0===f.children[it].segments.length){const O=Lr(f.children[it],p,u);return new Ze(f.segments,O.children)}return Object.entries(m).forEach(([O,F])=>{"string"==typeof F&&(F=[F]),null!==F&&(C[O]=ri(f.children[O],p,F))}),Object.entries(f.children).forEach(([O,F])=>{void 0===m[O]&&(C[O]=F)}),new Ze(f.segments,C)}}function Dr(f,p,u){const m=f.segments.slice(0,p);let C=0;for(;C<u.length;){const O=u[C];if(Rt(O)){const ee=Ri(O.outlets);return new Ze(m,ee)}if(0===C&&dt(u[0])){m.push(new Wr(f.segments[p].path,xi(u[0]))),C++;continue}const F=Rt(O)?O.outlets[it]:`${O}`,te=C<u.length-1?u[C+1]:null;F&&te&&dt(te)?(m.push(new Wr(F,xi(te))),C+=2):(m.push(new Wr(F,{})),C++)}return new Ze(m,{})}function Ri(f){const p={};return Object.entries(f).forEach(([u,m])=>{"string"==typeof m&&(m=[m]),null!==m&&(p[u]=Dr(new Ze([],{}),0,m))}),p}function xi(f){const p={};return Object.entries(f).forEach(([u,m])=>p[u]=`${m}`),p}function ar(f,p,u){return f==u.path&&En(p,u.parameters)}const ii="imperative";class lr{constructor(p,u){this.id=p,this.url=u}}class Fi extends lr{constructor(p,u,m="imperative",C=null){super(p,u),this.type=0,this.navigationTrigger=m,this.restoredState=C}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}}class Vr extends lr{constructor(p,u,m){super(p,u),this.urlAfterRedirects=m,this.type=1}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}}class Wn extends lr{constructor(p,u,m,C){super(p,u),this.reason=m,this.code=C,this.type=2}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}}class vi extends lr{constructor(p,u,m,C){super(p,u),this.reason=m,this.code=C,this.type=16}}class Zn extends lr{constructor(p,u,m,C){super(p,u),this.error=m,this.target=C,this.type=3}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}}class ao extends lr{constructor(p,u,m,C){super(p,u),this.urlAfterRedirects=m,this.state=C,this.type=4}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Ar extends lr{constructor(p,u,m,C){super(p,u),this.urlAfterRedirects=m,this.state=C,this.type=7}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Qi extends lr{constructor(p,u,m,C,O){super(p,u),this.urlAfterRedirects=m,this.state=C,this.shouldActivate=O,this.type=8}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}}class ki extends lr{constructor(p,u,m,C){super(p,u),this.urlAfterRedirects=m,this.state=C,this.type=5}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class oi extends lr{constructor(p,u,m,C){super(p,u),this.urlAfterRedirects=m,this.state=C,this.type=6}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class qi{constructor(p){this.route=p,this.type=9}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}}class lo{constructor(p){this.route=p,this.type=10}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}}class Zi{constructor(p){this.snapshot=p,this.type=11}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class bo{constructor(p){this.snapshot=p,this.type=12}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class Bo{constructor(p){this.snapshot=p,this.type=13}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class si{constructor(p){this.snapshot=p,this.type=14}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class uo{constructor(p,u,m){this.routerEvent=p,this.position=u,this.anchor=m,this.type=15}toString(){return`Scroll(anchor: '${this.anchor}', position: '${this.position?`${this.position[0]}, ${this.position[1]}`:null}')`}}class jr{}class Di{constructor(p){this.url=p}}class Ji{constructor(){this.outlet=null,this.route=null,this.injector=null,this.children=new Xr,this.attachRef=null}}let Xr=(()=>{class f{constructor(){this.contexts=new Map}onChildOutletCreated(u,m){const C=this.getOrCreateContext(u);C.outlet=m,this.contexts.set(u,C)}onChildOutletDestroyed(u){const m=this.getContext(u);m&&(m.outlet=null,m.attachRef=null)}onOutletDeactivated(){const u=this.contexts;return this.contexts=new Map,u}onOutletReAttached(u){this.contexts=u}getOrCreateContext(u){let m=this.getContext(u);return m||(m=new Ji,this.contexts.set(u,m)),m}getContext(u){return this.contexts.get(u)||null}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=d.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();class Ci{constructor(p){this._root=p}get root(){return this._root.value}parent(p){const u=this.pathFromRoot(p);return u.length>1?u[u.length-2]:null}children(p){const u=Yi(p,this._root);return u?u.children.map(m=>m.value):[]}firstChild(p){const u=Yi(p,this._root);return u&&u.children.length>0?u.children[0].value:null}siblings(p){const u=Mt(p,this._root);return u.length<2?[]:u[u.length-2].children.map(C=>C.value).filter(C=>C!==p)}pathFromRoot(p){return Mt(p,this._root).map(u=>u.value)}}function Yi(f,p){if(f===p.value)return p;for(const u of p.children){const m=Yi(f,u);if(m)return m}return null}function Mt(f,p){if(f===p.value)return[p];for(const u of p.children){const m=Mt(f,u);if(m.length)return m.unshift(p),m}return[]}class on{constructor(p,u){this.value=p,this.children=u}toString(){return`TreeNode(${this.value})`}}function sn(f){const p={};return f&&f.children.forEach(u=>p[u.value.outlet]=u),p}class Or extends Ci{constructor(p,u){super(p),this.snapshot=u,Le(this,p)}toString(){return this.snapshot.toString()}}function bn(f,p){const u=function Li(f,p){const F=new _i([],{},{},"",{},it,p,null,{});return new ln("",new on(F,[]))}(0,p),m=new ie.t([new Wr("",{})]),C=new ie.t({}),O=new ie.t({}),F=new ie.t({}),te=new ie.t(""),ee=new Nr(m,C,F,te,O,it,p,u.root);return ee.snapshot=u.root,new Or(new on(ee,[]),u)}class Nr{constructor(p,u,m,C,O,F,te,ee){this.urlSubject=p,this.paramsSubject=u,this.queryParamsSubject=m,this.fragmentSubject=C,this.dataSubject=O,this.outlet=F,this.component=te,this._futureSnapshot=ee,this.title=this.dataSubject?.pipe((0,je.T)($e=>$e[Yn]))??(0,oe.of)(void 0),this.url=p,this.params=u,this.queryParams=m,this.fragment=C,this.data=O}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=this.params.pipe((0,je.T)(p=>Tn(p)))),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=this.queryParams.pipe((0,je.T)(p=>Tn(p)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}}function Qr(f,p="emptyOnly"){const u=f.pathFromRoot;let m=0;if("always"!==p)for(m=u.length-1;m>=1;){const C=u[m],O=u[m-1];if(C.routeConfig&&""===C.routeConfig.path)m--;else{if(O.component)break;m--}}return function Mo(f){return f.reduce((p,u)=>({params:{...p.params,...u.params},data:{...p.data,...u.data},resolve:{...u.data,...p.resolve,...u.routeConfig?.data,...u._resolvedData}}),{params:{},data:{},resolve:{}})}(u.slice(m))}class _i{get title(){return this.data?.[Yn]}constructor(p,u,m,C,O,F,te,ee,$e){this.url=p,this.params=u,this.queryParams=m,this.fragment=C,this.data=O,this.outlet=F,this.component=te,this.routeConfig=ee,this._resolve=$e}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=Tn(this.params)),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=Tn(this.queryParams)),this._queryParamMap}toString(){return`Route(url:'${this.url.map(m=>m.toString()).join("/")}', path:'${this.routeConfig?this.routeConfig.path:""}')`}}class ln extends Ci{constructor(p,u){super(u),this.url=p,Le(this,u)}toString(){return mt(this._root)}}function Le(f,p){p.value._routerState=f,p.children.forEach(u=>Le(f,u))}function mt(f){const p=f.children.length>0?` { ${f.children.map(mt).join(", ")} } `:"";return`${f.value}${p}`}function nn(f){if(f.snapshot){const p=f.snapshot,u=f._futureSnapshot;f.snapshot=u,En(p.queryParams,u.queryParams)||f.queryParamsSubject.next(u.queryParams),p.fragment!==u.fragment&&f.fragmentSubject.next(u.fragment),En(p.params,u.params)||f.paramsSubject.next(u.params),function Ct(f,p){if(f.length!==p.length)return!1;for(let u=0;u<f.length;++u)if(!En(f[u],p[u]))return!1;return!0}(p.url,u.url)||f.urlSubject.next(u.url),En(p.data,u.data)||f.dataSubject.next(u.data)}else f.snapshot=f._futureSnapshot,f.dataSubject.next(f._futureSnapshot.data)}function Kn(f,p){const u=En(f.params,p.params)&&function yi(f,p){return ot(f,p)&&f.every((u,m)=>En(u.parameters,p[m].parameters))}(f.url,p.url);return u&&!(!f.parent!=!p.parent)&&(!f.parent||Kn(f.parent,p.parent))}let li=(()=>{class f{constructor(){this.activated=null,this._activatedRoute=null,this.name=it,this.activateEvents=new d.bkB,this.deactivateEvents=new d.bkB,this.attachEvents=new d.bkB,this.detachEvents=new d.bkB,this.parentContexts=(0,d.WQX)(Xr),this.location=(0,d.WQX)(d.c1b),this.changeDetector=(0,d.WQX)(d.gRc),this.environmentInjector=(0,d.WQX)(d.uvJ),this.inputBinder=(0,d.WQX)(tr,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(u){if(u.name){const{firstChange:m,previousValue:C}=u.name;if(m)return;this.isTrackedInParentContexts(C)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(C)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(u){return this.parentContexts.getContext(u)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;const u=this.parentContexts.getContext(this.name);u?.route&&(u.attachRef?this.attach(u.attachRef,u.route):this.activateWith(u.route,u.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new d.wOt(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new d.wOt(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new d.wOt(4012,!1);this.location.detach();const u=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(u.instance),u}attach(u,m){this.activated=u,this._activatedRoute=m,this.location.insert(u.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(u.instance)}deactivate(){if(this.activated){const u=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(u)}}activateWith(u,m){if(this.isActivated)throw new d.wOt(4013,!1);this._activatedRoute=u;const C=this.location,F=u.snapshot.component,te=this.parentContexts.getOrCreateContext(this.name).children,ee=new Ln(u,te,C.injector);this.activated=C.createComponent(F,{index:C.length,injector:ee,environmentInjector:m??this.environmentInjector}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275dir=d.FsC({type:f,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[d.OA$]})}}return f})();class Ln{constructor(p,u,m){this.route=p,this.childContexts=u,this.parent=m}get(p,u){return p===Nr?this.route:p===Xr?this.childContexts:this.parent.get(p,u)}}const tr=new d.nKC("");let un=(()=>{class f{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(u){this.unsubscribeFromRouteData(u),this.subscribeToRouteData(u)}unsubscribeFromRouteData(u){this.outletDataSubscriptions.get(u)?.unsubscribe(),this.outletDataSubscriptions.delete(u)}subscribeToRouteData(u){const{activatedRoute:m}=u,C=Ge([m.queryParams,m.params,m.data]).pipe((0,ft.n)(([O,F,te],ee)=>(te={...O,...F,...te},0===ee?(0,oe.of)(te):Promise.resolve(te)))).subscribe(O=>{if(!u.isActivated||!u.activatedComponentRef||u.activatedRoute!==m||null===m.component)return void this.unsubscribeFromRouteData(u);const F=(0,d.HJs)(m.component);if(F)for(const{templateName:te}of F.inputs)u.activatedComponentRef.setInput(te,O[te]);else this.unsubscribeFromRouteData(u)});this.outletDataSubscriptions.set(u,C)}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=d.jDH({token:f,factory:f.\u0275fac})}}return f})();function ur(f,p,u){if(u&&f.shouldReuseRoute(p.value,u.value.snapshot)){const m=u.value;m._futureSnapshot=p.value;const C=function lt(f,p,u){return p.children.map(m=>{for(const C of u.children)if(f.shouldReuseRoute(m.value,C.value.snapshot))return ur(f,m,C);return ur(f,m)})}(f,p,u);return new on(m,C)}{if(f.shouldAttach(p.value)){const O=f.retrieve(p.value);if(null!==O){const F=O.route;return F.value._futureSnapshot=p.value,F.children=p.children.map(te=>ur(f,te)),F}}const m=function qr(f){return new Nr(new ie.t(f.url),new ie.t(f.params),new ie.t(f.queryParams),new ie.t(f.fragment),new ie.t(f.data),f.outlet,f.component,f)}(p.value),C=p.children.map(O=>ur(f,O));return new on(m,C)}}const Ei="ngNavigationCancelingError";function Ur(f,p){const{redirectTo:u,navigationBehaviorOptions:m}=Jt(p)?{redirectTo:p,navigationBehaviorOptions:void 0}:p,C=an(!1,0,p);return C.url=u,C.navigationBehaviorOptions=m,C}function an(f,p,u){const m=new Error("NavigationCancelingError: "+(f||""));return m[Ei]=!0,m.cancellationCode=p,u&&(m.url=u),m}function nr(f){return f&&f[Ei]}let Sn=(()=>{class f{static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275cmp=d.VBU({type:f,selectors:[["ng-component"]],standalone:!0,features:[d.aNF],decls:1,vars:0,template:function(m,C){1&m&&d.nrm(0,"router-outlet")},dependencies:[li],encapsulation:2})}}return f})();function eo(f){const p=f.children&&f.children.map(eo),u=p?{...f,children:p}:{...f};return!u.component&&!u.loadComponent&&(p||u.loadChildren)&&u.outlet&&u.outlet!==it&&(u.component=Sn),u}function gn(f){return f.outlet||it}function Nn(f){if(!f)return null;if(f.routeConfig?._injector)return f.routeConfig._injector;for(let p=f.parent;p;p=p.parent){const u=p.routeConfig;if(u?._loadedInjector)return u._loadedInjector;if(u?._injector)return u._injector}return null}class Ho{constructor(p,u,m,C,O){this.routeReuseStrategy=p,this.futureState=u,this.currState=m,this.forwardEvent=C,this.inputBindingEnabled=O}activate(p){const u=this.futureState._root,m=this.currState?this.currState._root:null;this.deactivateChildRoutes(u,m,p),nn(this.futureState.root),this.activateChildRoutes(u,m,p)}deactivateChildRoutes(p,u,m){const C=sn(u);p.children.forEach(O=>{const F=O.value.outlet;this.deactivateRoutes(O,C[F],m),delete C[F]}),Object.values(C).forEach(O=>{this.deactivateRouteAndItsChildren(O,m)})}deactivateRoutes(p,u,m){const C=p.value,O=u?u.value:null;if(C===O)if(C.component){const F=m.getContext(C.outlet);F&&this.deactivateChildRoutes(p,u,F.children)}else this.deactivateChildRoutes(p,u,m);else O&&this.deactivateRouteAndItsChildren(u,m)}deactivateRouteAndItsChildren(p,u){p.value.component&&this.routeReuseStrategy.shouldDetach(p.value.snapshot)?this.detachAndStoreRouteSubtree(p,u):this.deactivateRouteAndOutlet(p,u)}detachAndStoreRouteSubtree(p,u){const m=u.getContext(p.value.outlet),C=m&&p.value.component?m.children:u,O=sn(p);for(const F of Object.keys(O))this.deactivateRouteAndItsChildren(O[F],C);if(m&&m.outlet){const F=m.outlet.detach(),te=m.children.onOutletDeactivated();this.routeReuseStrategy.store(p.value.snapshot,{componentRef:F,route:p,contexts:te})}}deactivateRouteAndOutlet(p,u){const m=u.getContext(p.value.outlet),C=m&&p.value.component?m.children:u,O=sn(p);for(const F of Object.keys(O))this.deactivateRouteAndItsChildren(O[F],C);m&&(m.outlet&&(m.outlet.deactivate(),m.children.onOutletDeactivated()),m.attachRef=null,m.route=null)}activateChildRoutes(p,u,m){const C=sn(u);p.children.forEach(O=>{this.activateRoutes(O,C[O.value.outlet],m),this.forwardEvent(new si(O.value.snapshot))}),p.children.length&&this.forwardEvent(new bo(p.value.snapshot))}activateRoutes(p,u,m){const C=p.value,O=u?u.value:null;if(nn(C),C===O)if(C.component){const F=m.getOrCreateContext(C.outlet);this.activateChildRoutes(p,u,F.children)}else this.activateChildRoutes(p,u,m);else if(C.component){const F=m.getOrCreateContext(C.outlet);if(this.routeReuseStrategy.shouldAttach(C.snapshot)){const te=this.routeReuseStrategy.retrieve(C.snapshot);this.routeReuseStrategy.store(C.snapshot,null),F.children.onOutletReAttached(te.contexts),F.attachRef=te.componentRef,F.route=te.route.value,F.outlet&&F.outlet.attach(te.componentRef,te.route.value),nn(te.route.value),this.activateChildRoutes(p,null,F.children)}else{const te=Nn(C.snapshot);F.attachRef=null,F.route=C,F.injector=te,F.outlet&&F.outlet.activateWith(C,F.injector),this.activateChildRoutes(p,null,F.children)}}else this.activateChildRoutes(p,null,m)}}class Pn{constructor(p){this.path=p,this.route=this.path[this.path.length-1]}}class Dn{constructor(p,u){this.component=p,this.route=u}}function Bi(f,p,u){const m=f._root;return Rn(m,p?p._root:null,u,[m.value])}function Bt(f,p){const u=Symbol(),m=p.get(f,u);return m===u?"function"!=typeof f||(0,d.LfX)(f)?p.get(f):f:m}function Rn(f,p,u,m,C={canDeactivateChecks:[],canActivateChecks:[]}){const O=sn(p);return f.children.forEach(F=>{(function co(f,p,u,m,C={canDeactivateChecks:[],canActivateChecks:[]}){const O=f.value,F=p?p.value:null,te=u?u.getContext(f.value.outlet):null;if(F&&O.routeConfig===F.routeConfig){const ee=function b(f,p,u){if("function"==typeof u)return u(f,p);switch(u){case"pathParamsChange":return!ot(f.url,p.url);case"pathParamsOrQueryParamsChange":return!ot(f.url,p.url)||!En(f.queryParams,p.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Kn(f,p)||!En(f.queryParams,p.queryParams);default:return!Kn(f,p)}}(F,O,O.routeConfig.runGuardsAndResolvers);ee?C.canActivateChecks.push(new Pn(m)):(O.data=F.data,O._resolvedData=F._resolvedData),Rn(f,p,O.component?te?te.children:null:u,m,C),ee&&te&&te.outlet&&te.outlet.isActivated&&C.canDeactivateChecks.push(new Dn(te.outlet.component,F))}else F&&S(p,te,C),C.canActivateChecks.push(new Pn(m)),Rn(f,null,O.component?te?te.children:null:u,m,C)})(F,O[F.value.outlet],u,m.concat([F.value]),C),delete O[F.value.outlet]}),Object.entries(O).forEach(([F,te])=>S(te,u.getContext(F),C)),C}function S(f,p,u){const m=sn(f),C=f.value;Object.entries(m).forEach(([O,F])=>{S(F,C.component?p?p.children.getContext(O):null:p,u)}),u.canDeactivateChecks.push(new Dn(C.component&&p&&p.outlet&&p.outlet.isActivated?p.outlet.component:null,C))}function v(f){return"function"==typeof f}function to(f){return f instanceof jt||"EmptyError"===f?.name}const Si=Symbol("INITIAL_VALUE");function Jr(){return(0,ft.n)(f=>Ge(f.map(p=>p.pipe(Fn(1),function Fr(...f){const p=(0,Z.lI)(f);return(0,Ft.N)((u,m)=>{(p?Te(f,u,p):Te(f,u)).subscribe(m)})}(Si)))).pipe((0,je.T)(p=>{for(const u of p)if(!0!==u){if(u===Si)return Si;if(!1===u||u instanceof wn)return u}return!0}),(0,Q.p)(p=>p!==Si),Fn(1)))}function Ii(f){return(0,Re.F)((0,ct.M)(p=>{if(Jt(p))throw Ur(0,p)}),(0,je.T)(p=>!0===p))}class To{constructor(p){this.segmentGroup=p||null}}class cn{constructor(p){this.urlTree=p}}function cr(f){return(0,Ae.$)(new To(f))}function _r(f){return(0,Ae.$)(new cn(f))}class Ao{constructor(p,u){this.urlSerializer=p,this.urlTree=u}noMatchError(p){return new d.wOt(4002,!1)}lineralizeSegments(p,u){let m=[],C=u.root;for(;;){if(m=m.concat(C.segments),0===C.numberOfChildren)return(0,oe.of)(m);if(C.numberOfChildren>1||!C.children[it])return(0,Ae.$)(new d.wOt(4e3,!1));C=C.children[it]}}applyRedirectCommands(p,u,m){return this.applyRedirectCreateUrlTree(u,this.urlSerializer.parse(u),p,m)}applyRedirectCreateUrlTree(p,u,m,C){const O=this.createSegmentGroup(p,u.root,m,C);return new wn(O,this.createQueryParams(u.queryParams,this.urlTree.queryParams),u.fragment)}createQueryParams(p,u){const m={};return Object.entries(p).forEach(([C,O])=>{if("string"==typeof O&&O.startsWith(":")){const te=O.substring(1);m[C]=u[te]}else m[C]=O}),m}createSegmentGroup(p,u,m,C){const O=this.createSegments(p,u.segments,m,C);let F={};return Object.entries(u.children).forEach(([te,ee])=>{F[te]=this.createSegmentGroup(p,ee,m,C)}),new Ze(O,F)}createSegments(p,u,m,C){return u.map(O=>O.path.startsWith(":")?this.findPosParam(p,O,C):this.findOrReturn(O,m))}findPosParam(p,u,m){const C=m[u.path.substring(1)];if(!C)throw new d.wOt(4001,!1);return C}findOrReturn(p,u){let m=0;for(const C of u){if(C.path===p.path)return u.splice(m),C;m++}return p}}const ho={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Oo(f,p,u,m,C){const O=No(f,p,u);return O.matched?(m=function Zr(f,p){return f.providers&&!f._injector&&(f._injector=(0,d.Ol2)(f.providers,p,`Route: ${f.path}`)),f._injector??p}(p,m),function bs(f,p,u,m){const C=p.canMatch;if(!C||0===C.length)return(0,oe.of)(!0);const O=C.map(F=>{const te=Bt(F,f);return Nt(function ui(f){return f&&v(f.canMatch)}(te)?te.canMatch(p,u):f.runInContext(()=>te(p,u)))});return(0,oe.of)(O).pipe(Jr(),Ii())}(m,p,u).pipe((0,je.T)(F=>!0===F?O:{...ho}))):(0,oe.of)(O)}function No(f,p,u){if(""===p.path)return"full"===p.pathMatch&&(f.hasChildren()||u.length>0)?{...ho}:{matched:!0,consumedSegments:[],remainingSegments:u,parameters:{},positionalParamSegments:{}};const C=(p.matcher||ht)(u,f,p);if(!C)return{...ho};const O={};Object.entries(C.posParams??{}).forEach(([te,ee])=>{O[te]=ee.path});const F=C.consumed.length>0?{...O,...C.consumed[C.consumed.length-1].parameters}:O;return{matched:!0,consumedSegments:C.consumed,remainingSegments:u.slice(C.consumed.length),parameters:F,positionalParamSegments:C.posParams??{}}}function Po(f,p,u,m){return u.length>0&&function is(f,p,u){return u.some(m=>Hi(f,p,m)&&gn(m)!==it)}(f,u,m)?{segmentGroup:new Ze(p,rs(m,new Ze(u,f.children))),slicedSegments:[]}:0===u.length&&function os(f,p,u){return u.some(m=>Hi(f,p,m))}(f,u,m)?{segmentGroup:new Ze(f.segments,Go(f,0,u,m,f.children)),slicedSegments:u}:{segmentGroup:new Ze(f.segments,f.children),slicedSegments:u}}function Go(f,p,u,m,C){const O={};for(const F of m)if(Hi(f,u,F)&&!C[gn(F)]){const te=new Ze([],{});O[gn(F)]=te}return{...C,...O}}function rs(f,p){const u={};u[it]=p;for(const m of f)if(""===m.path&&gn(m)!==it){const C=new Ze([],{});u[gn(m)]=C}return u}function Hi(f,p,u){return(!(f.hasChildren()||p.length>0)||"full"!==u.pathMatch)&&""===u.path}class Ro{constructor(p,u,m,C,O,F,te){this.injector=p,this.configLoader=u,this.rootComponentType=m,this.config=C,this.urlTree=O,this.paramsInheritanceStrategy=F,this.urlSerializer=te,this.allowRedirects=!0,this.applyRedirects=new Ao(this.urlSerializer,this.urlTree)}noMatchError(p){return new d.wOt(4002,!1)}recognize(){const p=Po(this.urlTree.root,[],[],this.config).segmentGroup;return this.processSegmentGroup(this.injector,this.config,p,it).pipe((0,zt.W)(u=>{if(u instanceof cn)return this.allowRedirects=!1,this.urlTree=u.urlTree,this.match(u.urlTree);throw u instanceof To?this.noMatchError(u):u}),(0,je.T)(u=>{const m=new _i([],Object.freeze({}),Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,{},it,this.rootComponentType,null,{}),C=new on(m,u),O=new ln("",C),F=function er(f,p,u=null,m=null){return yr(Kr(f),p,u,m)}(m,[],this.urlTree.queryParams,this.urlTree.fragment);return F.queryParams=this.urlTree.queryParams,O.url=this.urlSerializer.serialize(F),this.inheritParamsAndData(O._root),{state:O,tree:F}}))}match(p){return this.processSegmentGroup(this.injector,this.config,p.root,it).pipe((0,zt.W)(m=>{throw m instanceof To?this.noMatchError(m):m}))}inheritParamsAndData(p){const u=p.value,m=Qr(u,this.paramsInheritanceStrategy);u.params=Object.freeze(m.params),u.data=Object.freeze(m.data),p.children.forEach(C=>this.inheritParamsAndData(C))}processSegmentGroup(p,u,m,C){return 0===m.segments.length&&m.hasChildren()?this.processChildren(p,u,m):this.processSegment(p,u,m,m.segments,C,!0)}processChildren(p,u,m){const C=[];for(const O of Object.keys(m.children))"primary"===O?C.unshift(O):C.push(O);return(0,X.H)(C).pipe((0,We.H)(O=>{const F=m.children[O],te=function Xn(f,p){const u=f.filter(m=>gn(m)===p);return u.push(...f.filter(m=>gn(m)!==p)),u}(u,O);return this.processSegmentGroup(p,te,F,O)}),function kt(f,p){return(0,Ft.N)(function at(f,p,u,m,C){return(O,F)=>{let te=u,ee=p,$e=0;O.subscribe((0,be._)(F,wt=>{const dn=$e++;ee=te?f(ee,wt,dn):(te=!0,wt),m&&F.next(ee)},C&&(()=>{te&&F.next(ee),F.complete()})))}}(f,p,arguments.length>=2,!0))}((O,F)=>(O.push(...F),O)),J(null),function Jn(f,p){const u=arguments.length>=2;return m=>m.pipe(f?(0,Q.p)((C,O)=>f(C,O,m)):le.D,qt(1),u?J(p):se(()=>new jt))}(),(0,Y.Z)(O=>{if(null===O)return cr(m);const F=T(O);return function ha(f){f.sort((p,u)=>p.value.outlet===it?-1:u.value.outlet===it?1:p.value.outlet.localeCompare(u.value.outlet))}(F),(0,oe.of)(F)}))}processSegment(p,u,m,C,O,F){return(0,X.H)(u).pipe((0,We.H)(te=>this.processSegmentAgainstRoute(te._injector??p,u,te,m,C,O,F).pipe((0,zt.W)(ee=>{if(ee instanceof To)return(0,oe.of)(null);throw ee}))),we(te=>!!te),(0,zt.W)(te=>{if(to(te))return function ro(f,p,u){return 0===p.length&&!f.children[u]}(m,C,O)?(0,oe.of)([]):cr(m);throw te}))}processSegmentAgainstRoute(p,u,m,C,O,F,te){return function po(f,p,u,m){return!!(gn(f)===m||m!==it&&Hi(p,u,f))&&("**"===f.path||No(p,f,u).matched)}(m,C,O,F)?void 0===m.redirectTo?this.matchSegmentAgainstRoute(p,C,m,O,F,te):te&&this.allowRedirects?this.expandSegmentAgainstRouteUsingRedirect(p,C,u,m,O,F):cr(C):cr(C)}expandSegmentAgainstRouteUsingRedirect(p,u,m,C,O,F){return"**"===C.path?this.expandWildCardWithParamsAgainstRouteUsingRedirect(p,m,C,F):this.expandRegularSegmentAgainstRouteUsingRedirect(p,u,m,C,O,F)}expandWildCardWithParamsAgainstRouteUsingRedirect(p,u,m,C){const O=this.applyRedirects.applyRedirectCommands([],m.redirectTo,{});return m.redirectTo.startsWith("/")?_r(O):this.applyRedirects.lineralizeSegments(m,O).pipe((0,Y.Z)(F=>{const te=new Ze(F,{});return this.processSegment(p,u,te,F,C,!1)}))}expandRegularSegmentAgainstRouteUsingRedirect(p,u,m,C,O,F){const{matched:te,consumedSegments:ee,remainingSegments:$e,positionalParamSegments:wt}=No(u,C,O);if(!te)return cr(u);const dn=this.applyRedirects.applyRedirectCommands(ee,C.redirectTo,wt);return C.redirectTo.startsWith("/")?_r(dn):this.applyRedirects.lineralizeSegments(C,dn).pipe((0,Y.Z)(Xt=>this.processSegment(p,m,u,Xt.concat($e),F,!1)))}matchSegmentAgainstRoute(p,u,m,C,O,F){let te;if("**"===m.path){const ee=C.length>0?kn(C).parameters:{},$e=new _i(C,ee,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,I(m),gn(m),m.component??m._loadedComponent??null,m,k(m));te=(0,oe.of)({snapshot:$e,consumedSegments:[],remainingSegments:[]}),u.children={}}else te=Oo(u,m,C,p).pipe((0,je.T)(({matched:ee,consumedSegments:$e,remainingSegments:wt,parameters:dn})=>ee?{snapshot:new _i($e,dn,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,I(m),gn(m),m.component??m._loadedComponent??null,m,k(m)),consumedSegments:$e,remainingSegments:wt}:null));return te.pipe((0,ft.n)(ee=>null===ee?cr(u):this.getChildConfig(p=m._injector??p,m,C).pipe((0,ft.n)(({routes:$e})=>{const wt=m._loadedInjector??p,{snapshot:dn,consumedSegments:Xt,remainingSegments:Gi}=ee,{segmentGroup:Xo,slicedSegments:ko}=Po(u,Xt,Gi,$e);if(0===ko.length&&Xo.hasChildren())return this.processChildren(wt,$e,Xo).pipe((0,je.T)(Qo=>null===Qo?null:[new on(dn,Qo)]));if(0===$e.length&&0===ko.length)return(0,oe.of)([new on(dn,[])]);const ya=gn(m)===O;return this.processSegment(wt,$e,Xo,ko,ya?it:O,!0).pipe((0,je.T)(Qo=>[new on(dn,Qo)]))}))))}getChildConfig(p,u,m){return u.children?(0,oe.of)({routes:u.children,injector:p}):u.loadChildren?void 0!==u._loadedRoutes?(0,oe.of)({routes:u._loadedRoutes,injector:u._loadedInjector}):function no(f,p,u,m){const C=p.canLoad;if(void 0===C||0===C.length)return(0,oe.of)(!0);const O=C.map(F=>{const te=Bt(F,f);return Nt(function B(f){return f&&v(f.canLoad)}(te)?te.canLoad(p,u):f.runInContext(()=>te(p,u)))});return(0,oe.of)(O).pipe(Jr(),Ii())}(p,u,m).pipe((0,Y.Z)(C=>C?this.configLoader.loadChildren(p,u).pipe((0,ct.M)(O=>{u._loadedRoutes=O.routes,u._loadedInjector=O.injector})):function zo(f){return(0,Ae.$)(an(!1,3))}())):(0,oe.of)({routes:[],injector:p})}}function h(f){const p=f.value.routeConfig;return p&&""===p.path}function T(f){const p=[],u=new Set;for(const m of f){if(!h(m)){p.push(m);continue}const C=p.find(O=>m.value.routeConfig===O.value.routeConfig);void 0!==C?(C.children.push(...m.children),u.add(C)):p.push(m)}for(const m of u){const C=T(m.children);p.push(new on(m.value,C))}return p.filter(m=>!u.has(m))}function I(f){return f.data||{}}function k(f){return f.resolve||{}}function y(f){return"string"==typeof f.title||null===f.title}function M(f){return(0,ft.n)(p=>{const u=f(p);return u?(0,X.H)(u).pipe((0,je.T)(()=>p)):(0,oe.of)(p)})}const N=new d.nKC("ROUTES");let L=(()=>{class f{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=(0,d.WQX)(d.Ql9)}loadComponent(u){if(this.componentLoaders.get(u))return this.componentLoaders.get(u);if(u._loadedComponent)return(0,oe.of)(u._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(u);const m=Nt(u.loadComponent()).pipe((0,je.T)(ue),(0,ct.M)(O=>{this.onLoadEndListener&&this.onLoadEndListener(u),u._loadedComponent=O}),(0,fn.j)(()=>{this.componentLoaders.delete(u)})),C=new $n(m,()=>new xe.B).pipe(_n());return this.componentLoaders.set(u,C),C}loadChildren(u,m){if(this.childrenLoaders.get(m))return this.childrenLoaders.get(m);if(m._loadedRoutes)return(0,oe.of)({routes:m._loadedRoutes,injector:m._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(m);const O=function U(f,p,u,m){return Nt(f.loadChildren()).pipe((0,je.T)(ue),(0,Y.Z)(C=>C instanceof d.Co$||Array.isArray(C)?(0,oe.of)(C):(0,X.H)(p.compileModuleAsync(C))),(0,je.T)(C=>{m&&m(f);let O,F,te=!1;return Array.isArray(C)?(F=C,!0):(O=C.create(u).injector,F=O.get(N,[],{optional:!0,self:!0}).flat()),{routes:F.map(eo),injector:O}}))}(m,this.compiler,u,this.onLoadEndListener).pipe((0,fn.j)(()=>{this.childrenLoaders.delete(m)})),F=new $n(O,()=>new xe.B).pipe(_n());return this.childrenLoaders.set(m,F),F}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=d.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();function ue(f){return function q(f){return f&&"object"==typeof f&&"default"in f}(f)?f.default:f}let Pe=(()=>{class f{get hasRequestedNavigation(){return 0!==this.navigationId}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new xe.B,this.transitionAbortSubject=new xe.B,this.configLoader=(0,d.WQX)(L),this.environmentInjector=(0,d.WQX)(d.uvJ),this.urlSerializer=(0,d.WQX)(On),this.rootContexts=(0,d.WQX)(Xr),this.inputBindingEnabled=null!==(0,d.WQX)(tr,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>(0,oe.of)(void 0),this.rootComponentType=null,this.configLoader.onLoadEndListener=C=>this.events.next(new lo(C)),this.configLoader.onLoadStartListener=C=>this.events.next(new qi(C))}complete(){this.transitions?.complete()}handleNavigationRequest(u){const m=++this.navigationId;this.transitions?.next({...this.transitions.value,...u,id:m})}setupNavigations(u,m,C){return this.transitions=new ie.t({id:0,currentUrlTree:m,currentRawUrl:m,currentBrowserUrl:m,extractedUrl:u.urlHandlingStrategy.extract(m),urlAfterRedirects:u.urlHandlingStrategy.extract(m),rawUrl:m,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:ii,restoredState:null,currentSnapshot:C.snapshot,targetSnapshot:null,currentRouterState:C,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe((0,Q.p)(O=>0!==O.id),(0,je.T)(O=>({...O,extractedUrl:u.urlHandlingStrategy.extract(O.rawUrl)})),(0,ft.n)(O=>{this.currentTransition=O;let F=!1,te=!1;return(0,oe.of)(O).pipe((0,ct.M)(ee=>{this.currentNavigation={id:ee.id,initialUrl:ee.rawUrl,extractedUrl:ee.extractedUrl,trigger:ee.source,extras:ee.extras,previousNavigation:this.lastSuccessfulNavigation?{...this.lastSuccessfulNavigation,previousNavigation:null}:null}}),(0,ft.n)(ee=>{const $e=ee.currentBrowserUrl.toString(),wt=!u.navigated||ee.extractedUrl.toString()!==$e||$e!==ee.currentUrlTree.toString();if(!wt&&"reload"!==(ee.extras.onSameUrlNavigation??u.onSameUrlNavigation)){const Xt="";return this.events.next(new vi(ee.id,this.urlSerializer.serialize(ee.rawUrl),Xt,0)),ee.resolve(null),Ee.w}if(u.urlHandlingStrategy.shouldProcessUrl(ee.rawUrl))return(0,oe.of)(ee).pipe((0,ft.n)(Xt=>{const Gi=this.transitions?.getValue();return this.events.next(new Fi(Xt.id,this.urlSerializer.serialize(Xt.extractedUrl),Xt.source,Xt.restoredState)),Gi!==this.transitions?.getValue()?Ee.w:Promise.resolve(Xt)}),function re(f,p,u,m,C,O){return(0,Y.Z)(F=>function ss(f,p,u,m,C,O,F="emptyOnly"){return new Ro(f,p,u,m,C,F,O).recognize()}(f,p,u,m,F.extractedUrl,C,O).pipe((0,je.T)(({state:te,tree:ee})=>({...F,targetSnapshot:te,urlAfterRedirects:ee}))))}(this.environmentInjector,this.configLoader,this.rootComponentType,u.config,this.urlSerializer,u.paramsInheritanceStrategy),(0,ct.M)(Xt=>{O.targetSnapshot=Xt.targetSnapshot,O.urlAfterRedirects=Xt.urlAfterRedirects,this.currentNavigation={...this.currentNavigation,finalUrl:Xt.urlAfterRedirects};const Gi=new ao(Xt.id,this.urlSerializer.serialize(Xt.extractedUrl),this.urlSerializer.serialize(Xt.urlAfterRedirects),Xt.targetSnapshot);this.events.next(Gi)}));if(wt&&u.urlHandlingStrategy.shouldProcessUrl(ee.currentRawUrl)){const{id:Xt,extractedUrl:Gi,source:Xo,restoredState:ko,extras:ya}=ee,Qo=new Fi(Xt,this.urlSerializer.serialize(Gi),Xo,ko);this.events.next(Qo);const xr=bn(0,this.rootComponentType).snapshot;return this.currentTransition=O={...ee,targetSnapshot:xr,urlAfterRedirects:Gi,extras:{...ya,skipLocationChange:!1,replaceUrl:!1}},(0,oe.of)(O)}{const Xt="";return this.events.next(new vi(ee.id,this.urlSerializer.serialize(ee.extractedUrl),Xt,1)),ee.resolve(null),Ee.w}}),(0,ct.M)(ee=>{const $e=new Ar(ee.id,this.urlSerializer.serialize(ee.extractedUrl),this.urlSerializer.serialize(ee.urlAfterRedirects),ee.targetSnapshot);this.events.next($e)}),(0,je.T)(ee=>(this.currentTransition=O={...ee,guards:Bi(ee.targetSnapshot,ee.currentSnapshot,this.rootContexts)},O)),function da(f,p){return(0,Y.Z)(u=>{const{targetSnapshot:m,currentSnapshot:C,guards:{canActivateChecks:O,canDeactivateChecks:F}}=u;return 0===F.length&&0===O.length?(0,oe.of)({...u,guardsResult:!0}):function fa(f,p,u,m){return(0,X.H)(f).pipe((0,Y.Z)(C=>function ws(f,p,u,m,C){const O=p&&p.routeConfig?p.routeConfig.canDeactivate:null;if(!O||0===O.length)return(0,oe.of)(!0);const F=O.map(te=>{const ee=Nn(p)??C,$e=Bt(te,ee);return Nt(function Qn(f){return f&&v(f.canDeactivate)}($e)?$e.canDeactivate(f,p,u,m):ee.runInContext(()=>$e(f,p,u,m))).pipe(we())});return(0,oe.of)(F).pipe(Jr())}(C.component,C.route,u,p,m)),we(C=>!0!==C,!0))}(F,m,C,f).pipe((0,Y.Z)(te=>te&&function P(f){return"boolean"==typeof f}(te)?function ts(f,p,u,m){return(0,X.H)(p).pipe((0,We.H)(C=>Te(function Io(f,p){return null!==f&&p&&p(new Zi(f)),(0,oe.of)(!0)}(C.route.parent,m),function fo(f,p){return null!==f&&p&&p(new Bo(f)),(0,oe.of)(!0)}(C.route,m),function Es(f,p,u){const m=p[p.length-1],O=p.slice(0,p.length-1).reverse().map(F=>function bi(f){const p=f.routeConfig?f.routeConfig.canActivateChild:null;return p&&0!==p.length?{node:f,guards:p}:null}(F)).filter(F=>null!==F).map(F=>nt(()=>{const te=F.guards.map(ee=>{const $e=Nn(F.node)??u,wt=Bt(ee,$e);return Nt(function vt(f){return f&&v(f.canActivateChild)}(wt)?wt.canActivateChild(m,f):$e.runInContext(()=>wt(m,f))).pipe(we())});return(0,oe.of)(te).pipe(Jr())}));return(0,oe.of)(O).pipe(Jr())}(f,C.path,u),function _s(f,p,u){const m=p.routeConfig?p.routeConfig.canActivate:null;if(!m||0===m.length)return(0,oe.of)(!0);const C=m.map(O=>nt(()=>{const F=Nn(p)??u,te=Bt(O,F);return Nt(function Ke(f){return f&&v(f.canActivate)}(te)?te.canActivate(p,f):F.runInContext(()=>te(p,f))).pipe(we())}));return(0,oe.of)(C).pipe(Jr())}(f,C.route,u))),we(C=>!0!==C,!0))}(m,O,f,p):(0,oe.of)(te)),(0,je.T)(te=>({...u,guardsResult:te})))})}(this.environmentInjector,ee=>this.events.next(ee)),(0,ct.M)(ee=>{if(O.guardsResult=ee.guardsResult,Jt(ee.guardsResult))throw Ur(0,ee.guardsResult);const $e=new Qi(ee.id,this.urlSerializer.serialize(ee.extractedUrl),this.urlSerializer.serialize(ee.urlAfterRedirects),ee.targetSnapshot,!!ee.guardsResult);this.events.next($e)}),(0,Q.p)(ee=>!!ee.guardsResult||(this.cancelNavigationTransition(ee,"",3),!1)),M(ee=>{if(ee.guards.canActivateChecks.length)return(0,oe.of)(ee).pipe((0,ct.M)($e=>{const wt=new ki($e.id,this.urlSerializer.serialize($e.extractedUrl),this.urlSerializer.serialize($e.urlAfterRedirects),$e.targetSnapshot);this.events.next(wt)}),(0,ft.n)($e=>{let wt=!1;return(0,oe.of)($e).pipe(function ve(f,p){return(0,Y.Z)(u=>{const{targetSnapshot:m,guards:{canActivateChecks:C}}=u;if(!C.length)return(0,oe.of)(u);let O=0;return(0,X.H)(C).pipe((0,We.H)(F=>function qe(f,p,u,m){const C=f.routeConfig,O=f._resolve;return void 0!==C?.title&&!y(C)&&(O[Yn]=C.title),function E(f,p,u,m){const C=function a(f){return[...Object.keys(f),...Object.getOwnPropertySymbols(f)]}(f);if(0===C.length)return(0,oe.of)({});const O={};return(0,X.H)(C).pipe((0,Y.Z)(F=>function l(f,p,u,m){const C=Nn(p)??m,O=Bt(f,C);return Nt(O.resolve?O.resolve(p,u):C.runInContext(()=>O(p,u)))}(f[F],p,u,m).pipe(we(),(0,ct.M)(te=>{O[F]=te}))),qt(1),function gt(f){return(0,je.T)(()=>f)}(O),(0,zt.W)(F=>to(F)?Ee.w:(0,Ae.$)(F)))}(O,f,p,m).pipe((0,je.T)(F=>(f._resolvedData=F,f.data=Qr(f,u).resolve,C&&y(C)&&(f.data[Yn]=C.title),null)))}(F.route,m,f,p)),(0,ct.M)(()=>O++),qt(1),(0,Y.Z)(F=>O===C.length?(0,oe.of)(u):Ee.w))})}(u.paramsInheritanceStrategy,this.environmentInjector),(0,ct.M)({next:()=>wt=!0,complete:()=>{wt||this.cancelNavigationTransition($e,"",2)}}))}),(0,ct.M)($e=>{const wt=new oi($e.id,this.urlSerializer.serialize($e.extractedUrl),this.urlSerializer.serialize($e.urlAfterRedirects),$e.targetSnapshot);this.events.next(wt)}))}),M(ee=>{const $e=wt=>{const dn=[];wt.routeConfig?.loadComponent&&!wt.routeConfig._loadedComponent&&dn.push(this.configLoader.loadComponent(wt.routeConfig).pipe((0,ct.M)(Xt=>{wt.component=Xt}),(0,je.T)(()=>{})));for(const Xt of wt.children)dn.push(...$e(Xt));return dn};return Ge($e(ee.targetSnapshot.root)).pipe(J(),Fn(1))}),M(()=>this.afterPreactivation()),(0,je.T)(ee=>{const $e=function Cr(f,p,u){const m=ur(f,p._root,u?u._root:void 0);return new Or(m,p)}(u.routeReuseStrategy,ee.targetSnapshot,ee.currentRouterState);return this.currentTransition=O={...ee,targetRouterState:$e},O}),(0,ct.M)(()=>{this.events.next(new jr)}),((f,p,u,m)=>(0,je.T)(C=>(new Ho(p,C.targetRouterState,C.currentRouterState,u,m).activate(f),C)))(this.rootContexts,u.routeReuseStrategy,ee=>this.events.next(ee),this.inputBindingEnabled),Fn(1),(0,ct.M)({next:ee=>{F=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Vr(ee.id,this.urlSerializer.serialize(ee.extractedUrl),this.urlSerializer.serialize(ee.urlAfterRedirects))),u.titleStrategy?.updateTitle(ee.targetRouterState.snapshot),ee.resolve(!0)},complete:()=>{F=!0}}),function Qe(f){return(0,Ft.N)((p,u)=>{(0,Ve.Tg)(f).subscribe((0,be._)(u,()=>u.complete(),Gt.l)),!u.closed&&p.subscribe(u)})}(this.transitionAbortSubject.pipe((0,ct.M)(ee=>{throw ee}))),(0,fn.j)(()=>{F||te||this.cancelNavigationTransition(O,"",1),this.currentNavigation?.id===O.id&&(this.currentNavigation=null)}),(0,zt.W)(ee=>{if(te=!0,nr(ee))this.events.next(new Wn(O.id,this.urlSerializer.serialize(O.extractedUrl),ee.message,ee.cancellationCode)),function Vn(f){return nr(f)&&Jt(f.url)}(ee)?this.events.next(new Di(ee.url)):O.resolve(!1);else{this.events.next(new Zn(O.id,this.urlSerializer.serialize(O.extractedUrl),ee,O.targetSnapshot??void 0));try{O.resolve(u.errorHandler(ee))}catch($e){O.reject($e)}}return Ee.w}))}))}cancelNavigationTransition(u,m,C){const O=new Wn(u.id,this.urlSerializer.serialize(u.extractedUrl),m,C);this.events.next(O),u.resolve(!1)}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=d.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();function Ue(f){return f!==ii}let It=(()=>{class f{buildTitle(u){let m,C=u.root;for(;void 0!==C;)m=this.getResolvedTitleForRoute(C)??m,C=C.children.find(O=>O.outlet===it);return m}getResolvedTitleForRoute(u){return u.data[Yn]}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=d.jDH({token:f,factory:function(){return(0,d.WQX)(yt)},providedIn:"root"})}}return f})(),yt=(()=>{class f extends It{constructor(u){super(),this.title=u}updateTitle(u){const m=this.buildTitle(u);void 0!==m&&this.title.setTitle(m)}static{this.\u0275fac=function(m){return new(m||f)(d.KVO(tn.hE))}}static{this.\u0275prov=d.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})(),st=(()=>{class f{static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=d.jDH({token:f,factory:function(){return(0,d.WQX)(mn)},providedIn:"root"})}}return f})();class _t{shouldDetach(p){return!1}store(p,u){}shouldAttach(p){return!1}retrieve(p){return null}shouldReuseRoute(p,u){return p.routeConfig===u.routeConfig}}let mn=(()=>{class f extends _t{static{this.\u0275fac=function(){let u;return function(C){return(u||(u=d.xGo(f)))(C||f)}}()}static{this.\u0275prov=d.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();const Mn=new d.nKC("",{providedIn:"root",factory:()=>({})});let Un=(()=>{class f{static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=d.jDH({token:f,factory:function(){return(0,d.WQX)(rr)},providedIn:"root"})}}return f})(),rr=(()=>{class f{shouldProcessUrl(u){return!0}extract(u){return u}merge(u,m){return u}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=d.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();var In=function(f){return f[f.COMPLETE=0]="COMPLETE",f[f.FAILED=1]="FAILED",f[f.REDIRECTING=2]="REDIRECTING",f}(In||{});function Ti(f,p){f.events.pipe((0,Q.p)(u=>u instanceof Vr||u instanceof Wn||u instanceof Zn||u instanceof vi),(0,je.T)(u=>u instanceof Vr||u instanceof vi?In.COMPLETE:u instanceof Wn&&(0===u.code||1===u.code)?In.REDIRECTING:In.FAILED),(0,Q.p)(u=>u!==In.REDIRECTING),Fn(1)).subscribe(()=>{p()})}function ci(f){throw f}function yn(f,p,u){return p.parse("/")}const di={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},fi={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"};let Et=(()=>{class f{get navigationId(){return this.navigationTransitions.navigationId}get browserPageId(){return"computed"!==this.canceledNavigationResolution?this.currentPageId:this.location.getState()?.\u0275routerPageId??this.currentPageId}get events(){return this._events}constructor(){this.disposed=!1,this.currentPageId=0,this.console=(0,d.WQX)(d.H3F),this.isNgZoneEnabled=!1,this._events=new xe.B,this.options=(0,d.WQX)(Mn,{optional:!0})||{},this.pendingTasks=(0,d.WQX)(d.$K3),this.errorHandler=this.options.errorHandler||ci,this.malformedUriErrorHandler=this.options.malformedUriErrorHandler||yn,this.navigated=!1,this.lastSuccessfulId=-1,this.urlHandlingStrategy=(0,d.WQX)(Un),this.routeReuseStrategy=(0,d.WQX)(st),this.titleStrategy=(0,d.WQX)(It),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.config=(0,d.WQX)(N,{optional:!0})?.flat()??[],this.navigationTransitions=(0,d.WQX)(Pe),this.urlSerializer=(0,d.WQX)(On),this.location=(0,d.WQX)(en.aZ),this.componentInputBindingEnabled=!!(0,d.WQX)(tr,{optional:!0}),this.eventsSubscription=new rt.yU,this.isNgZoneEnabled=(0,d.WQX)(d.SKi)instanceof d.SKi&&d.SKi.isInAngularZone(),this.resetConfig(this.config),this.currentUrlTree=new wn,this.rawUrlTree=this.currentUrlTree,this.browserUrlTree=this.currentUrlTree,this.routerState=bn(0,null),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe(u=>{this.lastSuccessfulId=u.id,this.currentPageId=this.browserPageId},u=>{this.console.warn(`Unhandled Navigation Error: ${u}`)}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){const u=this.navigationTransitions.events.subscribe(m=>{try{const{currentTransition:C}=this.navigationTransitions;if(null===C)return void(Ai(m)&&this._events.next(m));if(m instanceof Fi)Ue(C.source)&&(this.browserUrlTree=C.extractedUrl);else if(m instanceof vi)this.rawUrlTree=C.rawUrl;else if(m instanceof ao){if("eager"===this.urlUpdateStrategy){if(!C.extras.skipLocationChange){const O=this.urlHandlingStrategy.merge(C.urlAfterRedirects,C.rawUrl);this.setBrowserUrl(O,C)}this.browserUrlTree=C.urlAfterRedirects}}else if(m instanceof jr)this.currentUrlTree=C.urlAfterRedirects,this.rawUrlTree=this.urlHandlingStrategy.merge(C.urlAfterRedirects,C.rawUrl),this.routerState=C.targetRouterState,"deferred"===this.urlUpdateStrategy&&(C.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,C),this.browserUrlTree=C.urlAfterRedirects);else if(m instanceof Wn)0!==m.code&&1!==m.code&&(this.navigated=!0),(3===m.code||2===m.code)&&this.restoreHistory(C);else if(m instanceof Di){const O=this.urlHandlingStrategy.merge(m.url,C.currentRawUrl),F={skipLocationChange:C.extras.skipLocationChange,replaceUrl:"eager"===this.urlUpdateStrategy||Ue(C.source)};this.scheduleNavigation(O,ii,null,F,{resolve:C.resolve,reject:C.reject,promise:C.promise})}m instanceof Zn&&this.restoreHistory(C,!0),m instanceof Vr&&(this.navigated=!0),Ai(m)&&this._events.next(m)}catch(C){this.navigationTransitions.transitionAbortSubject.next(C)}});this.eventsSubscription.add(u)}resetRootComponentType(u){this.routerState.root.component=u,this.navigationTransitions.rootComponentType=u}initialNavigation(){if(this.setUpLocationChangeListener(),!this.navigationTransitions.hasRequestedNavigation){const u=this.location.getState();this.navigateToSyncWithBrowser(this.location.path(!0),ii,u)}}setUpLocationChangeListener(){this.locationSubscription||(this.locationSubscription=this.location.subscribe(u=>{const m="popstate"===u.type?"popstate":"hashchange";"popstate"===m&&setTimeout(()=>{this.navigateToSyncWithBrowser(u.url,m,u.state)},0)}))}navigateToSyncWithBrowser(u,m,C){const O={replaceUrl:!0},F=C?.navigationId?C:null;if(C){const ee={...C};delete ee.navigationId,delete ee.\u0275routerPageId,0!==Object.keys(ee).length&&(O.state=ee)}const te=this.parseUrl(u);this.scheduleNavigation(te,m,F,O)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(u){this.config=u.map(eo),this.navigated=!1,this.lastSuccessfulId=-1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.locationSubscription&&(this.locationSubscription.unsubscribe(),this.locationSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(u,m={}){const{relativeTo:C,queryParams:O,fragment:F,queryParamsHandling:te,preserveFragment:ee}=m,$e=ee?this.currentUrlTree.fragment:F;let dn,wt=null;switch(te){case"merge":wt={...this.currentUrlTree.queryParams,...O};break;case"preserve":wt=this.currentUrlTree.queryParams;break;default:wt=O||null}null!==wt&&(wt=this.removeEmptyProps(wt));try{dn=Kr(C?C.snapshot:this.routerState.snapshot.root)}catch{("string"!=typeof u[0]||!u[0].startsWith("/"))&&(u=[]),dn=this.currentUrlTree.root}return yr(dn,u,wt,$e??null)}navigateByUrl(u,m={skipLocationChange:!1}){const C=Jt(u)?u:this.parseUrl(u),O=this.urlHandlingStrategy.merge(C,this.rawUrlTree);return this.scheduleNavigation(O,ii,null,m)}navigate(u,m={skipLocationChange:!1}){return function Wo(f){for(let p=0;p<f.length;p++)if(null==f[p])throw new d.wOt(4008,!1)}(u),this.navigateByUrl(this.createUrlTree(u,m),m)}serializeUrl(u){return this.urlSerializer.serialize(u)}parseUrl(u){let m;try{m=this.urlSerializer.parse(u)}catch(C){m=this.malformedUriErrorHandler(C,this.urlSerializer,u)}return m}isActive(u,m){let C;if(C=!0===m?{...di}:!1===m?{...fi}:m,Jt(u))return Zt(this.currentUrlTree,u,C);const O=this.parseUrl(u);return Zt(this.currentUrlTree,O,C)}removeEmptyProps(u){return Object.keys(u).reduce((m,C)=>{const O=u[C];return null!=O&&(m[C]=O),m},{})}scheduleNavigation(u,m,C,O,F){if(this.disposed)return Promise.resolve(!1);let te,ee,$e;F?(te=F.resolve,ee=F.reject,$e=F.promise):$e=new Promise((dn,Xt)=>{te=dn,ee=Xt});const wt=this.pendingTasks.add();return Ti(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(wt))}),this.navigationTransitions.handleNavigationRequest({source:m,restoredState:C,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,currentBrowserUrl:this.browserUrlTree,rawUrl:u,extras:O,resolve:te,reject:ee,promise:$e,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),$e.catch(dn=>Promise.reject(dn))}setBrowserUrl(u,m){const C=this.urlSerializer.serialize(u);if(this.location.isCurrentPathEqualTo(C)||m.extras.replaceUrl){const F={...m.extras.state,...this.generateNgRouterState(m.id,this.browserPageId)};this.location.replaceState(C,"",F)}else{const O={...m.extras.state,...this.generateNgRouterState(m.id,this.browserPageId+1)};this.location.go(C,"",O)}}restoreHistory(u,m=!1){if("computed"===this.canceledNavigationResolution){const O=this.currentPageId-this.browserPageId;0!==O?this.location.historyGo(O):this.currentUrlTree===this.getCurrentNavigation()?.finalUrl&&0===O&&(this.resetState(u),this.browserUrlTree=u.currentUrlTree,this.resetUrlToCurrentUrlTree())}else"replace"===this.canceledNavigationResolution&&(m&&this.resetState(u),this.resetUrlToCurrentUrlTree())}resetState(u){this.routerState=u.currentRouterState,this.currentUrlTree=u.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,u.rawUrl)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(u,m){return"computed"===this.canceledNavigationResolution?{navigationId:u,\u0275routerPageId:m}:{navigationId:u}}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=d.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();function Ai(f){return!(f instanceof jr||f instanceof Di)}let Cn=(()=>{class f{constructor(u,m,C,O,F,te){this.router=u,this.route=m,this.tabIndexAttribute=C,this.renderer=O,this.el=F,this.locationStrategy=te,this.href=null,this.commands=null,this.onChanges=new xe.B,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1;const ee=F.nativeElement.tagName?.toLowerCase();this.isAnchorElement="a"===ee||"area"===ee,this.isAnchorElement?this.subscription=u.events.subscribe($e=>{$e instanceof Vr&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(u){null!=this.tabIndexAttribute||this.isAnchorElement||this.applyAttributeValue("tabindex",u)}ngOnChanges(u){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(u){null!=u?(this.commands=Array.isArray(u)?u:[u],this.setTabIndexIfNotOnNativeEl("0")):(this.commands=null,this.setTabIndexIfNotOnNativeEl(null))}onClick(u,m,C,O,F){return!!(null===this.urlTree||this.isAnchorElement&&(0!==u||m||C||O||F||"string"==typeof this.target&&"_self"!=this.target))||(this.router.navigateByUrl(this.urlTree,{skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state}),!this.isAnchorElement)}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){this.href=null!==this.urlTree&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(this.urlTree)):null;const u=null===this.href?null:(0,d.n$t)(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",u)}applyAttributeValue(u,m){const C=this.renderer,O=this.el.nativeElement;null!==m?C.setAttribute(O,u,m):C.removeAttribute(O,u)}get urlTree(){return null===this.commands?null:this.router.createUrlTree(this.commands,{relativeTo:void 0!==this.relativeTo?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static{this.\u0275fac=function(m){return new(m||f)(d.rXU(Et),d.rXU(Nr),d.kS0("tabindex"),d.rXU(d.sFG),d.rXU(d.aKT),d.rXU(en.hb))}}static{this.\u0275dir=d.FsC({type:f,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(m,C){1&m&&d.bIt("click",function(F){return C.onClick(F.button,F.ctrlKey,F.shiftKey,F.altKey,F.metaKey)}),2&m&&d.BMQ("target",C.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",relativeTo:"relativeTo",preserveFragment:["preserveFragment","preserveFragment",d.L39],skipLocationChange:["skipLocationChange","skipLocationChange",d.L39],replaceUrl:["replaceUrl","replaceUrl",d.L39],routerLink:"routerLink"},standalone:!0,features:[d.GFd,d.OA$]})}}return f})(),as=(()=>{class f{get isActive(){return this._isActive}constructor(u,m,C,O,F){this.router=u,this.element=m,this.renderer=C,this.cdr=O,this.link=F,this.classes=[],this._isActive=!1,this.routerLinkActiveOptions={exact:!1},this.isActiveChange=new d.bkB,this.routerEventsSubscription=u.events.subscribe(te=>{te instanceof Vr&&this.update()})}ngAfterContentInit(){(0,oe.of)(this.links.changes,(0,oe.of)(null)).pipe((0,Qt.U)()).subscribe(u=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();const u=[...this.links.toArray(),this.link].filter(m=>!!m).map(m=>m.onChanges);this.linkInputChangesSubscription=(0,X.H)(u).pipe((0,Qt.U)()).subscribe(m=>{this._isActive!==this.isLinkActive(this.router)(m)&&this.update()})}set routerLinkActive(u){const m=Array.isArray(u)?u:u.split(" ");this.classes=m.filter(C=>!!C)}ngOnChanges(u){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{const u=this.hasActiveLinks();this._isActive!==u&&(this._isActive=u,this.cdr.markForCheck(),this.classes.forEach(m=>{u?this.renderer.addClass(this.element.nativeElement,m):this.renderer.removeClass(this.element.nativeElement,m)}),u&&void 0!==this.ariaCurrentWhenActive?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this.isActiveChange.emit(u))})}isLinkActive(u){const m=function ls(f){return!!f.paths}(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return C=>!!C.urlTree&&u.isActive(C.urlTree,m)}hasActiveLinks(){const u=this.isLinkActive(this.router);return this.link&&u(this.link)||this.links.some(u)}static{this.\u0275fac=function(m){return new(m||f)(d.rXU(Et),d.rXU(d.aKT),d.rXU(d.sFG),d.rXU(d.gRc),d.rXU(Cn,8))}}static{this.\u0275dir=d.FsC({type:f,selectors:[["","routerLinkActive",""]],contentQueries:function(m,C,O){if(1&m&&d.wni(O,Cn,5),2&m){let F;d.mGM(F=d.lsd())&&(C.links=F)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],standalone:!0,features:[d.OA$]})}}return f})();class Xe{}let $t=(()=>{class f{constructor(u,m,C,O,F){this.router=u,this.injector=C,this.preloadingStrategy=O,this.loader=F}setUpPreloading(){this.subscription=this.router.events.pipe((0,Q.p)(u=>u instanceof Vr),(0,We.H)(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(u,m){const C=[];for(const O of m){O.providers&&!O._injector&&(O._injector=(0,d.Ol2)(O.providers,u,`Route: ${O.path}`));const F=O._injector??u,te=O._loadedInjector??F;(O.loadChildren&&!O._loadedRoutes&&void 0===O.canLoad||O.loadComponent&&!O._loadedComponent)&&C.push(this.preloadConfig(F,O)),(O.children||O._loadedRoutes)&&C.push(this.processRoutes(te,O.children??O._loadedRoutes))}return(0,X.H)(C).pipe((0,Qt.U)())}preloadConfig(u,m){return this.preloadingStrategy.preload(m,()=>{let C;C=m.loadChildren&&void 0===m.canLoad?this.loader.loadChildren(u,m):(0,oe.of)(null);const O=C.pipe((0,Y.Z)(F=>null===F?(0,oe.of)(void 0):(m._loadedRoutes=F.routes,m._loadedInjector=F.injector,this.processRoutes(F.injector??u,F.routes))));if(m.loadComponent&&!m._loadedComponent){const F=this.loader.loadComponent(m);return(0,X.H)([O,F]).pipe((0,Qt.U)())}return O})}static{this.\u0275fac=function(m){return new(m||f)(d.KVO(Et),d.KVO(d.Ql9),d.KVO(d.uvJ),d.KVO(Xe),d.KVO(L))}}static{this.\u0275prov=d.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();const xn=new d.nKC("");let qn=(()=>{class f{constructor(u,m,C,O,F={}){this.urlSerializer=u,this.transitions=m,this.viewportScroller=C,this.zone=O,this.options=F,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},F.scrollPositionRestoration=F.scrollPositionRestoration||"disabled",F.anchorScrolling=F.anchorScrolling||"disabled"}init(){"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(u=>{u instanceof Fi?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=u.navigationTrigger,this.restoredId=u.restoredState?u.restoredState.navigationId:0):u instanceof Vr?(this.lastId=u.id,this.scheduleScrollEvent(u,this.urlSerializer.parse(u.urlAfterRedirects).fragment)):u instanceof vi&&0===u.code&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(u,this.urlSerializer.parse(u.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(u=>{u instanceof uo&&(u.position?"top"===this.options.scrollPositionRestoration?this.viewportScroller.scrollToPosition([0,0]):"enabled"===this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition(u.position):u.anchor&&"enabled"===this.options.anchorScrolling?this.viewportScroller.scrollToAnchor(u.anchor):"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(u,m){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new uo(u,"popstate"===this.lastSource?this.store[this.restoredId]:null,m))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(m){d.QTQ()}}static{this.\u0275prov=d.jDH({token:f,factory:f.\u0275fac})}}return f})();function Kt(f,p){return{\u0275kind:f,\u0275providers:p}}function Er(){const f=(0,d.WQX)(d.zZn);return p=>{const u=f.get(d.o8S);if(p!==u.components[0])return;const m=f.get(Et),C=f.get(Ko);1===f.get(xo)&&m.initialNavigation(),f.get(Fo,null,d.$GK.Optional)?.setUpPreloading(),f.get(xn,null,d.$GK.Optional)?.init(),m.resetRootComponentType(u.componentTypes[0]),C.closed||(C.next(),C.complete(),C.unsubscribe())}}const Ko=new d.nKC("",{factory:()=>new xe.B}),xo=new d.nKC("",{providedIn:"root",factory:()=>1}),Fo=new d.nKC("");function Su(f){return Kt(0,[{provide:Fo,useExisting:$t},{provide:Xe,useExisting:f}])}const Ht=new d.nKC("ROUTER_FORROOT_GUARD"),nl=[en.aZ,{provide:On,useClass:mr},Et,Xr,{provide:Nr,useFactory:function Oi(f){return f.routerState.root},deps:[Et]},L,[]];function rl(){return new d.NEm("Router",Et)}let fr=(()=>{class f{constructor(u){}static forRoot(u,m){return{ngModule:f,providers:[nl,[],{provide:N,multi:!0,useValue:u},{provide:Ht,useFactory:ga,deps:[[Et,new d.Xx1,new d.kdw]]},{provide:Mn,useValue:m||{}},m?.useHash?{provide:en.hb,useClass:en.fw}:{provide:en.hb,useClass:en.Sm},{provide:xn,useFactory:()=>{const f=(0,d.WQX)(en.Xr),p=(0,d.WQX)(d.SKi),u=(0,d.WQX)(Mn),m=(0,d.WQX)(Pe),C=(0,d.WQX)(On);return u.scrollOffset&&f.setOffset(u.scrollOffset),new qn(C,m,f,p,u)}},m?.preloadingStrategy?Su(m.preloadingStrategy).\u0275providers:[],{provide:d.NEm,multi:!0,useFactory:rl},m?.initialNavigation?ma(m):[],m?.bindToComponentInputs?Kt(8,[un,{provide:tr,useExisting:un}]).\u0275providers:[],[{provide:ol,useFactory:Er},{provide:d.iLQ,multi:!0,useExisting:ol}]]}}static forChild(u){return{ngModule:f,providers:[{provide:N,multi:!0,useValue:u}]}}static{this.\u0275fac=function(m){return new(m||f)(d.KVO(Ht,8))}}static{this.\u0275mod=d.$C({type:f})}static{this.\u0275inj=d.G2t({})}}return f})();function ga(f){return"guarded"}function ma(f){return["disabled"===f.initialNavigation?Kt(3,[{provide:d.hnV,multi:!0,useFactory:()=>{const p=(0,d.WQX)(Et);return()=>{p.setUpLocationChangeListener()}}},{provide:xo,useValue:2}]).\u0275providers:[],"enabledBlocking"===f.initialNavigation?Kt(2,[{provide:xo,useValue:0},{provide:d.hnV,multi:!0,deps:[d.zZn],useFactory:p=>{const u=p.get(en.hj,Promise.resolve());return()=>u.then(()=>new Promise(m=>{const C=p.get(Et),O=p.get(Ko);Ti(C,()=>{m(!0)}),p.get(Pe).afterPreactivation=()=>(m(!0),O.closed?(0,oe.of)(void 0):O),C.initialNavigation()}))}}]).\u0275providers:[]]}const ol=new d.nKC("")},467:(tt,ye,R)=>{function d(de,ae,X,oe,ie,ge,le){try{var pe=de[ge](le),Z=pe.value}catch(ne){return void X(ne)}pe.done?ae(Z):Promise.resolve(Z).then(oe,ie)}function w(de){return function(){var ae=this,X=arguments;return new Promise(function(oe,ie){var ge=de.apply(ae,X);function le(Z){d(ge,oe,ie,le,pe,"next",Z)}function pe(Z){d(ge,oe,ie,le,pe,"throw",Z)}le(void 0)})}}R.d(ye,{A:()=>w})},1635:(tt,ye,R)=>{function ne(Q,Y,J,se){return new(J||(J=Promise))(function(we,We){function ct(kt){try{at(se.next(kt))}catch(qt){We(qt)}}function zt(kt){try{at(se.throw(kt))}catch(qt){We(qt)}}function at(kt){kt.done?we(kt.value):function Ie(we){return we instanceof J?we:new J(function(We){We(we)})}(kt.value).then(ct,zt)}at((se=se.apply(Q,Y||[])).next())})}function De(Q){return this instanceof De?(this.v=Q,this):new De(Q)}function Te(Q,Y,J){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Ie,se=J.apply(Q,Y||[]),we=[];return Ie=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),ct("next"),ct("throw"),ct("return",function We(gt){return function(fn){return Promise.resolve(fn).then(gt,qt)}}),Ie[Symbol.asyncIterator]=function(){return this},Ie;function ct(gt,fn){se[gt]&&(Ie[gt]=function(Gt){return new Promise(function(Qe,tn){we.push([gt,Gt,Qe,tn])>1||zt(gt,Gt)})},fn&&(Ie[gt]=fn(Ie[gt])))}function zt(gt,fn){try{!function at(gt){gt.value instanceof De?Promise.resolve(gt.value.v).then(kt,qt):Jn(we[0][2],gt)}(se[gt](fn))}catch(Gt){Jn(we[0][3],Gt)}}function kt(gt){zt("next",gt)}function qt(gt){zt("throw",gt)}function Jn(gt,fn){gt(fn),we.shift(),we.length&&zt(we[0][0],we[0][1])}}function nt(Q){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var J,Y=Q[Symbol.asyncIterator];return Y?Y.call(Q):(Q=function Lt(Q){var Y="function"==typeof Symbol&&Symbol.iterator,J=Y&&Q[Y],se=0;if(J)return J.call(Q);if(Q&&"number"==typeof Q.length)return{next:function(){return Q&&se>=Q.length&&(Q=void 0),{value:Q&&Q[se++],done:!Q}}};throw new TypeError(Y?"Object is not iterable.":"Symbol.iterator is not defined.")}(Q),J={},se("next"),se("throw"),se("return"),J[Symbol.asyncIterator]=function(){return this},J);function se(we){J[we]=Q[we]&&function(We){return new Promise(function(ct,zt){!function Ie(we,We,ct,zt){Promise.resolve(zt).then(function(at){we({value:at,done:ct})},We)}(ct,zt,(We=Q[we](We)).done,We.value)})}}}R.d(ye,{AQ:()=>Te,N3:()=>De,sH:()=>ne,xN:()=>nt}),"function"==typeof SuppressedError&&SuppressedError}},tt=>{tt(tt.s=5169)}]);