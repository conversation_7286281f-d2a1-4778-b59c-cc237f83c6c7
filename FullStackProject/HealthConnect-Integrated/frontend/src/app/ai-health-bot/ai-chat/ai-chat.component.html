<div class="ai-chat-container">
  <!-- Header -->
  <div class="chat-header">
    <div class="d-flex justify-content-between align-items-center">
      <div class="d-flex align-items-center">
        <i class="fas fa-robot text-primary me-2"></i>
        <h4 class="mb-0">HealthConnect AI Assistant</h4>
      </div>
      <div class="d-flex gap-2">
        <button 
          class="btn btn-outline-primary btn-sm" 
          (click)="onNewConversation()"
          [disabled]="isSending">
          <i class="fas fa-plus me-1"></i>
          New Chat
        </button>
        <button 
          class="btn btn-outline-secondary btn-sm" 
          routerLink="/ai-health-bot/history">
          <i class="fas fa-history me-1"></i>
          History
        </button>
      </div>
    </div>
    
    <!-- Conversation Info -->
    <div class="conversation-info mt-2" *ngIf="currentConversation">
      <div class="d-flex align-items-center text-muted">
        <i class="fas fa-{{getConversationTypeIcon(currentConversation.conversationType)}} me-2"></i>
        <span class="me-3">{{getConversationTypeDisplayName(currentConversation.conversationType)}}</span>
        <small>{{currentConversation.title}}</small>
      </div>
    </div>
  </div>

  <!-- Messages Container -->
  <div class="messages-container" #messagesContainer>
    <!-- Loading State -->
    <div class="text-center py-4" *ngIf="isLoading">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2 text-muted">Loading conversation...</p>
    </div>

    <!-- Welcome Message -->
    <div class="welcome-message" *ngIf="messages.length === 0 && !isLoading">
      <div class="text-center py-5">
        <i class="fas fa-robot fa-3x text-primary mb-3"></i>
        <h5>Welcome to HealthConnect AI Assistant</h5>
        <p class="text-muted">I'm here to help you with health-related questions and provide general medical guidance.</p>
        <div class="alert alert-info">
          <i class="fas fa-info-circle me-2"></i>
          <strong>Important:</strong> I provide general health information only. For medical emergencies or serious concerns, please consult healthcare professionals immediately.
        </div>
      </div>
    </div>

    <!-- Messages -->
    <div class="messages-list" *ngIf="messages.length > 0">
      <div 
        class="message" 
        *ngFor="let message of messages"
        [ngClass]="{'user-message': message.role === 'USER', 'ai-message': message.role === 'ASSISTANT'}">
        
        <div class="message-content">
          <div class="message-header">
            <div class="message-avatar">
              <i class="fas" [ngClass]="{
                'fa-user': message.role === 'USER',
                'fa-robot': message.role === 'ASSISTANT'
              }"></i>
            </div>
            <div class="message-info">
              <span class="message-sender">
                {{message.role === 'USER' ? 'You' : 'HealthConnect AI'}}
              </span>
              <span class="message-time">{{formatTimestamp(message.timestamp)}}</span>
            </div>
          </div>
          <div class="message-text">
            {{message.content}}
          </div>
        </div>
      </div>

      <!-- Typing Indicator -->
      <div class="message ai-message" *ngIf="isSending">
        <div class="message-content">
          <div class="message-header">
            <div class="message-avatar">
              <i class="fas fa-robot"></i>
            </div>
            <div class="message-info">
              <span class="message-sender">HealthConnect AI</span>
              <span class="message-time">typing...</span>
            </div>
          </div>
          <div class="message-text">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error Message -->
  <div class="alert alert-danger mx-3" *ngIf="error">
    <i class="fas fa-exclamation-triangle me-2"></i>
    {{error}}
  </div>

  <!-- Chat Input -->
  <div class="chat-input">
    <form [formGroup]="chatForm" (ngSubmit)="onSendMessage()">
      <!-- Conversation Type Selector -->
      <div class="conversation-type-selector mb-2" *ngIf="!currentConversation">
        <label class="form-label small text-muted">Conversation Type:</label>
        <select 
          class="form-select form-select-sm" 
          formControlName="conversationType"
          (change)="onConversationTypeChange()">
          <option *ngFor="let type of conversationTypes" [value]="type">
            {{getConversationTypeDisplayName(type)}}
          </option>
        </select>
      </div>

      <!-- Message Input -->
      <div class="input-group">
        <textarea 
          class="form-control" 
          formControlName="message"
          placeholder="Type your health question here..."
          rows="2"
          [disabled]="isSending"
          (keypress)="onKeyPress($event)"></textarea>
        <button 
          class="btn btn-primary" 
          type="submit"
          [disabled]="chatForm.invalid || isSending">
          <i class="fas fa-paper-plane" *ngIf="!isSending"></i>
          <div class="spinner-border spinner-border-sm" *ngIf="isSending" role="status">
            <span class="visually-hidden">Sending...</span>
          </div>
        </button>
      </div>

      <!-- Character Count -->
      <div class="text-end mt-1">
        <small class="text-muted">
          {{chatForm.get('message')?.value?.length || 0}}/2000
        </small>
      </div>
    </form>
  </div>
</div>
