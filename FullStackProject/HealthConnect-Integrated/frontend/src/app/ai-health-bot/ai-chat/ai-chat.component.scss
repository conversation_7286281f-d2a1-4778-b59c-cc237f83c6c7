.ai-chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
  background: #f8f9fa;
}

.chat-header {
  background: white;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.conversation-info {
  font-size: 0.9rem;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.welcome-message {
  max-width: 600px;
  margin: 0 auto;
}

.messages-list {
  max-width: 800px;
  margin: 0 auto;
}

.message {
  margin-bottom: 1.5rem;
  
  &.user-message {
    .message-content {
      margin-left: auto;
      margin-right: 0;
      max-width: 70%;
      background: #007bff;
      color: white;
      
      .message-avatar {
        background: #0056b3;
      }
    }
  }
  
  &.ai-message {
    .message-content {
      margin-left: 0;
      margin-right: auto;
      max-width: 80%;
      background: white;
      color: #333;
      border: 1px solid #dee2e6;
      
      .message-avatar {
        background: #28a745;
        color: white;
      }
    }
  }
}

.message-content {
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  font-size: 0.9rem;
}

.message-info {
  display: flex;
  flex-direction: column;
}

.message-sender {
  font-weight: 600;
  font-size: 0.9rem;
}

.message-time {
  font-size: 0.75rem;
  opacity: 0.7;
}

.message-text {
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  
  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6c757d;
    animation: typing 1.4s infinite ease-in-out;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input {
  background: white;
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
}

.conversation-type-selector {
  max-width: 300px;
}

.input-group {
  textarea {
    resize: none;
    border-radius: 1rem 0 0 1rem;
    
    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }
  
  button {
    border-radius: 0 1rem 1rem 0;
    min-width: 60px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .ai-chat-container {
    height: calc(100vh - 80px);
  }
  
  .chat-header {
    padding: 0.75rem;
    
    h4 {
      font-size: 1.1rem;
    }
    
    .btn-sm {
      font-size: 0.8rem;
      padding: 0.25rem 0.5rem;
    }
  }
  
  .messages-container {
    padding: 0.75rem;
  }
  
  .message {
    &.user-message .message-content,
    &.ai-message .message-content {
      max-width: 90%;
    }
  }
  
  .message-content {
    padding: 0.75rem;
  }
  
  .chat-input {
    padding: 0.75rem;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .ai-chat-container {
    background: #1a1a1a;
  }
  
  .chat-header {
    background: #2d2d2d;
    border-bottom-color: #404040;
    color: #e9ecef;
  }
  
  .messages-container {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }
  
  .ai-message .message-content {
    background: #2d2d2d;
    border-color: #404040;
    color: #e9ecef;
  }
  
  .chat-input {
    background: #2d2d2d;
    border-top-color: #404040;
  }
}
