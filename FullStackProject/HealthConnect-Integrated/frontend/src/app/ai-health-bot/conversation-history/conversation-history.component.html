<div class="conversation-history-container">
  <!-- Header -->
  <div class="history-header">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <div class="d-flex align-items-center">
        <i class="fas fa-history text-primary me-2"></i>
        <h4 class="mb-0">Conversation History</h4>
      </div>
      <div class="d-flex gap-2">
        <button 
          class="btn btn-primary btn-sm" 
          (click)="startNewConversation()">
          <i class="fas fa-plus me-1"></i>
          New Chat
        </button>
        <button 
          class="btn btn-outline-secondary btn-sm" 
          (click)="refreshConversations()"
          [disabled]="isLoading">
          <i class="fas fa-sync-alt me-1" [class.fa-spin]="isLoading"></i>
          Refresh
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
      <div class="row g-3">
        <div class="col-md-6">
          <div class="input-group">
            <span class="input-group-text">
              <i class="fas fa-search"></i>
            </span>
            <input 
              type="text" 
              class="form-control" 
              placeholder="Search conversations..."
              [(ngModel)]="searchTerm"
              (input)="onSearchChange()">
            <button 
              class="btn btn-outline-secondary" 
              type="button"
              (click)="clearSearch()"
              *ngIf="searchTerm">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        <div class="col-md-4">
          <select 
            class="form-select" 
            [(ngModel)]="selectedType"
            (change)="onTypeFilterChange()">
            <option value="ALL">All Types</option>
            <option *ngFor="let type of conversationTypes" [value]="type">
              {{getConversationTypeDisplayName(type)}}
            </option>
          </select>
        </div>
        <div class="col-md-2">
          <div class="text-muted small">
            {{filteredConversations.length}} conversation{{filteredConversations.length !== 1 ? 's' : ''}}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="history-content">
    <!-- Loading State -->
    <div class="text-center py-5" *ngIf="isLoading">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2 text-muted">Loading conversation history...</p>
    </div>

    <!-- Error State -->
    <div class="alert alert-danger" *ngIf="error">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{error}}
    </div>

    <!-- Empty State -->
    <div class="empty-state text-center py-5" *ngIf="!isLoading && filteredConversations.length === 0 && !error">
      <i class="fas fa-comments fa-3x text-muted mb-3"></i>
      <h5 class="text-muted" *ngIf="conversations.length === 0">No conversations yet</h5>
      <h5 class="text-muted" *ngIf="conversations.length > 0">No conversations match your filters</h5>
      <p class="text-muted" *ngIf="conversations.length === 0">
        Start your first conversation with the AI Health Assistant
      </p>
      <button 
        class="btn btn-primary" 
        (click)="startNewConversation()"
        *ngIf="conversations.length === 0">
        <i class="fas fa-plus me-2"></i>
        Start First Conversation
      </button>
      <button 
        class="btn btn-outline-secondary" 
        (click)="clearSearch(); selectedType = 'ALL'; onTypeFilterChange()"
        *ngIf="conversations.length > 0">
        <i class="fas fa-filter me-2"></i>
        Clear Filters
      </button>
    </div>

    <!-- Conversations List -->
    <div class="conversations-list" *ngIf="!isLoading && filteredConversations.length > 0">
      <div 
        class="conversation-card" 
        *ngFor="let conversation of filteredConversations"
        (click)="openConversation(conversation)">
        
        <div class="conversation-header">
          <div class="d-flex align-items-center">
            <div class="conversation-type-icon">
              <i class="fas fa-{{getConversationTypeIcon(conversation.conversationType)}}"></i>
            </div>
            <div class="conversation-title">
              <h6 class="mb-0">{{conversation.title}}</h6>
              <small class="text-muted">
                {{getConversationTypeDisplayName(conversation.conversationType)}}
              </small>
            </div>
          </div>
          <div class="conversation-meta">
            <div class="conversation-date">
              <small class="text-muted">{{formatDate(conversation.updatedAt)}}</small>
              <small class="text-muted d-block">{{formatTime(conversation.updatedAt)}}</small>
            </div>
          </div>
        </div>

        <div class="conversation-preview">
          <p class="mb-2">{{getConversationPreview(conversation)}}</p>
        </div>

        <div class="conversation-footer">
          <div class="d-flex justify-content-between align-items-center">
            <div class="conversation-stats">
              <span class="badge bg-light text-dark me-2">
                <i class="fas fa-comments me-1"></i>
                {{conversation.messageCount}} message{{conversation.messageCount !== 1 ? 's' : ''}}
              </span>
              <span class="badge bg-success" *ngIf="conversation.isSharedWithDoctor">
                <i class="fas fa-share me-1"></i>
                Shared
              </span>
            </div>
            <div class="conversation-actions">
              <i class="fas fa-chevron-right text-muted"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
