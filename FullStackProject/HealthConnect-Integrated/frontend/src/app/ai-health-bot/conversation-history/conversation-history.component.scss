.conversation-history-container {
  padding: 1.5rem;
  background: #f8f9fa;
  min-height: calc(100vh - 120px);
}

.history-header {
  background: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 1.5rem;
}

.filters-section {
  border-top: 1px solid #dee2e6;
  padding-top: 1rem;
  margin-top: 1rem;
}

.history-content {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  min-height: 400px;
}

.conversations-list {
  padding: 1rem;
}

.conversation-card {
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;

  &:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
    transform: translateY(-2px);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.conversation-header {
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.conversation-type-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.conversation-title {
  flex: 1;
  
  h6 {
    color: #333;
    font-weight: 600;
    line-height: 1.2;
  }
}

.conversation-meta {
  text-align: right;
  flex-shrink: 0;
}

.conversation-date {
  line-height: 1.2;
}

.conversation-preview {
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 0.75rem;
  
  p {
    margin-bottom: 0;
  }
}

.conversation-footer {
  border-top: 1px solid #f8f9fa;
  padding-top: 0.75rem;
}

.conversation-stats {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.conversation-actions {
  display: flex;
  align-items: center;
}

.empty-state {
  padding: 3rem 1rem;
}

// Responsive design
@media (max-width: 768px) {
  .conversation-history-container {
    padding: 1rem;
  }
  
  .history-header {
    padding: 1rem;
  }
  
  .filters-section {
    .row {
      --bs-gutter-x: 0.75rem;
    }
  }
  
  .conversation-card {
    padding: 0.75rem;
  }
  
  .conversation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .conversation-meta {
    text-align: left;
    width: 100%;
  }
  
  .conversation-type-icon {
    width: 32px;
    height: 32px;
    margin-right: 0.5rem;
  }
  
  .conversation-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

// Loading animation
.fa-spin {
  animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Badge styles
.badge {
  font-size: 0.75rem;
  
  &.bg-light {
    color: #495057 !important;
  }
}

// Search input focus
.input-group {
  .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .conversation-history-container {
    background: #1a1a1a;
  }
  
  .history-header,
  .history-content,
  .conversation-card {
    background: #2d2d2d;
    border-color: #404040;
    color: #e9ecef;
  }
  
  .conversation-card:hover {
    border-color: #0d6efd;
    background: #343a40;
  }
  
  .conversation-title h6 {
    color: #e9ecef;
  }
  
  .conversation-preview {
    color: #adb5bd;
  }
  
  .conversation-footer {
    border-top-color: #404040;
  }
  
  .badge.bg-light {
    background-color: #495057 !important;
    color: #e9ecef !important;
  }
}
