<div class="container-fluid py-4">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-3 text-muted">Loading your dashboard...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !isLoading" class="alert alert-danger" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ error }}
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!isLoading && !error">
    <!-- Welcome Header -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h1 class="h3 mb-1">{{ getGreeting() }}, {{ currentUser?.fullName }}!</h1>
            <p class="text-muted mb-0">Here's your health overview for today</p>
          </div>
          <button class="btn btn-outline-primary" (click)="refreshData()">
            <i class="bi bi-arrow-clockwise me-2"></i>Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Health Metrics -->
    <div class="row mb-4">
      <div class="col-12">
        <h5 class="mb-3">
          <i class="bi bi-heart-pulse me-2 text-primary"></i>Health Metrics
        </h5>
      </div>
      <div class="col-md-3 col-sm-6 mb-3" *ngFor="let metric of healthMetrics">
        <div class="card h-100">
          <div class="card-body text-center">
            <i class="bi bi-{{ metric.icon }} display-6 {{ metric.color }} mb-2"></i>
            <h6 class="card-title">{{ metric.name }}</h6>
            <h4 class="mb-2">{{ metric.value }} <small class="text-muted">{{ metric.unit }}</small></h4>
            <span [class]="getStatusBadgeClass(metric.status)">{{ metric.status | titlecase }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
      <div class="col-12">
        <h5 class="mb-3">
          <i class="bi bi-lightning me-2 text-primary"></i>Quick Actions
        </h5>
      </div>
      <div class="col-md-3 col-sm-6 mb-3" *ngFor="let action of quickActions">
        <div class="card h-100 action-card" (click)="navigateTo(action.route)">
          <div class="card-body text-center">
            <div class="rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                 [class]="action.color" style="width: 60px; height: 60px;">
              <i class="bi bi-{{ action.icon }} text-white fs-4"></i>
            </div>
            <h6 class="card-title">{{ action.title }}</h6>
            <p class="card-text text-muted small">{{ action.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Upcoming Appointments -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
              <i class="bi bi-calendar-check me-2"></i>Upcoming Appointments
            </h6>
            <button class="btn btn-sm btn-outline-primary" (click)="navigateTo('/appointments')">
              View All
            </button>
          </div>
          <div class="card-body">
            <div *ngIf="upcomingAppointments.length === 0" class="text-center py-4 text-muted">
              <i class="bi bi-calendar-x display-6 mb-3"></i>
              <p>No upcoming appointments</p>
              <button class="btn btn-primary" (click)="navigateTo('/appointments/book')">
                <i class="bi bi-calendar-plus me-2"></i>Book Appointment
              </button>
            </div>

            <div *ngFor="let appointment of upcomingAppointments" class="appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded border">
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                  <div class="rounded-circle bg-light d-flex align-items-center justify-content-center"
                       style="width: 45px; height: 45px;">
                    <i class="bi bi-{{ appointment.type === 'VIDEO_CALL' ? 'camera-video' : 'geo-alt' }} text-primary"></i>
                  </div>
                </div>
                <div>
                  <h6 class="mb-1">Dr. {{ appointment.doctor.fullName }}</h6>
                  <p class="mb-1 text-muted small">
                    <i class="bi bi-calendar me-1"></i>{{ appointment.date }}
                    <i class="bi bi-clock ms-2 me-1"></i>{{ appointment.startTime }}
                  </p>
                  <span [class]="getStatusBadgeClass(appointment.status)">
                    {{ appointment.status | titlecase }}
                  </span>
                </div>
              </div>
              <div class="flex-shrink-0">
                <button
                  class="btn btn-sm btn-outline-primary"
                  (click)="navigateTo('/appointments/' + appointment.id)"
                >
                  <i class="bi bi-eye me-1"></i>View Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Messages Section -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
              <i class="bi bi-chat-dots me-2"></i>Recent Messages
            </h6>
            <button class="btn btn-sm btn-outline-primary" (click)="openChatModal()">
              <i class="bi bi-chat-plus me-1"></i>New Message
            </button>
          </div>
          <div class="card-body">
            <div *ngIf="recentChats.length === 0" class="text-center py-4 text-muted">
              <i class="bi bi-chat-square-text display-6 mb-3"></i>
              <p>No messages yet</p>
              <button class="btn btn-primary" (click)="openChatModal()">
                <i class="bi bi-chat-plus me-2"></i>Start Conversation
              </button>
            </div>

            <div *ngFor="let chat of recentChats" class="chat-preview d-flex align-items-center justify-content-between p-3 mb-2 rounded border">
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                  <img
                    [src]="chat.doctor.avatar || '/assets/images/default-avatar.png'"
                    [alt]="chat.doctor.fullName"
                    class="rounded-circle"
                    style="width: 45px; height: 45px; object-fit: cover;">
                </div>
                <div>
                  <h6 class="mb-1">Dr. {{ chat.doctor.fullName }}</h6>
                  <p class="mb-1 text-muted small" *ngIf="chat.lastMessage">
                    {{ chat.lastMessage.content | slice:0:50 }}{{ chat.lastMessage.content.length > 50 ? '...' : '' }}
                  </p>
                  <small class="text-muted" *ngIf="chat.lastMessage">
                    {{ formatChatTime(chat.lastMessage.createdAt) }}
                  </small>
                </div>
              </div>
              <div class="flex-shrink-0">
                <span *ngIf="chat.unreadCount > 0" class="badge bg-primary rounded-pill me-2">
                  {{ chat.unreadCount }}
                </span>
                <button
                  class="btn btn-sm btn-outline-primary"
                  (click)="openChat(chat)"
                >
                  <i class="bi bi-chat me-1"></i>Open
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activities & Health Tips -->
    <div class="row">
      <!-- Quick Chat Widget -->
      <div class="col-lg-4 mb-4">
        <app-quick-chat-widget></app-quick-chat-widget>
      </div>

      <!-- Recent Activities -->
      <div class="col-md-4 mb-4">
        <div class="card h-100">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="bi bi-clock-history me-2"></i>Recent Activities
            </h6>
          </div>
          <div class="card-body">
            <div class="activity-item d-flex align-items-start mb-3" *ngFor="let activity of recentActivities; last as isLast">
              <div class="flex-shrink-0 me-3">
                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" 
                     style="width: 40px; height: 40px;">
                  <i class="bi bi-{{ activity.icon }} {{ activity.color }}"></i>
                </div>
              </div>
              <div class="flex-grow-1">
                <h6 class="mb-1">{{ activity.title }}</h6>
                <p class="mb-1 text-muted small">{{ activity.description }}</p>
                <small class="text-muted">{{ activity.time }}</small>
              </div>
              <hr *ngIf="!isLast" class="my-3">
            </div>
          </div>
        </div>
      </div>

      <!-- Health Tips -->
      <div class="col-md-4 mb-4">
        <div class="card h-100">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="bi bi-lightbulb me-2"></i>Health Tips
            </h6>
          </div>
          <div class="card-body">
            <div class="tip-item d-flex align-items-start mb-3" *ngFor="let tip of healthTips; last as isLast">
              <div class="flex-shrink-0 me-3">
                <div class="rounded-circle bg-primary bg-opacity-10 d-flex align-items-center justify-content-center" 
                     style="width: 40px; height: 40px;">
                  <i class="bi bi-{{ tip.icon }} text-primary"></i>
                </div>
              </div>
              <div class="flex-grow-1">
                <h6 class="mb-1">{{ tip.title }}</h6>
                <p class="mb-0 text-muted small">{{ tip.description }}</p>
              </div>
              <hr *ngIf="!isLast" class="my-3">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Emergency Contact -->
    <div class="row">
      <div class="col-12">
        <div class="alert alert-info d-flex align-items-center" role="alert">
          <i class="bi bi-info-circle me-3 fs-4"></i>
          <div>
            <h6 class="alert-heading mb-1">Emergency Contact</h6>
            <p class="mb-0">For medical emergencies, call <strong>911</strong> or visit your nearest emergency room.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Chat Modal -->
<div class="modal fade" id="chatModal" tabindex="-1" aria-labelledby="chatModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="chatModalLabel">
          <i class="bi bi-chat-dots me-2"></i>Messages
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body p-0" style="height: 600px;">
        <div class="row h-100 g-0">
          <div class="col-md-4 border-end">
            <app-chat-list (chatSelected)="onChatSelected($event)"></app-chat-list>
          </div>
          <div class="col-md-8">
            <app-chat-window #chatWindow></app-chat-window>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
